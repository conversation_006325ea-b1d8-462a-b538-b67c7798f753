import { request } from '@/service/request'

/**
 * AI_生成图片
 * @param data 请求参数
 */
export async function fetchAIGenerateImage(
  data: {
    /**
     * AI生成图片接口入参
     */
    Prompt: string
  },
) {
  const { ...reset } = data
  return request<string>(
    {
      url: 'AgentCommon/AgentCommon/AIGenerateImage',
      method: 'POST',
      data: reset,
      signal: new AbortController().signal,
    },
  )
}
