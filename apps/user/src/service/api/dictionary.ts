import { appRequest } from '@/service/request'
/**
 * 获取学生学科列表
 * @param data 请求参数，类型为Dictionary.GetSubjectListByStudentRequest
 * @returns 返回Promise，包含学科列表响应数据，类型为Dictionary.GetSubjectListByStudentResponse数组
 */
export function fetchGetSubjectListByStudentResponse(data: Dictionary.GetSubjectListByStudentRequest) {
  return appRequest<Dictionary.GetSubjectListByStudentResponse[]>({
    url: 'Period_Subject/Period_Subject/GetSubjectListByStudent',
    method: 'POST',
    data,
  })
}
