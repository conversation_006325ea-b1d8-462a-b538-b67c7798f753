<script setup lang="ts">
import dayjs from 'dayjs'

defineOptions({
  name: 'SimpleAgentCard',
})

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
})

const emit = defineEmits<Emits>()

type Agent = AgentApi.OralCommunicationListOutput

interface Props {
  /** Agent数据 */
  agent: Agent
  /** 卡片尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 卡片索引 */
  index?: number
}

interface Emits {
  /** 点击卡片 */
  (e: 'click', agent: Agent): void

  /** 切换收藏状态 */
  (e: 'toggleStar', agent: Agent): void

  /** 立即使用 */
  (e: 'useNow', agent: Agent): void

  /** 编辑 */
  (e: 'edit', agent: Agent): void

  /** 删除 */
  (e: 'delete', agent: Agent): void

  /** 查看统计 */
  (e: 'viewStats', agent: Agent): void
}

// 处理编辑
function handleEdit(event: Event) {
  event.stopPropagation()
  emit('edit', props.agent)
}

// 处理删除
function handlePositiveClick(event: Event) {
  event.stopPropagation()
  emit('delete', props.agent)
}

// 处理查看统计
function handleViewStats(event: Event) {
  event.stopPropagation()
  emit('viewStats', props.agent)
}
</script>

<template>
  <NCard
    hoverable
    class="group simple-agent-card min-h-160px cursor-pointer card-wrapper transition-all duration-300 hover:translate-y-[-2px] hover:shadow-lg"
  >
    <!-- 卡片内容容器 -->
    <div class="h-full flex flex-col">
      <!-- 头像和标题区域 -->
      <div class="mb-12px flex items-start gap-12px">
        <!-- 头像 -->
        <div class="flex-shrink-0">
          <img
            :src="agent.Logo"
            :alt="agent.Name"
            class="size-52px rounded-8px object-cover"
          >
        </div>

        <!-- 标题和描述 -->
        <div class="min-w-0 flex-1">
          <h3
            class="line-clamp-1 mb-8px text-14px text-gray-900 font-semibold dark:text-gray-100"
          >
            {{ agent.Name }}
          </h3>
          <p
            class="line-clamp-3 w-fit bg-[#F2F3F7] px-8px py-4px text-12px text-[#707689] leading-16px dark:text-gray-300"
          >
            {{
              {
                1: "指令式",
                2: "对话式",
                3: "辩论式",
              }[agent.InteractiveMode] ?? "-"
            }}
          </p>
        </div>
      </div>

      <div class="w-full">
        <p class="text-14px text-[#605F69]">
          {{ agent.Introduce }}
        </p>

        <div class="mt-16px flex flex-wrap gap-x-48px gap-y-12px">
          <p class="whitespace-nowrap text-14px text-[#605F69]">
            发布班级：{{ agent.ClassName }}
          </p>
          <p class="whitespace-nowrap text-14px text-[#605F69]">
            教材章节 ：{{ agent.ChapterName }}
          </p>
          <p class="whitespace-nowrap text-14px text-[#605F69]">
            任务周期：{{ agent.BeginTime }} - {{ agent.EndTime }}
          </p>
        </div>
      </div>

      <!-- 弹性空间 -->
      <div class="flex-1" />

      <!-- 底部操作区域 -->
      <div
        class="mt-16px flex items-center justify-end gap-8px transition-all duration-600"
      >
        <NButton
          v-if="dayjs(agent.EndTime).isAfter(dayjs())"
          text
          type="primary"
          size="tiny"
          @click="handleEdit"
        >
          编辑
        </NButton>
        <NButton text type="primary" size="tiny" @click="handlePositiveClick">
          删除
        </NButton>

        <NButton text type="primary" size="tiny" @click="handleViewStats">
          查看统计
        </NButton>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
/* 动画样式 */
@keyframes animation-translateY {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 初始状态 */
[class*="animation"] {
  opacity: 0;
}
</style>
