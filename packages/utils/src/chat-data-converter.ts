import type Chat from '../../../typings/chat'

/**
 * 聊天数据转换工具
 * 用于将接口返回的历史对话数据转换为聊天组件所需的消息格式
 */
interface AgentTeacherHomePageAgentTaskOuput {
  /**
   * 会话Id
   */
  Id: string
  /**
   * 问
   */
  Ask: string
  AskInfo: AgentModelAIAIDialogueASKDto
  /**
   * 答
   */
  Answer: string
  /**
   * 消息类型(1系统消息、2用户消息)
   */
  MessageType: 1 | 2
  /**
   * 创建时间
   */
  CreateTime: string
}
interface AgentModelAIAIDialogueASKDto {
  /**
   * “问”文本
   */
  AskText?: null | string
  AudioFile?: AgentModelAIAIDialogueASKAudioFileInfo | null
  /**
   * 文件消息
   */
  Files?: AgentModelAIAIDialogueASKFileInfo[] | null
}
interface AgentModelAIAIDialogueASKFileInfo {
  /**
   * 文件名称
   */
  FileName: string
  /**
   * 文件大小
   */
  FileSize: string
  /**
   * 文件类型
   */
  FileType: string
  /**
   * 文件地址
   */
  FileUrl: string
}
interface AgentModelAIAIDialogueASKAudioFileInfo {
  /**
   * 语音地址
   */
  AudioUrl: string
  /**
   * 时长（单位:秒）
   */
  Duration: number
}

/**
 * 历史对话数据转换参数接口
 */
export interface ConvertHistoryDataParams {
  /** 接口返回的历史对话数据数组 */
  historyData: AgentTeacherHomePageAgentTaskOuput[]
  /** 用户信息 */
  userInfo: {
    /** 用户名称 */
    userName: string
    /** 用户头像 */
    userAvatar: string
  }
  aiInfo: {
    /** AI名称 */
    aiName: string
    /** AI头像 */
    aiAvatar: string
  }
}

/**
 * 消息创建配置常量
 */
const MESSAGE_CONFIG = {
  /** 默认音频格式 */
  DEFAULT_AUDIO_MIME_TYPE: 'audio/wav',
  /** 用户消息位置 */
  USER_PLACEMENT: 'end' as const,
  /** AI消息位置 */
  AI_PLACEMENT: 'start' as const,
  /** 用户角色 */
  USER_ROLE: 'user' as const,
  /** AI角色 */
  AI_ROLE: 'ai' as const,
  /** 文本内容类型 */
  TEXT_CONTENT_TYPE: 'text' as const,
  /** 音频内容类型 */
  AUDIO_CONTENT_TYPE: 'audio' as const,
} as const

/**
 * 创建用户消息基础属性
 */
function createUserMessageBase(userInfo: ConvertHistoryDataParams['userInfo']) {
  return {
    role: MESSAGE_CONFIG.USER_ROLE,
    avatar: userInfo.userAvatar,
    placement: MESSAGE_CONFIG.USER_PLACEMENT,
    name: userInfo.userName,
  } as const
}

/**
 * 创建AI消息基础属性
 */
function createAiMessageBase(aiInfo: ConvertHistoryDataParams['aiInfo']) {
  return {
    role: MESSAGE_CONFIG.AI_ROLE,
    avatar: aiInfo.aiAvatar,
    placement: MESSAGE_CONFIG.AI_PLACEMENT,
    name: aiInfo.aiName,
    contentType: MESSAGE_CONFIG.TEXT_CONTENT_TYPE,
  } as const
}

/**
 * 创建用户音频消息
 */
function createUserAudioMessage(
  item: AgentTeacherHomePageAgentTaskOuput,
  userInfo: ConvertHistoryDataParams['userInfo'],
): Chat.MessageItem | null {
  const audioFile = item.AskInfo.AudioFile
  if (!audioFile?.AudioUrl) {
    return null
  }

  return {
    ...createUserMessageBase(userInfo),
    key: item.Id,
    contentType: MESSAGE_CONFIG.AUDIO_CONTENT_TYPE,
    audioData: {
      audioUrl: audioFile.AudioUrl,
      duration: audioFile.Duration || 0,
      mimeType: MESSAGE_CONFIG.DEFAULT_AUDIO_MIME_TYPE,
    },
  }
}

/**
 * 创建用户文本消息
 */
function createUserTextMessage(
  item: AgentTeacherHomePageAgentTaskOuput,
  userInfo: ConvertHistoryDataParams['userInfo'],
): Chat.MessageItem | null {
  const askText = item.AskInfo.AskText
  if (!askText?.trim()) {
    return null
  }

  return {
    ...createUserMessageBase(userInfo),
    key: item.Id,
    contentType: MESSAGE_CONFIG.TEXT_CONTENT_TYPE,
    content: askText,
  }
}

/**
 * 创建AI回复消息
 */
function createAiMessage(
  item: AgentTeacherHomePageAgentTaskOuput,
  aiInfo: ConvertHistoryDataParams['aiInfo'],
): Chat.MessageItem | null {
  if (!item.Answer?.trim()) {
    return null
  }

  return {
    ...createAiMessageBase(aiInfo),
    key: `${item.Id}-ai`,
    content: item.Answer,
  }
}

/**
 * 处理单个历史对话项，返回对应的消息数组
 */
function processHistoryItem(
  item: AgentTeacherHomePageAgentTaskOuput,
  userInfo: ConvertHistoryDataParams['userInfo'],
  aiInfo: ConvertHistoryDataParams['aiInfo'],
): Chat.MessageItem[] {
  const messages: Chat.MessageItem[] = []

  // 优先处理音频消息，其次处理文本消息（互斥关系）
  const userMessage = createUserAudioMessage(item, userInfo) || createUserTextMessage(item, userInfo)
  if (userMessage) {
    messages.push(userMessage)
  }

  // 处理AI回复消息
  const aiMessage = createAiMessage(item, aiInfo)
  if (aiMessage) {
    messages.push(aiMessage)
  }

  return messages
}

/**
 * 将接口返回的历史对话数据转换为组件所需的消息格式
 * @param params 转换参数对象
 * @returns 转换后的消息数组
 */
export function convertHistoryDataToMessages(params: ConvertHistoryDataParams): Chat.MessageItem[] {
  const { historyData, userInfo, aiInfo } = params

  // 参数验证
  if (!Array.isArray(historyData)) {
    console.warn('convertHistoryDataToMessages: historyData is not an array')
    return []
  }

  // 使用 flatMap 进行函数式转换，性能更好且代码更简洁
  return historyData.flatMap(item => processHistoryItem(item, userInfo, aiInfo))
}
