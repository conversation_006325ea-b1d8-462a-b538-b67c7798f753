<script setup lang="ts">
import { computed } from 'vue'
import type { Component } from 'vue'
import type { PageTabMode, PageTabProps } from '../../types'
import { ACTIVE_COLOR, createTabCssVars } from './shared'
import ChromeTab from './chrome-tab.vue'
import ButtonTab from './button-tab.vue'
import SvgClose from './svg-close.vue'
import style from './index.module.css'

defineOptions({
  name: 'PageTab',
})
// 定义props并设置默认值
const props = withDefaults(defineProps<PageTabProps>(), {
  mode: 'chrome', // 默认使用chrome样式
  commonClass: 'transition-all-300', // 默认过渡动画类
  activeColor: ACTIVE_COLOR, // 默认激活颜色
  closable: true, // 默认显示关闭按钮
})
// 定义emit事件
const emit = defineEmits<Emits>()

interface Emits {
  (e: 'close'): void// 关闭事件
}
// 计算当前激活的标签页组件
const activeTabComponent = computed(() => {
  const { mode, chromeClass, buttonClass } = props

  // 标签页组件映射表
  const tabComponentMap = {
    chrome: {
      component: ChromeTab, // chrome样式组件
      class: chromeClass, // chrome样式类
    },
    button: {
      component: ButtonTab, // button样式组件
      class: buttonClass, // button样式类
    },
  } satisfies Record<PageTabMode, { component: Component, class?: string }>

  return tabComponentMap[mode]
})

// 计算CSS变量
const cssVars = computed(() => createTabCssVars(props.activeColor))

// 计算需要绑定的props
const bindProps = computed(() => {
  const { chromeClass: _chromeCls, buttonClass: _btnCls, ...rest } = props

  return rest
})

// 关闭按钮点击处理
function handleClose() {
  emit('close')
}
</script>

<template>
  <component :is="activeTabComponent.component" :class="activeTabComponent.class" :style="cssVars" v-bind="bindProps">
    <template #prefix>
      <slot name="prefix" />
    </template>
    <slot />
    <template #suffix>
      <slot name="suffix">
        <SvgClose v-if="closable" :class="[style['svg-close']]" @click.stop="handleClose" />
      </slot>
    </template>
  </component>
</template>

<style scoped></style>
