<script setup lang="ts">
defineOptions({
  name: 'ChromeTabBg',
})
</script>

<template>
  <!-- Chrome浏览器样式的标签页背景 -->
  <svg class="size-full">
    <defs>
      <!-- 定义左侧几何形状 -->
      <symbol id="geometry-left" viewBox="0 0 214 36">
        <path d="M17 0h197v36H0v-2c4.5 0 9-3.5 9-8V8c0-4.5 3.5-8 8-8z" />
      </symbol>
      <!-- 定义右侧几何形状(复用左侧形状) -->
      <symbol id="geometry-right" viewBox="0 0 214 36">
        <use xlink:href="#geometry-left" />
      </symbol>
      <!-- 定义裁剪路径 -->
      <clipPath>
        <rect width="100%" height="100%" x="0" />
      </clipPath>
    </defs>
    <!-- 左侧部分(占51%宽度) -->
    <svg width="51%" height="100%">
      <use xlink:href="#geometry-left" width="214" height="36" fill="currentColor" />
    </svg>
    <!-- 右侧部分(水平翻转左侧部分) -->
    <g transform="scale(-1, 1)">
      <svg width="51%" height="100%" x="-100%" y="0">
        <use xlink:href="#geometry-right" width="214" height="36" fill="currentColor" />
      </svg>
    </g>
  </svg>
</template>

<style scoped></style>
