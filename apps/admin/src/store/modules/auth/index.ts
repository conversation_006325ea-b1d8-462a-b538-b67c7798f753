import { computed, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { defineStore } from 'pinia'
import { useLoading, useRouterPush } from '@sa/hooks'
import { localStg } from '@sa/utils'
import { SetupStoreId } from '@sa/enum'
import { useRouteStore } from '../route'
import { useTabStore } from '../tab'
import { clearAuthStorage, getToken } from './shared'
import { fetchGetGradeList, fetchGetNowYearSemesterAndList, fetchGetTeachClass, fetchGetUserInfo, fetchLogin, fetchSubjectListByUser } from '@/service/api'

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute()
  const routeStore = useRouteStore()
  const tabStore = useTabStore()
  const { toLogin, redirectFromLogin } = useRouterPush(false)
  const { loading: loginLoading, startLoading, endLoading } = useLoading()

  const token = ref(getToken())

  // 选中的角色和学科和年级
  const activeState = ref({
    roleId: '',
    subjectId: '',
    grade: '',
  })
  // 获取当前学年学期
  const yearSemester: AgentApi.YearSemesterResponse = reactive({
    Years: [],
    NowTerm: null,
    NowYear: null,
  })
  const gradeList = ref<AgentApi.GradeResponse[]>([])
  // 获取当前用户信息
  const userInfo: AuthApi.UserInfo = reactive({
    userId: '',
    userName: '',
    roles: [],
    buttons: [],
    RoleIdList: [],
    subjectList: [],
    schoolId: '',
    classList: [],
  })

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE)
  })

  /** Is login */
  const isLogin = computed(() => Boolean(token.value))

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore()

    clearAuthStorage()

    authStore.$reset()

    if (!route.meta.constant) {
      await toLogin()
    }

    tabStore.cacheTabs()
    routeStore.resetStore()
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param [redirect] Whether to redirect after login. Default is `true`
   */
  async function login(userName: string, password: string, redirect = true) {
    startLoading()

    const { data: loginToken, error } = await fetchLogin(userName, password)

    if (!error) {
      const pass = await loginByToken(loginToken)

      if (pass) {
        await redirectFromLogin(redirect)

        window.$notification?.success({
          title: '登录成功',
          content: `欢迎回来，${userInfo.userName} !`,
          duration: 4500,
        })
      }
    }
    else {
      resetStore()
    }

    endLoading()
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token)
    localStg.set('refreshToken', loginToken.refreshToken || '')

    // 2. get user info
    const pass = await getUserInfo()

    if (pass) {
      token.value = loginToken.token

      return true
    }

    return false
  }

  async function getUserInfo() {
    const { data: info, error } = await fetchGetUserInfo()

    if (!error) {
      const subjectRes = await fetchSubjectListByUser(info!.userId)
      const yearSemesterRes = await fetchGetNowYearSemesterAndList()
      const gradeRes = await fetchGetGradeList({
        year: yearSemesterRes.data?.NowYear?.toString() || '',
      })

      gradeList.value = gradeRes ?? []
      // 设置学科
      info.subjectList = subjectRes.data ?? []

      // 设置学年学期
      Object.assign(yearSemester, yearSemesterRes.data)

      // update store
      Object.assign(userInfo, info)

      setDefaultRoleOrSubject()

      const classRes = await fetchGetTeachClass({
        year: yearSemesterRes.data!.NowYear?.toString() || '',
        grade: activeState.value.grade,
        UserId: userInfo.userId,
      })

      // 设置班级列表
      userInfo.classList = classRes.data ?? []

      return true
    }

    return false
  }

  function setDefaultRoleOrSubject() {
    let stgRole = localStg.get('defaultRoleId')
    let stgSubject = localStg.get('defaultSubjectId')
    let stgGrade = localStg.get('defaultGradeId')
    if (!stgRole) {
      stgRole = userInfo.RoleIdList[0]?.RoleId
      localStg.set('defaultRoleId', stgRole)
    }
    else {
      // 1、列表是否为空，为空则清楚本地的数据 2、是否存在在列表中，存在不处理，不存在就设置为第一个
      if (!userInfo.RoleIdList.length) {
        localStg.remove('defaultRoleId')
        stgRole = ''
      }
      else if (!userInfo.RoleIdList.find(v => v.RoleId === stgRole)) {
        stgRole = userInfo.RoleIdList[0]?.RoleId
        localStg.set('defaultRoleId', stgRole)
      }
    }
    if (!stgSubject) {
      stgSubject = userInfo.subjectList[0]?.Id
      localStg.set('defaultSubjectId', stgSubject)
    }
    else {
      // 1、列表是否为空，为空则清楚本地的数据 2、是否存在在列表中，存在不处理，不存在就设置为第一个
      if (!userInfo.subjectList.length) {
        localStg.remove('defaultSubjectId')
        stgSubject = ''
      }
      else if (!userInfo.subjectList.find(v => v.Id === stgSubject)) {
        stgSubject = userInfo.subjectList[0]?.Id
        localStg.set('defaultSubjectId', stgSubject)
      }
    }
    if (!stgGrade) {
      stgGrade = `${gradeList.value[0]?.Id}`
      localStg.set('defaultGradeId', stgGrade)
    }
    else {
      // 1、列表是否为空，为空则清楚本地的数据 2、是否存在在列表中，存在不处理，不存在就设置为第一个
      if (!gradeList.value.length) {
        localStg.remove('defaultGradeId')
        stgGrade = ''
      }
      else if (!gradeList.value.find(v => v.Id === stgGrade)) {
        stgGrade = `${gradeList.value[0]?.Id}`
        localStg.set('defaultSubjectId', stgGrade)
      }
    }
    activeState.value.roleId = stgRole
    activeState.value.subjectId = stgSubject
    activeState.value.grade = stgGrade
  }

  async function initUserInfo() {
    const hasToken = getToken()

    if (hasToken) {
      const pass = await getUserInfo()

      if (!pass) {
        resetStore()
      }
    }
  }

  return {
    token,
    userInfo,
    yearSemester,
    gradeList,
    isStaticSuper,
    isLogin,
    loginLoading,
    resetStore,
    login,
    initUserInfo,
    loginByToken,
    activeState,
  }
})
