/**
 * PCM音频播放器
 * 支持实时PCM音频数据播放，具有音量控制、暂停/恢复等功能
 */

// 输入编码类型及其对应的转换值
const INPUT_CODECS = {
  Int8: 128,
  Int16: 32768,
  Int32: 2147483648,
  Float32: 1,
} as const

// TypedArray类型映射
const TYPED_ARRAYS = {
  Int8: Int8Array,
  Int16: Int16Array,
  Int32: Int32Array,
  Float32: Float32Array,
} as const

// 支持的输入编码类型
export type InputCodec = keyof typeof INPUT_CODECS

// FFT大小类型
export type FFTSize = 32 | 64 | 128 | 256 | 512 | 1024 | 2048 | 4096 | 8192 | 16384 | 32768

// PCM播放器配置选项
export interface PCMPlayerOptions {
  /** 输入数据编码格式，默认Int16 */
  inputCodec?: InputCodec
  /** 声道数，默认1 */
  channels?: number
  /** 采样率，默认8000Hz */
  sampleRate?: number
  /** 缓存刷新时间，默认1000ms */
  flushTime?: number
  /** 分析器FFT大小，默认2048 */
  fftSize?: FFTSize
  /** 音频上下文状态变化回调 */
  onstatechange?: (audioContext: AudioContext, event: Event, state: AudioContextState) => void
  /** 音频播放结束回调 */
  onended?: (bufferSource: AudioBufferSourceNode, event: Event) => void
}

// 默认配置
const DEFAULT_OPTIONS: Required<PCMPlayerOptions> = {
  inputCodec: 'Int16',
  channels: 1,
  sampleRate: 8000,
  flushTime: 1000,
  fftSize: 2048,
  onstatechange: () => {},
  onended: () => {},
}

/**
 * PCM音频播放器类
 */
class PCMPlayer {
  // 公共属性
  public audioCtx!: AudioContext
  public gainNode!: GainNode
  public analyserNode!: AnalyserNode

  // 私有属性
  private options!: Required<PCMPlayerOptions>
  private samples!: Float32Array
  private interval!: NodeJS.Timeout
  private convertValue!: number
  private typedArray!: new (buffer: ArrayBuffer) => ArrayLike<number>
  private startTime!: number
  private activeBufferSources!: Set<AudioBufferSourceNode>
  private hasEverPlayedAudio!: boolean

  constructor(options: PCMPlayerOptions = {}) {
    this.init(options)
  }

  /**
   * 初始化播放器
   */
  private init(options: PCMPlayerOptions): void {
    // 合并默认配置和用户配置
    this.options = { ...DEFAULT_OPTIONS, ...options }

    // 初始化样本存储区域
    this.samples = new Float32Array()

    // 设置定时刷新
    this.interval = setInterval(() => this.flush(), this.options.flushTime)

    // 获取转换值和类型数组构造函数
    this.convertValue = this.getConvertValue()
    this.typedArray = this.getTypedArray()

    // 初始化音频上下文
    this.initAudioContext()

    // 绑定音频上下文事件
    this.bindAudioContextEvent()

    // 初始化活动buffer sources集合，用于跟踪所有正在播放的音频段
    this.activeBufferSources = new Set()

    // 初始化音频播放标志
    this.hasEverPlayedAudio = false
  }

  /**
   * 获取转换值
   * 根据输入编码类型返回对应的转换基数
   */
  private getConvertValue(): number {
    const codec = this.options.inputCodec
    if (!(codec in INPUT_CODECS)) {
      throw new Error(`Unsupported codec: ${codec}. Supported codecs: ${Object.keys(INPUT_CODECS).join(', ')}`)
    }
    return INPUT_CODECS[codec]
  }

  /**
   * 获取TypedArray构造函数
   * 根据输入编码类型返回对应的TypedArray构造函数
   */
  private getTypedArray(): new (buffer: ArrayBuffer) => ArrayLike<number> {
    const codec = this.options.inputCodec
    if (!(codec in TYPED_ARRAYS)) {
      throw new Error(`Unsupported codec: ${codec}. Supported codecs: ${Object.keys(TYPED_ARRAYS).join(', ')}`)
    }
    return TYPED_ARRAYS[codec] as new (buffer: ArrayBuffer) => ArrayLike<number>
  }

  /**
   * 初始化音频上下文
   */
  private initAudioContext(): void {
    // 创建音频上下文，兼容旧版浏览器
    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
    if (!AudioContextClass) {
      throw new Error('Web Audio API is not supported in this browser')
    }

    this.audioCtx = new AudioContextClass()

    // 创建音量控制节点
    this.gainNode = this.audioCtx.createGain()
    this.gainNode.gain.value = 0.1
    this.gainNode.connect(this.audioCtx.destination)

    // 创建分析器节点
    this.analyserNode = this.audioCtx.createAnalyser()
    this.analyserNode.fftSize = this.options.fftSize

    // 设置开始时间
    this.startTime = this.audioCtx.currentTime
  }

  /**
   * 检测数据是否为TypedArray或ArrayBuffer类型
   */
  static isTypedArray(data: any): data is ArrayBuffer | ArrayBufferView {
    return (data.byteLength !== undefined && data.buffer && data.buffer.constructor === ArrayBuffer)
      || data.constructor === ArrayBuffer
  }

  /**
   * 检查数据类型是否支持
   */
  private isSupported(data: ArrayBuffer | ArrayBufferView): boolean {
    if (!PCMPlayer.isTypedArray(data)) {
      throw new TypeError('Data must be ArrayBuffer or TypedArray')
    }
    return true
  }

  /**
   * 向播放器输入PCM数据
   */
  public feed(data: ArrayBuffer | ArrayBufferView): void {
    this.isSupported(data)

    // 获取格式化后的Float32Array数据
    const formattedData = this.getFormattedValue(data)

    // 创建新的缓冲区来合并历史数据和新数据
    const newSamples = new Float32Array(this.samples.length + formattedData.length)

    // 复制历史数据
    newSamples.set(this.samples, 0)

    // 复制新数据
    newSamples.set(formattedData, this.samples.length)

    // 更新样本数据
    this.samples = newSamples
  }

  /**
   * 将输入数据格式化为Float32Array
   */
  private getFormattedValue(data: ArrayBuffer | ArrayBufferView): Float32Array {
    let typedData: ArrayLike<number>

    if (data.constructor === ArrayBuffer) {
      // eslint-disable-next-line new-cap
      typedData = new this.typedArray(data as ArrayBuffer)
    }
    else {
      const buffer = (data as ArrayBufferView).buffer
      // 处理SharedArrayBuffer的情况
      const arrayBuffer = buffer instanceof ArrayBuffer
        ? buffer
        : new ArrayBuffer(buffer.byteLength)
      // eslint-disable-next-line new-cap
      typedData = new this.typedArray(arrayBuffer)
    }

    const float32 = new Float32Array(typedData.length)

    // 将数据转换为-1到+1范围的IEEE754 32位线性PCM格式
    for (let i = 0; i < typedData.length; i++) {
      float32[i] = typedData[i] / this.convertValue
    }

    return float32
  }

  /**
   * 设置音量
   * @param volume 音量值，范围0-1
   */
  public volume(volume: number): void {
    if (volume < 0 || volume > 1) {
      throw new Error('Volume must be between 0 and 1')
    }
    this.gainNode.gain.value = volume
  }

  /**
   * 销毁播放器，释放资源
   */
  public destroy(): void {
    if (this.interval) {
      clearInterval(this.interval)
    }

    // 清理活动的buffer sources
    this.activeBufferSources.clear()

    // 重置音频播放标志
    this.hasEverPlayedAudio = false

    // 清理音频上下文
    if (this.audioCtx && this.audioCtx.state !== 'closed') {
      this.audioCtx.close()
    }

    // 清理引用
    this.samples = new Float32Array()
  }

  /**
   * 刷新缓冲区，播放音频数据
   */
  private flush(): void {
    if (!this.samples.length) {
      return
    }

    const bufferSource = this.audioCtx.createBufferSource()

    // 标记已经播放过音频
    this.hasEverPlayedAudio = true

    // 将新的buffer source添加到活动集合中
    this.activeBufferSources.add(bufferSource)

    // 设置播放结束回调
    if (this.options.onended) {
      bufferSource.onended = (event) => {
        // 从活动集合中移除已结束的buffer source
        this.activeBufferSources.delete(bufferSource)

        // 只有当所有buffer sources都结束且确实播放过音频时才触发onended回调
        if (this.activeBufferSources.size === 0 && this.hasEverPlayedAudio) {
          this.options.onended!(bufferSource, event)
        }
      }
    }

    const length = this.samples.length / this.options.channels
    const audioBuffer = this.audioCtx.createBuffer(
      this.options.channels,
      length,
      this.options.sampleRate,
    )

    // 处理每个声道的数据
    for (let channel = 0; channel < this.options.channels; channel++) {
      const audioData = audioBuffer.getChannelData(channel)
      let offset = channel
      let decrement = 50

      for (let i = 0; i < length; i++) {
        audioData[i] = this.samples[offset]

        // 淡入效果
        if (i < 50) {
          audioData[i] = (audioData[i] * i) / 50
        }

        // 淡出效果
        if (i >= length - 51) {
          audioData[i] = (audioData[i] * decrement--) / 50
        }

        offset += this.options.channels
      }
    }

    // 确保播放时间连续
    if (this.startTime < this.audioCtx.currentTime) {
      this.startTime = this.audioCtx.currentTime
    }

    // 连接音频节点并开始播放
    bufferSource.buffer = audioBuffer
    bufferSource.connect(this.gainNode)
    bufferSource.connect(this.analyserNode)
    bufferSource.start(this.startTime)

    this.startTime += audioBuffer.duration
    this.samples = new Float32Array()
  }

  /**
   * 暂停播放
   */
  public async pause(): Promise<void> {
    await this.audioCtx.suspend()
  }

  /**
   * 恢复播放
   */
  public async continue(): Promise<void> {
    await this.audioCtx.resume()
  }

  /**
   * 绑定音频上下文事件
   */
  private bindAudioContextEvent(): void {
    if (this.options.onstatechange) {
      this.audioCtx.onstatechange = (event) => {
        if (this.audioCtx && this.options.onstatechange) {
          this.options.onstatechange(this.audioCtx, event, this.audioCtx.state)
        }
      }
    }
  }

  /**
   * 获取音频频谱数据
   * @returns 频谱数据数组
   */
  public getFrequencyData(): Uint8Array {
    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount)
    this.analyserNode.getByteFrequencyData(dataArray)
    return dataArray
  }

  /**
   * 获取音频时域数据
   * @returns 时域数据数组
   */
  public getTimeDomainData(): Uint8Array {
    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount)
    this.analyserNode.getByteTimeDomainData(dataArray)
    return dataArray
  }

  /**
   * 获取当前音频上下文状态
   */
  public getState(): AudioContextState {
    return this.audioCtx.state
  }

  /**
   * 获取当前音量
   */
  public getVolume(): number {
    return this.gainNode.gain.value
  }

  /**
   * 检查是否有待播放的数据
   */
  public hasData(): boolean {
    return this.samples.length > 0
  }

  /**
   * 获取缓冲区中的样本数量
   */
  public getBufferLength(): number {
    return this.samples.length
  }

  /**
   * 清空缓冲区
   */
  public clearBuffer(): void {
    this.samples = new Float32Array()
  }

  /**
   * 检查是否还有音频段正在播放
   */
  public isPlaying(): boolean {
    return this.activeBufferSources.size > 0
  }

  /**
   * 获取当前正在播放的音频段数量
   */
  public getActiveSourcesCount(): number {
    return this.activeBufferSources.size
  }
}

// 导出常量
export { INPUT_CODECS, TYPED_ARRAYS }

// 默认导出
export default PCMPlayer

// 命名导出
export { PCMPlayer }
