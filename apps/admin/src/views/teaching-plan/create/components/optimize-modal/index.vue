<script setup lang="tsx">
import { computed, ref, watch } from 'vue'
import { useSse } from '@sa/hooks'
import { XMarkdown } from 'vue-element-plus-x'
import { copyToClipboard } from '@sa/utils'
import AiLogo from '@/assets/imgs/teaching-plan/ai-logo.png'
import { useAuthStore } from '@/store/modules/auth'

interface Emits {
  (e: 'replace', content: string): void

  (e: 'insert', content: string): void
}

interface Props {
  /**
   * 教案创建记录Id
   */
  recordId: string
  /**
   * 将要优化的原文
   */
  optimizeText: string
  /**
   * 操作
   */
  action: string
}

const props = defineProps<Props>()

const emit = defineEmits<Emits>()
const show = defineModel('show', { default: false })
const inputContent = ref('')

const records = ref<string[]>([])
const currentVersion = ref(0)

// 计算属性
const totalVersions = computed(() => records.value.length)
const currentContent = computed(() => records.value[currentVersion.value] || '')

const { userInfo } = useAuthStore()

const scrollbarRef = ref()

function toBottom() {
  requestAnimationFrame(() => {
    scrollbarRef.value!.scrollTo({ position: 'bottom' })
  })
}

const { start, stop, isRunning } = useSse()

// 监听弹窗显示状态，每次打开时清除记录
watch(show, (newShow) => {
  if (newShow) {
    // 自动开始生成
    createOptimize()
  }
  else {
    stop() // 弹窗关闭停止当前请求
  }
})

function handleOptimize() {
  if (!inputContent.value.trim()) {
    return
  }
  if (isRunning.value) {
    window.$message?.info('正在生成中,请耐心等待')
    return
  }
  createOptimize()
}

function handleRegenerate() {
  createOptimize() // 重新生成一个
}

function handleCopy() {
  copyToClipboard(currentContent.value as string)
}

function handlePrevVersion() {
  if (currentVersion.value <= 0) {
    return
  }
  currentVersion.value--
}

function handleNextVersion() {
  if (currentVersion.value >= totalVersions.value - 1) {
    return
  }
  currentVersion.value++
}

const runningIndex = ref()

async function createOptimize() {
  if (!props.recordId) {
    return
  }
  if (!props.optimizeText) {
    return
  }
  if (!props.action) {
    return
  }

  records.value.push('') // 插入空记录
  const updateIndex = records.value.length - 1
  runningIndex.value = updateIndex
  currentVersion.value = updateIndex // 设置当前展示的下标
  await start({
    url: baseUrl => `${baseUrl}/AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanCreateRecordOptimize`,
    body: () => JSON.stringify({
      TeacherId: userInfo.userId,
      Id: props.recordId,
      Scheme: props.action,
      OptimizeText: props.optimizeText,
      Prompt: inputContent.value, // 教师指令
    }),
    initOption: {
      onmessage: (msg) => {
        try {
          const { Content, Success } = JSON.parse(msg.data)
          if (!Success) {
            return
          }
          if (Content === 'DONE') {
            isRunning.value = false
            return
          }
          records.value[updateIndex] += Content ?? ''

          if (currentVersion.value === updateIndex) {
            toBottom()
          }
        }
        catch {

        }
      },
    },
  })
}

function titleRender() {
  return h('div', { class: 'flex items-center' }, [
    h('img', { src: AiLogo, alt: '', class: 'w-20px' }),
    h('div', { class: 'ml-12px' }, '润色'),
  ])
}

function replace() {
  emit('replace', currentContent.value)
  show.value = false
}

function insert() {
  emit('insert', currentContent.value)
  show.value = false
}

const showAction = computed(() => {
  if (!currentContent.value) {
    return false
  }
  return !(isRunning.value && runningIndex.value === currentVersion.value)
})
</script>

<template>
  <NModal
    v-model:show="show"
    :title="titleRender"
    preset="card"
    class="optimize-modal w-880px"
    :mask-closable="true"
  >
    <div class="modal-content">
      <!-- 说明文本 -->
      <div class="description">
        <NScrollbar ref="scrollbarRef" class="h-full">
          <XMarkdown
            :markdown="currentContent"
            :code-x-slot="{
              codeHeaderControl: () => h('div', ''),
            }"
            class="markdown-body"
            default-theme-mode="light"
          />
        </NScrollbar>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <div class="action-buttons">
          <NButton v-if="showAction" size="large" class="action-btn continue-btn" @click="replace">
            <IconLocalExchange class="i-carbon-add h-16px w-16px" />
            替换
          </NButton>
          <NButton v-if="showAction" class="action-btn input-btn" @click="insert">
            <IconLocalInsert class="i-carbon-menu h-16px w-16px" />
            插入
          </NButton>
        </div>

        <!-- 右侧按钮组 -->
        <div class="right-actions">
          <!-- 版本切换器 -->
          <div v-if="totalVersions > 1" class="version-switcher">
            <button
              class="version-btn prev-btn"
              :disabled="currentVersion <= 0"
              @click="handlePrevVersion"
            >
              <IconLocalArrowOutlined class="i-carbon-chevron-left h-14px w-14px rotate-180" />
            </button>
            <span class="version-text">{{ currentVersion + 1 }}/{{ totalVersions }}</span>
            <button
              class="version-btn next-btn"
              :disabled="currentVersion >= totalVersions - 1"
              @click="handleNextVersion"
            >
              <IconLocalArrowOutlined class="i-carbon-chevron-right h-14px w-14px" />
            </button>
          </div>

          <NButton v-if="showAction" secondary type="primary" @click="handleRegenerate">
            重新生成
          </NButton>
          <NButton v-if="showAction" secondary type="primary" @click="handleCopy">
            复制
          </NButton>
        </div>
      </div>

      <!-- 输入框 -->
      <div class="border-t-1 border-[#E5E7EB]">
        <div class="input-wrapper">
          <div class="input-icon">
            <img src="@/assets/imgs/teaching-plan/start.png" class="i-carbon-star-filled h-20px w-20px text-purple-500">
          </div>
          <input
            v-model="inputContent"
            placeholder="请输入优化文本的指令"
            class="h-full flex-1 bg-transparent outline-none"
          >
          <div class="input-send" @click="handleOptimize">
            <IconLocalMonotoneUpArrow :class="inputContent ? 'opacity-100' : 'opacity-40'" />
          </div>
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.optimize-modal {
  border-radius: 12px;
}

.modal-content {
  padding: 0;
}

.description {
  margin-bottom: 24px;
  height: 300px;
}

.desc-title {
  font-size: 14px;
  color: #374151;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.desc-list p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 8px 0;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  height: 42px;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 16px;
}

.continue-btn {
  background: #a855f7;
  border: 1px solid #a855f7;
  color: white;
}

.continue-btn:hover {
  background: #9333ea;
  border-color: #9333ea;
}

.input-btn {
  background: white;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.input-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-switcher {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 4px;
}

.version-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.version-btn:hover:not(:disabled) {
  background: #e2e8f0;
  color: #374151;
}

.version-btn:disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

.version-text {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  min-width: 32px;
  text-align: center;
}

.right-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 13px;
  border-radius: 6px;
}

.regenerate-btn {
  background: white;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.regenerate-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.copy-btn {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.copy-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.input-section {
  margin-top: 32px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 24px;
  padding: 16px 0;
  gap: 12px;
}

.input-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.optimize-input {
  flex: 1;
  background: transparent;
  border: none;
  font-size: 14px;
}

.input-send {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s;
  flex-shrink: 0;
  font-size: 34px;
  color: #A99FFF;
}

.input-send:hover {
}

:deep(.n-input__input-el) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

:deep(.n-input) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.n-input--focus) {
  box-shadow: none !important;
}
</style>
