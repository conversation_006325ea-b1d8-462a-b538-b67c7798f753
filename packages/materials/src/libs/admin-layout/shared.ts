import type { AdminLayoutProps, LayoutCssVars, LayoutCssVarsProps } from '../../types'

/** 布局滚动元素的ID常量 */
export const LAYOUT_SCROLL_EL_ID = '__SCROLL_EL_ID__'

/** 布局最大z-index常量 */
export const LAYOUT_MAX_Z_INDEX = 100

/**
 * 根据CSS变量属性创建布局CSS变量
 * @param props CSS变量属性
 * @returns 返回生成的CSS变量对象
 */
function createLayoutCssVarsByCssVarsProps(props: LayoutCssVarsProps) {
  const cssVars: LayoutCssVars = {
    '--soy-header-height': `${props.headerHeight}px`, // 头部高度
    '--soy-header-z-index': props.headerZIndex, // 头部z-index
    '--soy-tab-height': `${props.tabHeight}px`, // 标签页高度
    '--soy-tab-z-index': props.tabZIndex, // 标签页z-index
    '--soy-sider-width': `${props.siderWidth}px`, // 侧边栏宽度
    '--soy-sider-collapsed-width': `${props.siderCollapsedWidth}px`, // 折叠侧边栏宽度
    '--soy-sider-z-index': props.siderZIndex, // 侧边栏z-index
    '--soy-mobile-sider-z-index': props.mobileSiderZIndex, // 移动端侧边栏z-index
    '--soy-footer-height': `${props.footerHeight}px`, // 底部高度
    '--soy-footer-z-index': props.footerZIndex, // 底部z-index
  }

  return cssVars
}

/**
 * 创建布局CSS变量
 * @param props 布局属性
 * @returns 返回生成的CSS变量对象
 */
export function createLayoutCssVars(props: AdminLayoutProps) {
  const {
    mode,
    isMobile,
    maxZIndex = LAYOUT_MAX_Z_INDEX,
    headerHeight,
    tabHeight,
    siderWidth,
    siderCollapsedWidth,
    footerHeight,
  } = props

  // 计算各部分的z-index层级
  const headerZIndex = maxZIndex - 3
  const tabZIndex = maxZIndex - 5
  const siderZIndex = mode === 'vertical' || isMobile ? maxZIndex - 1 : maxZIndex - 4
  const mobileSiderZIndex = isMobile ? maxZIndex - 2 : 0
  const footerZIndex = maxZIndex - 5

  // 组装CSS变量属性
  const cssProps: LayoutCssVarsProps = {
    headerHeight,
    headerZIndex,
    tabHeight,
    tabZIndex,
    siderWidth,
    siderZIndex,
    mobileSiderZIndex,
    siderCollapsedWidth,
    footerHeight,
    footerZIndex,
  }

  return createLayoutCssVarsByCssVarsProps(cssProps)
}
