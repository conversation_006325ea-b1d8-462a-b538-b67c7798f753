<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import { useVoicePlayerConsumer } from '@sa/hooks'

defineOptions({
  name: 'VoicePlayButton',
})

const props = defineProps<{
  /** 消息项 */
  item: Chat.MessageItem
}>()

// 使用语音播放上下文
const { handleVoicePlay, isMessagePlaying } = useVoicePlayerConsumer()

// 检查当前消息是否正在播放
function getPlayingState(): boolean {
  return isMessagePlaying(props.item.key)
}

// 处理点击播放按钮的原始函数
async function handlePlayButtonClick() {
  try {
    await handleVoicePlay(props.item)
  }
  catch (error) {
    console.error('语音播放失败:', error)
  }
}

// 使用防抖包装点击处理函数
const onPlayButtonClick = useDebounceFn(handlePlayButtonClick, 300)
</script>

<template>
  <NButton
    size="small"
    type="primary"
    class="transition-all duration-200 ease-in-out active:translate-y-0 hover:translate-y-[-1px] hover:shadow-[0_2px_8px_rgba(98,106,239,0.3)]"
    @click="onPlayButtonClick"
  >
    <template #icon>
      <SvgIcon
        :icon="getPlayingState() ? 'mdi:pause' : 'rivet-icons:audio'"
        class="text-16px"
      />
    </template>
    {{ getPlayingState() ? '语音暂停' : '语音播放' }}
  </NButton>
</template>
