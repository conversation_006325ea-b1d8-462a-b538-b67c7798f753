<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  /** 任务数据 */
  task: Task.AgentStudentHomePageAgentTaskOuput
}

interface Emits {
  /** 按钮点击事件 */
  (e: 'buttonClick', task: Task.AgentStudentHomePageAgentTaskOuput): void
}

defineOptions({
  name: 'TaskCard',
})

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理按钮点击
function handleButtonClick() {
  emit('buttonClick', props.task)
}

// 计算任务状态文本和样式
const taskStatus = computed(() => {
  const isCompleted = props.task.StudentDoTaskState === 1
  const isEnd = props.task.AgentTaskState === 2
  return {
    text: isCompleted ? '已完成' : '待提交',
    class: isCompleted ? 'text-green-600' : 'text-orange-600',
    buttonText: isEnd || isCompleted ? '查看详情' : '开始任务',
  }
})
</script>

<template>
  <NCard
    hoverable
    :bordered="false"
    class="h-full cursor-pointer transition-all duration-300 hover:translate-y-[-2px] hover:shadow-lg"
    style="border-radius: 12px; border: 1px solid #e5e7eb;"
  >
    <div class="h-90px flex items-center gap-16px">
      <!-- 左侧头像 -->
      <NAvatar
        :src="task.AgentLogo"
        :size="64"
        class="flex-shrink-0 ring-2 ring-gray-100"
        fallback-src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
        style="border-radius: 50%;"
      />

      <!-- 中间内容区域 -->
      <div class="min-w-0 flex-1">
        <!-- 标题行 -->
        <div class="mb-8px flex items-start justify-between">
          <div class="min-w-0 flex-1">
            <h3 class="mb-4px text-18px text-gray-900 font-semibold leading-tight">
              <span :class="taskStatus.class" class="font-medium">【{{ taskStatus.text }}】</span>
              {{ task.AgentTaskName }}
            </h3>
          </div>
          <!-- 智能体标签 -->
          <NTag
            type="info"
            size="small"
            class="border-blue-200 bg-blue-50 px-8px py-2px text-12px text-blue-700 font-medium"
          >
            {{ task.AgentName }}
          </NTag>
        </div>

        <!-- 描述内容 -->
        <p class="line-clamp-2 mb-8px text-14px text-gray-600 leading-relaxed">
          {{ task.AgentTaskIntroduce }}
        </p>
      </div>

      <!-- 右侧按钮 -->
      <div class="flex-shrink-0">
        <NButton
          size="medium"
          type="primary"
          @click="handleButtonClick"
        >
          {{ taskStatus.buttonText }}
        </NButton>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
