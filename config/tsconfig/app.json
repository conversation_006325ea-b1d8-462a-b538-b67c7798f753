{"$schema": "https://json.schemastore.org/tsconfig", "display": "App Config", "extends": "./base.json", "compilerOptions": {"jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "ESNext"], "types": ["vite/client", "node", "unplugin-icons/types/vue", "naive-ui/volar"], "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo"}, "vueCompilerOptions": {"experimentalDisableTemplateSupport": true}}