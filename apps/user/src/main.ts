/*
 *           佛曰:
 *                   写字楼里写字间，写字间里程序员；
 *                   程序人员写程序，又拿程序换酒钱。
 *                   酒醒只在网上坐，酒醉还来网下眠；
 *                   酒醉酒醒日复日，网上网下年复年。
 *                   但愿老死电脑间，不愿鞠躬老板前；
 *                   奔驰宝马贵者趣，公交自行程序员。
 *                   别人笑我忒疯癫，我笑自己命太贱；
 *                   不见满街漂亮妹，哪个归得程序员？
 */

import { createApp } from 'vue'
import 'virtual:svg-icons-register'
import 'uno.css'
import '@sa/styles/css/global.css'
import { setupAppVersionNotification, setupDayjs, setupIconifyOffline, setupNProgress, setupVConsole } from '@sa/plugins'
import { setupRouter } from './router'
import App from './App.vue'
import { setupStore } from '@/store'
import { setupLoading } from '@/plugins/loading'

async function setupApp() {
  setupLoading()

  setupNProgress()

  setupIconifyOffline()

  setupDayjs()

  setupVConsole()

  const app = createApp(App)

  setupStore(app)

  await setupRouter(app)

  setupAppVersionNotification()

  app.mount('#app')
}

setupApp()
