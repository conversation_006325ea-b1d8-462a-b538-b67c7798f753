import json5 from 'json5'

/**
 * Create service config by current env
 *
 * @param env The current env
 */
/**
 * 根据当前环境变量创建服务配置
 * @param env 包含环境变量的对象
 * @returns 完整的服务配置对象
 */
export function createServiceConfig(env: Env.ImportMeta) {
  // 从环境变量中解构出基础URL和其他服务URL配置
  const { VITE_SERVICE_BASE_URL, VITE_OTHER_SERVICE_BASE_URL } = env

  // 初始化其他服务配置对象
  let other = {} as Record<App.Service.OtherBaseURLKey, string>
  try {
    // 尝试解析JSON5格式的其他服务URL配置
    other = json5.parse(VITE_OTHER_SERVICE_BASE_URL)
  }
  catch {
    // 解析失败时输出错误日志
    console.error('VITE_OTHER_SERVICE_BASE_URL is not a valid json5 string')
  }

  // 创建基础HTTP配置
  const httpConfig: App.Service.SimpleServiceConfig = {
    baseURL: VITE_SERVICE_BASE_URL, // 主服务基础URL
    other, // 其他服务URL配置
  }

  // 获取其他服务的key列表
  const otherHttpKeys = Object.keys(httpConfig.other) as App.Service.OtherBaseURLKey[]

  // 构建其他服务配置数组
  const otherConfig: App.Service.OtherServiceConfigItem[] = otherHttpKeys.map((key) => {
    return {
      key, // 服务标识key
      baseURL: httpConfig.other[key], // 服务基础URL
      proxyPattern: createProxyPattern(key), // 生成代理路径模式
    }
  })

  // 构建完整服务配置
  const config: App.Service.ServiceConfig = {
    baseURL: httpConfig.baseURL, // 主服务URL
    proxyPattern: createProxyPattern(), // 默认代理路径模式
    other: otherConfig, // 其他服务配置
  }

  return config
}

/**
 * 获取后端服务基础URL
 * @param env - 包含环境变量的对象
 * @param isProxy - 是否使用代理模式
 * @returns 包含主服务URL和其他服务URL的对象
 */
export function getServiceBaseURL(env: Env.ImportMeta, isProxy: boolean) {
  // 从环境配置中获取基础URL和其他服务配置
  const { baseURL, other } = createServiceConfig(env)

  // 初始化其他服务URL对象
  const otherBaseURL = {} as Record<App.Service.OtherBaseURLKey, string>

  // 遍历其他服务配置，根据代理模式设置URL
  other.forEach((item) => {
    otherBaseURL[item.key] = isProxy ? item.proxyPattern : item.baseURL
  })

  // 返回最终URL配置
  return {
    baseURL: isProxy ? createProxyPattern() : baseURL, // 主服务URL
    otherBaseURL, // 其他服务URL集合
  }
}

/**
 * 创建后端服务代理路径模式
 * @returns 返回生成的代理路径字符串
 * @param key
 */
function createProxyPattern(key?: App.Service.OtherBaseURLKey) {
  // 当未提供key时，返回默认代理路径
  if (!key) {
    return '/proxy-default'
  }

  // 根据提供的key生成特定服务的代理路径
  return `/proxy-${key}`
}
