<script setup lang="ts">
import { downloadApiFile } from '@sa/utils'
import {
  fetchSaveTeachingPlan,
} from '@/service/api/teaching-plan'

defineProps<{
  businessId: string
}>()

defineEmits<{
  (e: 'onUpdateName', item: TeachingPlanApi.TeachingPlanCreateRecordFileOutput): void
}>()

const show = defineModel('show', { default: false })

const planWordList = defineModel<TeachingPlanApi.TeachingPlanCreateRecordFileOutput[]>('planWordList', { default: [] })

const newState = ref<Record<string, {
  isEditing: boolean
  docTempName: string
}>>({})

watch(planWordList, () => {
  if (!planWordList.value || !planWordList.value.length) {
    newState.value = {}
    return
  }

  planWordList.value.forEach((item) => {
    if (newState.value[item.Id]) {
      return
    }
    newState.value[item.Id] = {
      isEditing: false,
      docTempName: item.FileName,
    }
  })
}, { immediate: true })

async function onSave(item: TeachingPlanApi.TeachingPlanCreateRecordFileOutput) {
  const { error } = await fetchSaveTeachingPlan({
    Type: 2,
    Name: item.FileName,
    FileUrl: item.FileUrl,
  })
  if (error) {
    return
  }
  window.$message?.success('教案保存成功')
}
</script>

<template>
  <NModal
    v-model:show="show"
    title="教案下载"
    preset="card"
    class="w-700px"
  >
    <div class="w-full">
      <div
        v-for="item in planWordList"
        :key="item.Id"
        class="mt-8px min-h-40px w-full flex items-center border-1 border-[#EAF1FF] rounded-8px bg-[#F3F7FF] px-8px py-16px"
      >
        <img class="mr-8px size-24px shrink-0" src="@/assets/imgs/teaching-plan/file-word.png" alt="">

        <div class="flex flex-1 items-center">
          <template v-if="!newState[item.Id]?.isEditing">
            <span class="line-clamp-1 text-[#6E6F70]">{{ item.FileName }}</span>
            <img
              class="ml-12px size-20px cursor-pointer"
              src="@/assets/imgs/teaching-plan/ic_edit.png"
              alt=""
              @click="() => {
                newState[item.Id].isEditing = !newState[item.Id].isEditing
                newState[item.Id].docTempName = item.FileName
              }"
            >
          </template>
          <template v-else>
            <NInput v-if="newState[item.Id]" v-model:value="newState[item.Id].docTempName" class="text-[#6E6F70] !w-200px" />
            <div>
              <NButton
                quaternary
                type="info"
                @click="() => {
                  newState[item.Id].isEditing = !newState[item.Id].isEditing
                  item.FileName = newState[item.Id].docTempName
                  $emit('onUpdateName', item)
                }"
              >
                确定
              </NButton>
              <NButton
                quaternary
                type="info"
                @click="newState[item.Id].isEditing = !newState[item.Id].isEditing"
              >
                取消
              </NButton>
            </div>
          </template>
        </div>

        <div class="shrink-0">
          <NButton
            quaternary
            type="primary"
            @click="() => {
              downloadApiFile(item.FileUrl, item.FileName)
            }"
          >
            下载
          </NButton>
          <NButton quaternary type="primary" @click="onSave(item)">
            添加到教案列表
          </NButton>
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>

</style>
