<script setup lang="ts">
import { useNaiveForm } from '@sa/hooks'
import type { TreeSelectOverrideNodeClickBehavior } from 'naive-ui'
import {
  fetchGetChapterListByCondition,
  fetchGetTeachClass,
} from '@/service/api/agent'
import { useAuthStore } from '@/store/modules/auth'

defineOptions({
  name: 'StepOne',
})

const authStore = useAuthStore()
const { userInfo, yearSemester, activeState } = authStore
const { formRef } = useNaiveForm()
// 响应式数据
const classOptions = ref<AgentApi.GetTeachClassReponse[]>([])
// 教材章节选项
const textbookOptions = ref<AgentApi.ChapterResponse[]>([])

// 转换后的班级选项，用于 NSelect 组件
const classSelectOptions = computed(() => {
  return classOptions.value.map(item => ({
    label: item.ClassName,
    value: item.ClassId,
  }))
})

const formModel = reactive<AgentApi.SaveTeacherOralCommunicationInput>({
  Name: '',
  Introduce: '',
  Target: '',
  Prologue: '',
  ChapterId: '',
  EvaluateId: '',
  ClassId: [],
  TimeRange: undefined,
})

// 表单验证规则
const rules = {
  Name: {
    required: true,
    message: '请输入任务名称',
    trigger: ['blur', 'input'],
  },
  Introduce: {
    required: true,
    message: '请输入项目背景介绍',
    trigger: ['blur', 'input'],
  },
  Target: {
    required: true,
    message: '请输入教学目标',
    trigger: ['blur', 'input'],
  },
  Prologue: {
    required: true,
    message: '请输入开场白',
    trigger: ['blur', 'input'],
  },
  ChapterId: {
    required: true,
    message: '请选择教材章节',
    trigger: ['blur', 'change'],
  },
  EvaluateId: {
    required: true,
    message: '请选择评价体系',
    trigger: ['blur', 'change'],
  },
  ClassId: {
    required: true,
    validator: (_rule: any, value: any[]) => {
      if (!value || value.length === 0) {
        return new Error('请选择班级')
      }
      return true
    },
    trigger: ['blur', 'change'],
  },
  TimeRange: {
    required: true,
    validator: (_rule: any, value: any) => {
      if (
        !value
        || (Array.isArray(value) && value.length !== 2)
        || value.some((v: any) => !v)
      ) {
        return new Error('请选择任务周期')
      }
      return true
    },
    trigger: ['blur', 'change'],
  },
}

// 树选择器行为配置
const textbookOverride: TreeSelectOverrideNodeClickBehavior = ({ option }) => {
  return option.Second ? 'toggleExpand' : 'default'
}

async function init() {
  try {
    const [classRes, chapterRes] = await Promise.all([
      fetchGetTeachClass({
        year: yearSemester.NowYear?.toString() || '',
        grade: activeState.grade,
        UserId: userInfo.userId,
      }),
      fetchGetChapterListByCondition({
        year: yearSemester.NowYear?.toString() || '',
        grade: activeState.grade,
        term: yearSemester.NowTerm?.toString() || '',
      }),
    ])
    classOptions.value = classRes.data || []
    textbookOptions.value = chapterRes.data || []
  }
  catch (error) {
    console.error('Failed to initialize data:', error)
  }
}

onMounted(() => {
  init()
})
// 暴露校验方法给父组件
defineExpose({
  validate: () => formRef.value?.validate(),
  formModel,
  initData: (data: AgentApi.SaveTeacherOralCommunicationInput) => {
    Object.keys(formModel).forEach((key) => {
      // eslint-disable-next-line ts/ban-ts-comment
      // @ts-expect-error
      formModel[key] = data[key] as any
    })
  },
})
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"
  >
    <!-- 表单内容 -->
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NForm
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="left"
        :label-width="120"
        size="medium"
      >
        <NGrid responsive="screen" item-responsive>
          <!-- 任务名称 -->
          <NFormItemGi span="24" label="任务名称" path="Name" class="pr-24px">
            <NInput
              v-model:value="formModel.Name"
              placeholder="请输入任务名称"
              clearable
              maxlength="100"
              show-count
            />
          </NFormItemGi>

          <!-- 项目背景介绍 -->
          <NFormItemGi
            span="24"
            label="项目背景介绍"
            path="Introduce"
            class="pr-24px"
          >
            <NInput
              v-model:value="formModel.Introduce"
              type="textarea"
              placeholder="请输入"
              :rows="4"
              clearable
              maxlength="1000"
              show-count
            />
          </NFormItemGi>

          <!-- 教学目标 -->
          <NFormItemGi span="24" label="教学目标" path="Target" class="pr-24px">
            <NInput
              v-model:value="formModel.Target"
              type="textarea"
              placeholder="请输入教学目标"
              :rows="3"
              clearable
              maxlength="1000"
              show-count
            />
          </NFormItemGi>
          <!-- 开场白 -->
          <NFormItemGi span="24" label="开场白" path="Prologue" class="pr-24px">
            <NInput
              v-model:value="formModel.Prologue"
              type="textarea"
              placeholder="请输入开场白"
              :rows="3"
              clearable
              maxlength="1000"
              show-count
            />
          </NFormItemGi>
          <!-- 教材章节 -->
          <NFormItemGi
            span="24 s:12 m:12"
            label="教材章节"
            path="ChapterId"
            class="pr-24px"
          >
            <NTreeSelect
              v-model:value="formModel.ChapterId"
              key-field="ChapterId"
              label-field="ChapterName"
              children-field="Second"
              :options="textbookOptions"
              placeholder="请选择教材章节"
              clearable
              block-line
              :override-default-node-click-behavior="textbookOverride"
            />
          </NFormItemGi>
          <!-- 开始日期 -->
          <NFormItemGi
            span="24 s:12 m:12"
            label="任务周期"
            path="TimeRange"
            class="pr-24px"
          >
            <NDatePicker
              v-model:formatted-value="formModel.TimeRange"
              type="datetimerange"
              :default-time="['00:00:00', '23:59:59']"
              placeholder=""
              clearable
              class="w-full"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </NFormItemGi>

          <!-- 结束日期 -->
          <NFormItemGi
            span="24 s:12 m:12"
            label="发布班级"
            path="ClassId"
            class="pr-24px"
          >
            <NSelect
              v-model:value="formModel.ClassId"
              :options="classSelectOptions"
              placeholder="请选择班级"
              clearable
              multiple
            />
          </NFormItemGi>
        </NGrid>
      </NForm>
    </NCard>
  </div>
</template>
