<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import dayjs from 'dayjs'
import { useLoading } from '@sa/hooks'

import PreviewCode from '../code/PreviewCode.vue'

defineOptions({
  name: 'AnimationCard',
})

const props = withDefaults(defineProps<Props>(), {
  code: '',
  height: '200px',
  bordered: true,
})

interface Props {
  /** 动画代码内容 */
  code?: string
  /** 卡片高度 */
  height?: string | number
  /** 是否显示边框 */
  bordered?: boolean
}

interface AnimationOption {
  Name?: string
  Url?: string
  Time?: string
  CreateTime?: string
}

// 使用 loading hook
const { loading, startLoading, endLoading } = useLoading(true)
const isRendered = ref(false)
const backgroundImageRef = ref<HTMLImageElement>()
const previewModal = ref({
  html: '',
  show: false,
})

// 解析动画选项
const animationOption = computed<AnimationOption>(() => {
  if (!props.code) {
    return {}
  }

  try {
    // 查找 option= 后面的 JSON 字符串
    const optionMatch = props.code.match(/option=(\{.*?\})/)
    if (optionMatch && optionMatch[1]) {
      return JSON.parse(optionMatch[1])
    }
  }
  catch (error) {
    console.warn('Failed to parse animation option:', error)
  }

  return {}
})

// 检查所有资源是否加载完成
function checkRenderComplete() {
  const promises: Promise<void>[] = []

  // 等待背景图片加载完成
  if (backgroundImageRef.value) {
    if (backgroundImageRef.value.complete) {
      // 图片已经加载完成
    }
    else {
      // 图片还在加载中
      promises.push(
        new Promise((resolve) => {
          const img = backgroundImageRef.value!
          const onLoad = () => {
            img.removeEventListener('load', onLoad)
            img.removeEventListener('error', onLoad)
            resolve()
          }
          img.addEventListener('load', onLoad)
          img.addEventListener('error', onLoad)
        }),
      )
    }
  }

  // 等待DOM渲染完成
  promises.push(nextTick())

  // 所有资源加载完成后结束loading
  Promise.all(promises).then(() => {
    isRendered.value = true
    endLoading()
  })
}

// 监听动画选项变化，重新开始loading
watch(animationOption, () => {
  if (animationOption.value.Name) {
    startLoading()
    nextTick(() => {
      checkRenderComplete()
    })
  }
}, { immediate: true })

// 组件挂载后检查渲染状态
onMounted(() => {
  nextTick(() => {
    checkRenderComplete()
  })
})

function onTapCard() {
  // 只有在渲染完成后才允许点击
  if (!loading.value) {
    previewModal.value = {
      html: animationOption.value.Url || '',
      show: true,
    }
  }
}
</script>

<template>
  <div
    class="group relative mb-12px h-120px w-380px flex flex-col overflow-hidden border-1 rounded-8px border-solid bg-white p-16px text-[#44A9FB]"
    :class="{ 'cursor-pointer': !loading }"
    @click="onTapCard"
  >
    <!-- Loading 状态遮罩 -->
    <div
      v-if="loading"
      class="absolute inset-0 z-10 flex items-center justify-center rounded-8px bg-white bg-opacity-90"
    >
      <NSpin size="medium" />
    </div>

    <!-- 卡片内容 -->
    <div
      class="relative transition-opacity duration-300"
      :class="{ 'opacity-50': loading }"
    >
      <icon-local-code-plus />
      <span
        class="line-clamp-1 mt-8px max-w-[220px] text-xl text-[#333333] font-bold"
        :title="animationOption.Name"
      >
        {{ animationOption.Name || '加载中...' }}
      </span>
      <span class="mt-8px text-sm text-[#b8acac] font-bold">创建时间：
        {{
          animationOption.CreateTime ? dayjs(animationOption.CreateTime).format("mm:ss") : "-"
        }}
      </span>
      <img
        ref="backgroundImageRef"
        src="@/assets/imgs/code-bg.png"
        class="absolute right-0px top-20px h-136px w-140px transition-all duration-300 group-hover:scale-120 group-hover:-transform-rotate-15"
        alt=""
      >
    </div>

    <PreviewCode v-model:modal="previewModal" />
  </div>
</template>

<style scoped>
/* 淡入动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

/* 动画延迟类 */
.animate-delay-500 {
  animation-delay: 0.5s;
}

.animate-delay-1000 {
  animation-delay: 1s;
}

.animate-delay-1500 {
  animation-delay: 1.5s;
}

.animate-delay-2000 {
  animation-delay: 2s;
}

.animate-duration-2000 {
  animation-duration: 2s;
}

/* 卡片悬停效果 */
.animation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 内容区域样式 */
.animation-content {
  opacity: 0;
}

.animation-content.animate-fade-in {
  opacity: 1;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .animation-card {
    margin: 0 -4px;
  }

  .animation-card:hover {
    transform: translateY(-1px);
  }
}
</style>
