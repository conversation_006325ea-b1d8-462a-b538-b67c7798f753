import { qsFormInjectionKey } from '@sa/components/common/questions/qs-provide/context'

interface Options {
  type: Ref<'edit' | 'answer' | 'preview' | null | undefined>
}

export default function useQuestionsForm(options: Options) {
  // 选中的选项
  const QForm = inject(qsFormInjectionKey, null)

  const disabled = computed(() => options.type.value === 'preview')

  const mergedDisabled = computed(() => {
    if (disabled.value !== undefined && disabled.value !== null)
      return disabled.value
    return QForm?.props?.type === 'preview'
  })

  return {
    mergedDisabled,
  }
}
