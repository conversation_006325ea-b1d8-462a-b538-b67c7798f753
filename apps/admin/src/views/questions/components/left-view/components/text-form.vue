<script setup lang="ts">
import { useFormRules, useNaiveForm } from '@sa/hooks'

defineOptions({
  name: 'TextForm',
})

const textContent = defineModel('textContent', {
  type: String,
  default: '',
})

// 表单引用和校验
const { formRef, validate } = useNaiveForm()
const { createRequiredRule } = useFormRules()

// 表单校验规则
const rules = {
  textContent: [
    createRequiredRule('请输入文本内容'),
    {
      max: 2000,
      message: '文本内容不能超过2000个字符',
      trigger: ['blur', 'input'],
    },
  ],
}

// 暴露校验方法给父组件
defineExpose({
  validate,
})
</script>

<template>
  <div class="h-full p-12px">
    <div class="flex items-center">
      <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
      <span class="text-14px text-[#333] font-500">出题范围</span>
    </div>
    <NForm
      ref="formRef"
      :model="{ textContent }"
      :rules="rules"
      size="medium"
    >
      <NFormItem path="textContent">
        <NInput
          v-model:value="textContent"
          type="textarea"
          placeholder="请在下方输入框内 **粘贴或录入文本内容**（如知识点梳理、课文段落、教学案例等），AI将深度解析文本核心信息，自动生成对应题目。  👉 支持场景：概念定义、课文节选、实验描述等教学素材  👉 操作建议：文本越完整清晰，命题精准度越高~  输入完成后，点击「立即出题」即可启动AI命题流程。"
          :rows="6"
          clearable
          maxlength="2000"
          show-count
        />
      </NFormItem>
    </NForm>
  </div>
</template>
