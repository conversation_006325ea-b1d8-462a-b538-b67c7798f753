<script setup lang="ts">
defineOptions({
  name: 'AgentIn<PERSON>Header',
})

const props = defineProps<Props>()

interface AgentInfo {
  id: string
  name: string
  description: string
  avatar: string
  category: string
}

interface Props {
  /** 智能体信息 */
  agentInfo: AgentInfo
  /** 页面标题 */
  title?: string
}
</script>

<template>
  <div class="border-b border-gray-200 dark:border-gray-700 dark:bg-gray-800">
    <div class="pb-12px">
      <!-- 智能体信息卡片 -->
      <div class="flex items-start gap-16px">
        <!-- 头像 -->
        <div class="flex-shrink-0">
          <NImage
            :src="props.agentInfo.avatar"
            class="s:h-48px s:w-48px h-56px w-56px rounded-12px object-cover"
          />
        </div>

        <!-- 信息内容 -->
        <div class="min-w-0 flex-1">
          <div class="mb-8px flex items-center gap-8px">
            <h2 class="s:text-16px text-18px text-gray-900 font-semibold dark:text-gray-100">
              {{ props.agentInfo.name }}
            </h2>
            <span class="rounded-4px bg-blue-100 px-8px py-2px text-12px text-blue-600 dark:bg-blue-900 dark:text-blue-300">
              {{ props.agentInfo.category }}
            </span>
          </div>
          <p class="s:text-13px line-clamp-3 text-14px text-gray-600 dark:text-gray-400">
            {{ props.agentInfo.description }}
          </p>
        </div>
      </div>

      <div class="relative mt-24px">
        <span class="text-22px text-[#464646] font-bold">
          口语交际任务
        </span>
        <p
          class="absolute bottom-0 h-8px w-171px"
          style="background: linear-gradient(90deg, rgba(68, 169, 251, 0.6) 59.39%, rgba(68, 169, 251, 0) 100%);"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 文本截断样式 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
