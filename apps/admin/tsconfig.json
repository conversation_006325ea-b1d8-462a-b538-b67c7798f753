{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@sa/tsconfig/app.json", "compilerOptions": {"baseUrl": ".", "paths": {"@sa/components/*": ["../../packages/base/components/*"], "@sa/store": ["../../packages/base/store"], "@sa/store/*": ["../../packages/base/store/*"], "@sa/enum": ["../../packages/base/enum"], "@sa/enum/*": ["../../packages/base/enum/*"], "@sa/constants": ["../../packages/base/constants"], "@sa/constants/*": ["../../packages/base/constants/*"], "@sa/styles": ["../../packages/base/styles"], "@sa/styles/*": ["../../packages/base/styles/*"], "@sa/service": ["../../packages/base/service"], "@sa/service/*": ["../../packages/base/service/*"], "@sa/theme": ["../../packages/base/theme"], "@sa/theme/*": ["../../packages/base/theme/*"], "@sa/directives": ["../../packages/directives"], "@sa/directives/*": ["../../packages/directives/*"], "@/*": ["./src/*"], "~/*": ["./*"]}}, "references": [{"path": "./tsconfig.node.json"}], "include": ["../../packages", "../../typings", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}