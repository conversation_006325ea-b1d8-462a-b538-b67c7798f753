import renderMathInElement from 'katex/contrib/auto-render'
import 'katex/dist/katex.min.css'
import type { Directive, DirectiveBinding } from 'vue'

const delimiters = [
  { left: '$$', right: '$$', display: true },
  { left: '\\[', right: '\\]', display: true },
  { left: '\\(', right: '\\)', display: false },
  { left: '$', right: '$', display: false },
]
const renderOptions = {
  delimiters,
  strict: false,
  throwOnError: false,
}
function renderKatex(el: HTMLElement) {
  try {
    renderMathInElement(el, renderOptions)
  }
  catch (error) {
    console.error('KaTeX渲染错误:', error)
  }
}
export const katex: Directive = {
  mounted(el: HTMLElement, _binding: DirectiveBinding) {
    renderKatex(el)
  },
  updated(el: HTMLElement) {
    renderKatex(el)
  },
}
