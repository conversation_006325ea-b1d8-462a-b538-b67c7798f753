import { inject, provide } from 'vue'
import type { InjectionKey } from 'vue'

/**
 * Use context
 *
 * @example
 *   ```ts
 *   // there are three vue files: A.vue, B.vue, C.vue, and A.vue is the parent component of B.vue and C.vue
 *
 *   // context.ts
 *   import { ref } from 'vue';
 *   import { useContext } from '@sa/hooks';
 *
 *   export const { setupStore, useStore } = useContext('demo', () => {
 *     const count = ref(0);
 *
 *     function increment() {
 *       count.value++;
 *     }
 *
 *     function decrement() {
 *       count.value--;
 *     }
 *
 *     return {
 *       count,
 *       increment,
 *       decrement
 *     };
 *   })
 *   ``` // A.vue
 *   ```vue
 *   <template>
 *     <div>A</div>
 *   </template>
 *   <script setup lang="ts">
 *   import { setupStore } from './context';
 *
 *   setupStore();
 *   // const { increment } = setupStore(); // also can control the store in the parent component
 *   </script>
 *   ``` // B.vue
 *   ```vue
 *   <template>
 *    <div>B</div>
 *   </template>
 *   <script setup lang="ts">
 *   import { useStore } from './context';
 *
 *   const { count, increment } = useStore();
 *   </script>
 *   ```;
 *
 *   // C.vue is same as B.vue
 *
 * @param contextName Context name
 * @param fn Context function
 */
export default function useContext<T extends (...args: any[]) => any>(contextName: string, fn: T) {
  type Context = ReturnType<T> // 获取函数返回类型

  // 创建上下文相关方法
  const { useProvide, useInject: useStore } = createContext<Context>(contextName)

  /**
   * 在父组件中设置上下文
   * @param args 传递给fn的参数
   * @returns 返回上下文对象
   */
  function setupStore(...args: Parameters<T>) {
    const context: Context = fn(...args)
    return useProvide(context)
  }

  return {
    /** 在父组件中设置store */
    setupStore,
    /** 在子组件中使用store */
    useStore,
  }
}

/**
 * 创建Vue上下文
 * @param contextName 上下文名称
 * @returns 返回provide和inject方法
 */
function createContext<T>(contextName: string) {
  // 创建唯一的注入key
  const injectKey: InjectionKey<T> = Symbol(contextName)

  /**
   * 提供上下文
   * @param context 上下文对象
   * @returns 返回传入的上下文对象
   */
  function useProvide(context: T) {
    provide(injectKey, context)
    return context
  }

  /**
   * 注入上下文
   * @returns 返回注入的上下文对象
   */
  function useInject() {
    return inject(injectKey) as T
  }

  return {
    useProvide,
    useInject,
  }
}
