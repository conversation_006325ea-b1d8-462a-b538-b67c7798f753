// 导入工具函数
import { transformRecordToOption } from '@sa/utils'

/**
 * 启用状态配置
 * key: 状态值(1-启用, 2-禁用)
 * value: 状态显示文本
 */
export const enableStatusRecord: Record<Api.Common.EnableStatus, string> = {
  1: '启用',
  2: '禁用',
}
// 启用状态选项(用于下拉选择等场景)
export const enableStatusOptions = transformRecordToOption(enableStatusRecord)

/**
 * 用户性别配置
 * key: 性别编码(1-男, 2-女)
 * value: 性别显示文本
 */
export const userGenderRecord: Record<Api.SystemManage.UserGender, string> = {
  1: '男',
  2: '女',
}
// 用户性别选项
export const userGenderOptions = transformRecordToOption(userGenderRecord)

/**
 * 菜单类型配置
 * key: 类型编码(1-目录, 2-菜单)
 * value: 类型显示文本
 */
export const menuTypeRecord: Record<Api.SystemManage.MenuType, string> = {
  1: '目录',
  2: '菜单',
}
// 菜单类型选项
export const menuTypeOptions = transformRecordToOption(menuTypeRecord)

/**
 * 菜单图标类型配置
 * key: 类型编码(1-iconify图标, 2-本地图标)
 * value: 类型显示文本
 */
export const menuIconTypeRecord: Record<Api.SystemManage.IconType, string> = {
  1: 'iconify图标',
  2: '本地图标',
}
// 菜单图标类型选项
export const menuIconTypeOptions = transformRecordToOption(menuIconTypeRecord)
