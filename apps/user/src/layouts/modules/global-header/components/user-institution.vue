<script setup lang="ts">
import { useAuthStore } from '@/store/modules/auth'

defineOptions({
  name: 'UserInstitution',
})

// 获取认证store
const authStore = useAuthStore()
const { subjectState, setCurrentSubject } = authStore

// 当前选中的学科ID
const subjectValue = computed({
  get: () => subjectState.currentSubjectId,
  set: (value: string) => {
    setCurrentSubject(value)
  },
})

// 学科选项
const subjectOptions = computed(() => {
  return subjectState.list.map(subject => ({
    label: subject.Name,
    value: subject.Id,
    disabled: false,
  }))
})
</script>

<template>
  <NSpace>
    <!-- 学科选择 -->
    <NSelect
      v-model:value="subjectValue"
      :bordered="false"
      :options="subjectOptions"
      class="w-80px"
    />
  </NSpace>
</template>
