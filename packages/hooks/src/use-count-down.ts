import { computed, onScopeDispose, ref } from 'vue'
import { useRafFn } from '@vueuse/core'

/**
 * 倒计时钩子函数
 * @param seconds - 倒计时总秒数
 */
export default function useCountDown(seconds: number) {
  // 每秒帧数(用于平滑动画)
  const FPS_PER_SECOND = 60

  // 当前帧数
  const fps = ref(0)

  // 计算当前剩余秒数
  const count = computed(() => Math.ceil(fps.value / FPS_PER_SECOND))

  // 是否正在倒计时
  const isCounting = computed(() => fps.value > 0)

  // 使用requestAnimationFrame实现的帧更新
  const { pause, resume } = useRafFn(
    () => {
      if (fps.value > 0) {
        fps.value -= 1
      }
      else {
        pause()
      }
    },
    { immediate: false }, // 默认不自动开始
  )

  /**
   * 开始倒计时
   * @param updateSeconds - 可选的倒计时秒数(覆盖初始值)
   */
  function start(updateSeconds: number = seconds) {
    fps.value = FPS_PER_SECOND * updateSeconds
    resume()
  }

  /**
   * 停止倒计时
   */
  function stop() {
    fps.value = 0
    pause()
  }

  // 组件卸载时自动暂停
  onScopeDispose(() => {
    pause()
  })

  return {
    count, // 当前剩余秒数
    isCounting, // 是否正在倒计时
    start, // 开始倒计时方法
    stop, // 停止倒计时方法
  }
}
