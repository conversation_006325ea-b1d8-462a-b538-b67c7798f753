/**
 * 聊天相关的工具函数
 */

/**
 * 聊天配置常量
 */
export const CHAT_CONFIG = {
  /** 分页配置 */
  PAGINATION: {
    PAGE_SIZE: 3,
    INITIAL_PAGE: 1,
    NO_MORE_DATA_FLAG: 0,
  },
  /** SSE 配置 */
  SSE: {
    DONE_PREFIX: 'DONE',
    CONTENT_TYPE: 'application/json',
  },
  /** 消息配置 */
  MESSAGE: {
    SYSTEM_ROLE: 'system' as const,
    USER_ROLE: 'user' as const,
    AI_PLACEMENT: 'start' as const,
    START_PLACEMENT: 'start' as const,
  },
  /** 任务类型 */
  TASK_TYPE: {
    AGENT: 0,
  },
  /** 侧边栏配置 */
  DRAWER: {
    WIDTH: '400px',
    TRANSITION_DURATION: '300ms',
  },
} as const

/**
 * 检查是否有更多数据可加载
 * @param pageIndex 当前页码
 * @returns 是否有更多数据
 */
export function hasMoreData(pageIndex: number): boolean {
  return pageIndex !== CHAT_CONFIG.PAGINATION.NO_MORE_DATA_FLAG
}

/**
 * 格式化API请求参数
 * @param baseParams 基础参数
 * @param baseParams.AgentId 智能体ID
 * @param baseParams.AgentTaskId 智能体任务ID
 * @param pageIndex 页码
 * @param userInfo 用户信息
 * @param userInfo.userId 用户ID
 * @returns 格式化后的参数
 */
export function formatApiParams(
  baseParams: { AgentId: string, AgentTaskId: string },
  pageIndex: number,
  userInfo: { userId: string },
) {
  return {
    PageIndex: pageIndex,
    PageSize: CHAT_CONFIG.PAGINATION.PAGE_SIZE,
    ...baseParams,
    StudentId: userInfo.userId,
    Type: CHAT_CONFIG.TASK_TYPE.AGENT,
  }
}
