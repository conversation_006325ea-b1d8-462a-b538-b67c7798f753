<script setup lang="ts">
import { computed } from 'vue'

defineOptions({
  name: 'CustomDrawerContent',
})
const props = withDefaults(defineProps<Props>(), {
  oralCommunicationInfo: () => ({
    logo: '',
    oralCommunicationDetails: {
      Prologue: '',
      TimeRange: [],
      Name: '',
      InteractiveMode: 0,
      Introduce: '',
    },
  }),
  isSubmitting: false,
})
const emit = defineEmits<{
  submitTask: []
}>()
interface Props {
  oralCommunicationInfo: {
    logo: string
    oralCommunicationDetails: Task.AgentModelAISaveTeacherOralCommunicationInput
  }
  isSubmitting?: boolean
}
// 计算属性：格式化时间范围
const formattedTimeRange = computed(() => {
  const timeRange = props.oralCommunicationInfo.oralCommunicationDetails.TimeRange
  if (timeRange && timeRange.length >= 2) {
    return `${timeRange[0]} - ${timeRange[1]}`
  }
  return '暂无时间范围'
})

// 计算属性：获取互动模式文本
const interactiveModeText = computed(() => {
  const mode = props.oralCommunicationInfo.oralCommunicationDetails.InteractiveMode
  switch (mode) {
    case 1:
      return '指令式'
    case 2:
      return '对话式'
    case 3:
      return '辩论式'
    default:
      return '未知模式'
  }
})

// 处理接受任务
function handleAcceptTask() {
  // 这里可以添加接受任务的逻辑
  // 例如：调用API接受任务，更新任务状态等
  emit('submitTask')
}
</script>

<template>
  <div class="flex flex-col gap-24px p-24px">
    <!-- 任务头像 -->
    <div class="flex justify-center">
      <div class="relative">
        <NAvatar
          :src="props.oralCommunicationInfo.logo"
          :size="80"
          class="shadow-lg ring-4 ring-white"
          fallback-src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
        />
      </div>
    </div>

    <!-- 任务标题 -->
    <div class="text-center">
      <h2 class="text-20px text-gray-900 font-semibold">
        {{ props.oralCommunicationInfo.oralCommunicationDetails.Name || '口语交际任务' }}
      </h2>
    </div>

    <!-- 任务描述 -->
    <div class="space-y-12px">
      <div class="flex items-start gap-8px">
        <span class="whitespace-nowrap text-14px font-medium">
          任务描述：
        </span>
      </div>
      <div class="text-14px text-gray-700 leading-relaxed">
        {{ props.oralCommunicationInfo.oralCommunicationDetails.Introduce }}
      </div>
    </div>

    <!-- 互动模式 -->
    <div class="space-y-8px">
      <div class="flex items-center gap-8px">
        <span class="text-14px font-medium">
          互动模式：
        </span>
      </div>
      <div class="flex items-center gap-8px">
        <SvgIcon icon="ph:chat-circle" class="text-16px text-blue-500" />
        <span class="text-14px">
          {{ interactiveModeText }}
        </span>
      </div>
    </div>

    <!-- 任务周期 -->
    <div class="space-y-8px">
      <div class="flex items-center gap-8px">
        <span class="text-14px font-medium">
          任务周期：
        </span>
      </div>
      <div class="flex items-center gap-8px">
        <SvgIcon icon="ph:clock" class="text-16px text-green-500" />
        <span class="text-14px">
          {{ formattedTimeRange }}
        </span>
      </div>
    </div>

    <!-- 接受任务按钮 -->
    <div class="pt-8px">
      <NButton
        type="primary"
        size="large"
        class="h-48px w-full text-16px font-medium"
        :loading="props.isSubmitting"
        :disabled="props.isSubmitting"
        @click="handleAcceptTask"
      >
        <template #icon>
          <SvgIcon icon="ph:check-circle" class="text-18px" />
        </template>
        {{ props.isSubmitting ? '提交中...' : '提交任务' }}
      </NButton>
    </div>
  </div>
</template>

<style scoped>
:deep(.n-avatar:hover) {
  transform: scale(1.05);
}

/* 按钮悬停效果 */
:deep(.n-button) {
  transition: all 0.3s ease;
}

:deep(.n-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}
</style>
