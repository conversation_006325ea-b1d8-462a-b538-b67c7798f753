<script setup lang="ts">
import { useBoolean, useNaiveForm, useRequest } from '@sa/hooks'
import type { NFormItem } from 'naive-ui'
import { HuaweiOBSUploader } from '@sa/utils'
import AddModal from './AddModal.vue'
import { useAuthStore } from '@/store/modules/auth'
import {
  fetchDelTeachingPlanContentDemand,
  fetchGetTeachingPlanContentDemand,
  fetchSaveTeachingPlanContentDemand,
} from '@/service/api/teaching-plan'
import { fetchGetAgentModelInfo, fetchGetChapterListByCondition } from '@/service/api'

interface Props {
  businessId: string
}
defineOptions({
  name: 'LeftView',
})

defineProps<Props>()

const emit = defineEmits<{
  (e: 'submit'): void
  (e: 'changeType', type: number): void
}>()

const formModel = defineModel('modelValue') as Ref<TeachingPlanApi.CreateTeachingPlanInput>

const { gradeList, yearSemester, activeState } = useAuthStore()

const { data: requirementOptions, run: fetchRequirementOptions } = useRequest(fetchGetTeachingPlanContentDemand)
const { data: modelOptions } = useRequest(fetchGetAgentModelInfo)

const { data: chapterOptions } = useRequest(() => {
  return fetchGetChapterListByCondition({
    year: yearSemester.NowYear?.toString() || '',
    grade: activeState.grade,
    term: yearSemester.NowTerm?.toString() || '',
  })
})

const { formRef, validate } = useNaiveForm()

const requireFormItem = ref<InstanceType<typeof NFormItem>>()

const addModal = ref({
  show: false,
})

const formRules: Partial<Record<keyof TeachingPlanApi.CreateTeachingPlanInput, App.Global.FormRule[]>> = {
  Type: [{ required: true, message: '请选择创建方式', trigger: ['change', 'blur'] }],
  ModelId: [{ required: true, message: '请选择AI模型', trigger: ['change', 'blur'] }],
  Grade: [{ required: true, type: 'number', message: '请选择年级', trigger: ['change', 'blur'] }],
  Title: [{ required: true, message: '请输入标题', trigger: ['change', 'blur'] }],
  ChapterId: [{ required: true, message: '请选择章节', trigger: ['change', 'blur'] }],
  Content: [{ required: true, message: '请输入教案内容', trigger: ['change', 'blur'] }],
  FileUrl: [{ required: true, message: '请上传文档', trigger: ['change', 'blur'] }],
  ContentDemandId: [{ required: true, type: 'array', message: '请选择内容要求', trigger: ['change', 'blur'] }],
}

// 创建方式选项
const createMethods = [
  { key: 1, label: '标题创建', active: true },
  { key: 2, label: '文本创建', active: false },
  { key: 3, label: '章节创建', active: false },
  { key: 4, label: '文档创建', active: false },
]

watch(modelOptions, () => {
  if (!modelOptions.value || !modelOptions.value.length) {
    return
  }
  if (formModel.value.ModelId) {
    return
  }
  formModel.value.ModelId = modelOptions.value[0].Id
}, { immediate: true })

// 处理模型选择
function handleModelSelect(key: string | number) {
  formModel.value.ModelId = key as string
}

// 处理创建方式切换
function switchCreateMethod(method: number) {
  emit('changeType', method)
}

async function generateLesson() {
  await validate()

  emit('submit')
}

// 年级选择选项
const gradeOptions = computed(() => {
  return gradeList.map(item => ({
    label: item.Gradename,
    value: Number(item.Id),
  }))
})

function handleRequirementClick(option: TeachingPlanApi.GetTeachingPlanContentDemandOutput) {
  if (!formModel.value.ContentDemandId) {
    formModel.value.ContentDemandId = []
  }
  if (formModel.value.ContentDemandId.includes(option.Id)) {
    formModel.value.ContentDemandId = formModel.value.ContentDemandId.filter((v: any) => v !== option.Id)
    requireFormItem.value?.validate()
    return
  }
  formModel.value.ContentDemandId.push(option.Id)

  requireFormItem.value?.validate()
}

function handleAddRequirement() {
  // 弹窗提示
  addModal.value.show = true
}

async function onAddRequirement(data: { title: string, description: string }) {
  const { error } = await fetchSaveTeachingPlanContentDemand({
    Title: data.title,
    Describe: data.description,
  })
  if (error) {
    return
  }
  window.$message?.success('添加成功')
  fetchRequirementOptions()
}

async function handleDeleteRequirement(option: TeachingPlanApi.GetTeachingPlanContentDemandOutput) {
  const { error } = await fetchDelTeachingPlanContentDemand({
    Id: option.Id,
  })
  if (error) {
    return
  }
  formModel.value.ContentDemandId = (formModel.value.ContentDemandId ?? []).filter((v: any) => v !== option.Id)
  window.$message?.success('删除成功')
  fetchRequirementOptions()

  requireFormItem.value?.validate()
}

// 上传文件loading
const { setTrue: startUploading, setFalse: endUploading, bool: isUploading } = useBoolean(false)

const { open, onChange } = useFileDialog({
  accept: '.doc,.docx,.pdf,.txt',
  directory: false,
  multiple: false,
})

onChange(async (files) => {
  if (!files) {
    return
  }
  const file = files.item(0)
  if (!file) {
    return
  }
  try {
    startUploading()
    const huaweiObs = new HuaweiOBSUploader()
    const { url } = await huaweiObs.upload({ file })
    formModel.value.FileUrl = url
    formModel.value.FileName = file.name
  }
  finally {
    endUploading()
  }
})

async function uploadDocument() {
  open()
}

function removeDocument() {
  formModel.value.FileUrl = ''
  formModel.value.FileName = ''
}
</script>

<template>
  <div class="min-h-full w-500px">
    <div class="flex-shrink-0">
      <!-- 标题栏 -->
      <div class="mb-16px flex items-center justify-between">
        <div class="flex items-center gap-8px">
          <div
            class="h-38px w-38px flex items-center justify-center rounded-8px from-blue-500 to-purple-500 bg-gradient-to-r"
          >
            <SvgIcon icon="mdi:robot-outline" class="text-20px text-white" />
          </div>
          <span class="from-blue-500 to-purple-500 bg-gradient-to-r bg-clip-text text-22px text-transparent font-600">
            AI教案助手
          </span>
        </div>

        <!-- AI模型选择下拉框 -->
        <NDropdown
          trigger="click"
          :value="formModel.ModelId"
          :options="(modelOptions ?? []).map(item => ({ label: item.ModelName, key: item.Id }))"
          :show-arrow="true"
          @select="handleModelSelect"
        >
          <NButton
            quaternary
            class="flex items-center gap-6px rounded-8px bg-blue-100 px-12px py-8px hover:bg-blue-200"
          >
            <SvgIcon icon="mdi:brain" class="mr-4px text-16px text-blue-600" />
            <span class="text-14px text-blue-700">
              {{
                formModel.ModelId ? (modelOptions ?? []).find(m => m.Id === formModel.ModelId)?.ModelName : '选择模型'
              }}
            </span>
            <SvgIcon icon="mdi:chevron-down" class="text-12px text-blue-600" />
          </NButton>
        </NDropdown>
      </div>

      <!-- 创建方式选项卡 -->
      <div class="mb-16px flex justify-between rounded-8px bg-blue-100 p-4px">
        <div
          v-for="method in createMethods"
          :key="method.key"
          class="group relative cursor-pointer rounded-6px px-16px py-8px text-14px font-500 transition-all duration-200"
          :class="[
            formModel.Type === method.key
              ? 'bg-white text-blue-600 shadow-sm '
              : ' hover:text-blue-600',
          ]"
          @click="switchCreateMethod(method.key)"
        >
          <span>{{ method.label }}</span>
        </div>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="min-h-0 flex-shrink-1">
      <NScrollbar class="h-full">
        <!-- 表单区域 -->
        <NForm
          ref="formRef"
          :model="formModel"
          :rules="formRules"
          label-placement="top"
          size="medium"
          class="space-y-16px"
        >
          <!-- 年级选择 -->
          <NFormItem label="年级" path="Grade">
            <NSelect
              v-model:value="formModel.Grade"
              :options="gradeOptions"
              placeholder="请选择年级"
              clearable
            />
          </NFormItem>

          <!-- 内容要求 -->
          <NFormItem ref="requireFormItem" label="内容要求" path="ContentDemandId">
            <div class="flex flex-wrap gap-8px">
              <button
                v-for="option in (requirementOptions ?? [])"
                :key="option.Id"
                class="group relative border rounded-6px px-12px py-6px text-14px font-500 transition-all duration-200"
                :class="[
                  (formModel.ContentDemandId ?? []).includes(option.Id)
                    ? 'border-purple-300 bg-purple-100 text-purple-700'
                    : 'border-gray-300 bg-gray-50 text-gray-600 hover:border-purple-300 hover:bg-purple-50',
                ]"
                @click.stop="handleRequirementClick(option)"
              >
                <span>{{ option.Title }}</span>

                <NPopconfirm v-if="option.Type === 2" @positive-click="handleDeleteRequirement(option)">
                  <template #trigger>
                    <icon-local-remove-solid
                      class="absolute right-0 top-0 translate-x-1/2 text-gray -translate-y-1/2"
                      @click.stop
                    />
                  </template>
                  确定要删除吗？
                </NPopconfirm>
              </button>

              <button
                class="border border-gray-300 rounded-6px bg-gray-50 px-12px py-6px text-14px text-gray-600 font-500 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50"
                @click="handleAddRequirement"
              >
                +
              </button>
            </div>
          </NFormItem>

          <template v-if="formModel.Type === 1">
            <!-- 教案标题 -->
            <NFormItem label="教案标题" path="Title">
              <NInput
                v-model:value="formModel.Title"
                placeholder="请输入教案标题"
                clearable
                maxlength="100"
                show-count
              />
            </NFormItem>
            <!-- 描述文本 -->
            <div class="mb-16px rounded-8px bg-blue-50 p-12px">
              <p class="text-14px text-gray-600 leading-20px">
                <SvgIcon icon="mdi:information-outline" class="mr-4px inline text-blue-500" />
                输入想要生成教案的标题，AI 会自动调用学科课标、教学规律，生成完整教案框架
              </p>
            </div>
          </template>

          <template v-if="formModel.Type === 2">
            <!-- 内容要求 -->
            <NFormItem label="教案内容" path="Content">
              <NInput
                v-model:value="formModel.Content"
                placeholder="请输入教案内容"
                type="textarea"
                :rows="3"
                clearable
                maxlength="500"
                show-count
              />
            </NFormItem>
            <div class="text-gray">
              AI会基于提供的课文原文、已有教学思路等文本素材，拓展 / 整合出教案内容
            </div>
          </template>

          <!-- 章节 -->
          <template v-if="formModel.Type === 3">
            <!-- 教材章节 -->
            <NFormItem
              label="教材章节"
              path="ChapterId"
            >
              <NTreeSelect
                v-model:value="formModel.ChapterId"
                key-field="ChapterId"
                label-field="ChapterName"
                children-field="Second"
                :options="chapterOptions as any"
                placeholder="请选择教材章节"
                clearable
                block-line
                :override-default-node-click-behavior="({ option }) => option.Second ? 'toggleExpand' : 'default'"
              />
            </NFormItem>
          </template>

          <!--          文档创建 -->
          <template v-if="formModel.Type === 4">
            <NFormItem
              path="FileUrl"
              label="文档上传"
            >
              <div class="w-full">
                <NSpin :show="isUploading" description="文档上传中...">
                  <template v-if="!formModel.FileUrl">
                    <div
                      class="h-133px w-full flex flex-col cursor-pointer items-center justify-center border-1"
                      @click="uploadDocument"
                    >
                      <img class="mb-16px size-22px" src="@/assets/imgs/teaching-plan/add-upload.png" alt="">
                      <span class="text-14px">点击或者拖动文件到该区域来上传</span>
                      <span
                        class="text-center text-12px text-[#717077]"
                      >上传文件支持doc、docx、PDF、TXT格式的文档，文件大小不能超过10MB</span>
                    </div>
                    <p class="mt-8px text-14px text-[#828282]">
                      AI提取文档中的关键信息，生成一个贴合文档材料内容及要求的教案设计
                    </p>
                  </template>
                  <template v-else>
                    <div
                      class="mt-8px h-40px flex items-center border-1 border-[#EAF1FF] rounded-8px bg-[#F3F7FF] px-8px"
                    >
                      <img class="mr-8px size-24px" src="@/assets/imgs/teaching-plan/file-word.png" alt="">
                      <span class="flex-1">{{ formModel.FileName || '-' }}</span>
                      <NPopconfirm :on-positive-click="removeDocument">
                        <template #trigger>
                          <icon-local-remove-solid class="cursor-pointer color-gray-500" />
                        </template>
                        确定要删除吗？
                      </NPopconfirm>
                    </div>
                  </template>
                </NSpin>
              </div>
            </NFormItem>
          </template>

          <template v-if="formModel.Type !== 2">
            <!-- 其他要求 -->
            <NFormItem label="其他要求" path="Demand">
              <NInput
                v-model:value="formModel.Demand"
                type="textarea"
                placeholder="请输入其他特殊要求（可选）"
                :rows="3"
                clearable
                maxlength="500"
                show-count
              />
            </NFormItem>
          </template>
        </NForm>
      </Nscrollbar>
    </div>

    <!-- 生成按钮区域 -->
    <div class="mt-24px flex-shrink-0 rounded-8px bg-blue-50 p-16px text-center">
      <NButton
        type="primary"
        size="large"
        class="mb-12px from-blue-500 to-purple-500 bg-gradient-to-r px-24px py-12px"
        @click="generateLesson"
      >
        <template #icon>
          <SvgIcon icon="mdi:magic-staff" />
        </template>
        {{ businessId ? '再次生成' : '一键生成教案' }}
      </NButton>
      <p class="text-12px text-gray-500">
        内容由AI生成，仅供参考。
      </p>
    </div>

    <AddModal v-model:show="addModal.show" @confirm="onAddRequirement" />
  </div>
</template>

<style scoped lang="scss">

</style>
