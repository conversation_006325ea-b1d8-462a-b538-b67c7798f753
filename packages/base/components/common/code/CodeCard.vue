<script setup lang="ts" name="CodeCard">
import dayjs from 'dayjs'
import PreviewCode from './PreviewCode.vue'

interface Props {
  item: {
    /** 文件名称 */
    Name?: string
    /** 文件地址 */
    Url?: string
    /** 创建时间 */
    CreateTime?: string
    /** HTML代码 */
    HtmlCode?: string
  }
  /** 预览代码 */
  previewCode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  previewCode: true,
})

const emit = defineEmits(['preview', 'click'])

const previewModal = ref({
  html: '',
  show: false,
})

function onTapCard() {
  emit('click', props.item)
  if (props.previewCode) {
    //
    previewModal.value = {
      html: props.item.Url || props.item.HtmlCode || '',
      show: true,
    }
  }
}
</script>

<template>
  <div
    class="group relative h-120px w-380px flex flex-col cursor-pointer overflow-hidden border-1 rounded-8px border-solid bg-[linear-gradient(109deg_#fff_45.34%_#f7f3ff_102.43%)] p-16px text-[#44A9FB]"
    @click="onTapCard"
  >
    <icon-local-code-plus />
    <span
      class="line-clamp-1 mt-8px max-w-[220px] text-xl text-[#333333] font-bold"
      :title="item.Name"
    >
      {{ item.Name }}
    </span>
    <span class="mt-8px text-sm text-[#b8acac] font-bold">创建时间：
      {{
        item.CreateTime ? dayjs(item.CreateTime).format("mm:ss") : "-"
      }}
    </span>
    <img
      src="@/assets/imgs/code-bg.png"
      class="absolute right-0px top-20px h-136px w-140px transition-all duration-300 group-hover:scale-120 group-hover:-transform-rotate-15"
      alt=""
    >

    <PreviewCode v-model:modal="previewModal" />
  </div>
</template>

<style scoped></style>
