import useBoolean from './use-boolean'

/**
 * 加载状态钩子
 * @param initValue 初始值，默认为false
 */
export default function useLoading(initValue = false) {
  // 使用useBoolean钩子创建loading状态及相关方法
  const {
    bool: loading, // loading状态
    setTrue: startLoading, // 开始loading方法
    setFalse: endLoading, // 结束loading方法
  } = useBoolean(initValue)

  return {
    loading, // 当前loading状态
    startLoading, // 开始loading方法
    endLoading, // 结束loading方法
  }
}
