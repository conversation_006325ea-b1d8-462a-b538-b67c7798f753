declare namespace Task {
  /**
   * AgentStudentHomePageAgentTaskInput，智能体学生端首页输入
   */
  interface AgentTeacherHomePageListInputRequest {
    /**
     * 智能体任务状态（1进行中、2已结束）
     */
    AgentTaskState: number
    /**
     * 班级Id
     */
    ClassId: string
    /**
     * 页码（默认1）
     */
    PageIndex: number
    /**
     * 每页数量（默认10）
     */
    PageSize: number
    /**
     * 学校Id
     */
    SchoolId: string
    /**
     * 学生Id
     */
    StudentId: string
    /**
     * 学科Id
     */
    SubjectId: string
  }

  /**
   * AgentStudentHomePageAgentTaskOuputPageReturn，分页输出
   */
  interface AgentStudentHomePageAgentTaskOuputResponse {
    /**
     * 数据集
     */
    Datas: AgentStudentHomePageAgentTaskOuput[] | null
    /**
     * 数据总量
     */
    TotalCount?: number
  }

  /**
   * AgentStudentHomePageAgentTaskOuput，智能体学生端首页输出
   */
  interface AgentStudentHomePageAgentTaskOuput {
    /**
     * 智能体应用编码
     */
    AgentBotCode: string
    /**
     * 智能体Id
     */
    AgentId: string
    /**
     * 智能体Logo
     */
    AgentLogo: string
    /**
     * 智能体名称
     */
    AgentName: string
    /**
     * 智能体任务Id
     */
    AgentTaskId: string
    /**
     * 智能体任务背景
     */
    AgentTaskIntroduce: string
    /**
     * 智能体任务名称
     */
    AgentTaskName: string
    /**
     * 智能体任务状态（1进行中、2已结束）
     */
    AgentTaskState: number
    /**
     * 任务周期（开始时间）
     */
    BeginTime?: string
    /**
     * 任务周期（结束时间）
     */
    EndTime?: string
    /**
     * 开场白
     */
    Prologue?: string
    /**
     * 学生做智能体任务状态（1已完成、2未完成）
     */
    StudentDoTaskState: number
  }

  /**
   * 获取AI对话内容记录
   */
  interface AgentStudentOralCommunicationDialogueRequest {
    /**
     * 页码（默认1）
     */
    PageIndex: number
    /**
     * 每页数量（默认10）
     */
    PageSize: number
    /**
     * 智能体Id
     */
    AgentId: string
    /**
     * 智能体任务Id
     */
    AgentTaskId: string
    /**
     * 学生Id
     */
    StudentId: string
    /**
     * 类型（0智能体）
     */
    Type: 0
  }
  // 出参
  interface AgentTeacherHomePageAgentTaskOuputResponse {
    /**
     * 数据集
     */
    Datas: AgentTeacherHomePageAgentTaskOuput[] | null
    /**
     * 数据总量
     */
    TotalCount: number
  }

  interface AgentTeacherHomePageAgentTaskOuput {
    /**
     * 会话Id
     */
    Id: string
    /**
     * 问
     */
    Ask: string
    AskInfo: AgentModelAIAIDialogueASKDto
    /**
     * 答
     */
    Answer: string
    /**
     * 消息类型(1系统消息、2用户消息)
     */
    MessageType: 1 | 2
    /**
     * 创建时间
     */
    CreateTime: string
  }
  interface AgentModelAIAIDialogueASKDto {
    /**
     * “问”文本
     */
    AskText?: null | string
    AudioFile?: AgentModelAIAIDialogueASKAudioFileInfo | null
    /**
     * 文件消息
     */
    Files?: AgentModelAIAIDialogueASKFileInfo[] | null
  }
  interface AgentModelAIAIDialogueASKFileInfo {
    /**
     * 文件名称
     */
    FileName: string
    /**
     * 文件大小
     */
    FileSize: string
    /**
     * 文件类型
     */
    FileType: string
    /**
     * 文件地址
     */
    FileUrl: string
  }
  interface AgentModelAIAIDialogueASKAudioFileInfo {
    /**
     * 语音地址
     */
    AudioUrl: string
    /**
     * 时长（单位:秒）
     */
    Duration: number
  }

  /** 获取智能体口语交际详情 */
  interface AgentTeacherOralCommunicationGetOralCommunicationDetailInputResponse {
    /**
     * 是否存在问答数据
     */
    IsData: boolean
    Logo: string
    OralCommunicationInfo: AgentModelAISaveTeacherOralCommunicationInput
  }
  interface AgentModelAISaveTeacherOralCommunicationInput {
    /**
     * 开场白
     */
    Prologue: string
    /**
     * 发布时间范围(下标0开始、下标1结束)
     */
    TimeRange: string[]
    /**
     * 名称
     */
    Name: string
    /**
     * 互动模式(1指令式、2对话式、3辩论式)
     */
    InteractiveMode: number
    /**
     * 项目背景介绍
     */
    Introduce: string
  }

  /** 智能体口语交际对话 */

  interface AgentStudentOralCommunicationDialogueInputRequest {
    /**
     * 智能体Id
     */
    AgentId: string
    /**
     * 智能体任务Id
     */
    AgentTaskId: string
    /**
     * 语音文件
     */
    AudioUrl?: string
    /**
     * 班级Id
     */
    ClassId: string
    /**
     * 语音时长（单位:秒）
     */
    Duration?: number
    /**
     * 对话消息
     */
    Msg?: string
    /**
     * 学生Id
     */
    StudentId: string
  }

  /**
   * UwooAgent.Model.AI.AgentStudentOralCommunicationSubmitInput，智能体_学生端口语交际提交入参
   */
  interface AgentStudentOralCommunicationSubmitInputRequest {
    /**
     * 智能体Id
     */
    AgentId: string
    /**
     * 智能体任务Id
     */
    AgentTaskId: string
    /**
     * 班级Id
     */
    ClassId: string
    /**
     * 学生Id
     */
    StudentId: string
  }
}
