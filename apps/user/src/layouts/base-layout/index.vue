<script setup lang="ts">
// 导入Vue相关API和组件
import { computed, defineAsyncComponent } from 'vue'
// 导入布局组件和常量
import { AdminLayout, LAYOUT_SCROLL_EL_ID } from '@sa/materials'
import type { LayoutMode } from '@sa/materials'
// 导入全局组件
import { useThemeStore } from '@sa/store/modules/theme'
import GlobalHeader from '../modules/global-header/index.vue'
import GlobalSider from '../modules/global-sider/index.vue'
import GlobalTab from '../modules/global-tab/index.vue'
import GlobalContent from '../modules/global-content/index.vue'
import GlobalFooter from '../modules/global-footer/index.vue'
import ThemeDrawer from '../modules/theme-drawer/index.vue'
// 导入上下文和store
import { setupMixMenuContext } from '../context'
import { useAppStore } from '@/store/modules/app'

// 定义组件名称
defineOptions({
  name: 'BaseLayout',
})

// 使用store
const appStore = useAppStore()
const themeStore = useThemeStore()
// 设置混合菜单上下文
const { childLevelMenus, isActiveFirstLevelMenuHasChildren } = setupMixMenuContext()

// 异步加载全局菜单组件
const GlobalMenu = defineAsyncComponent(() => import('../modules/global-menu/index.vue'))

// 计算布局模式
const layoutMode = computed(() => {
  const vertical: LayoutMode = 'vertical'
  const horizontal: LayoutMode = 'horizontal'
  return themeStore.layout.mode.includes(vertical) ? vertical : horizontal
})

// 计算header组件的props
const headerProps = computed(() => {
  const { mode, reverseHorizontalMix } = themeStore.layout

  const headerPropsConfig: Record<UnionKey.ThemeLayoutMode, App.Global.HeaderProps> = {
    'vertical': {
      showLogo: false,
      showMenu: false,
      showMenuToggler: true,
    },
    'vertical-mix': {
      showLogo: false,
      showMenu: false,
      showMenuToggler: false,
    },
    'horizontal': {
      showLogo: true,
      showMenu: true,
      showMenuToggler: false,
    },
    'horizontal-mix': {
      showLogo: true,
      showMenu: true,
      showMenuToggler: reverseHorizontalMix && isActiveFirstLevelMenuHasChildren.value,
    },
  }

  return headerPropsConfig[mode]
})

// 计算侧边栏是否可见
const siderVisible = computed(() => themeStore.layout.mode !== 'horizontal')

// 计算是否是垂直混合模式
const isVerticalMix = computed(() => themeStore.layout.mode === 'vertical-mix')

// 计算是否是水平混合模式
const isHorizontalMix = computed(() => themeStore.layout.mode === 'horizontal-mix')

// 计算侧边栏宽度
const siderWidth = computed(() => getSiderWidth())

// 计算侧边栏折叠宽度
const siderCollapsedWidth = computed(() => getSiderCollapsedWidth())

// 获取侧边栏宽度
function getSiderWidth() {
  const { reverseHorizontalMix } = themeStore.layout
  const { width, mixWidth, mixChildMenuWidth } = themeStore.sider

  if (isHorizontalMix.value && reverseHorizontalMix) {
    return isActiveFirstLevelMenuHasChildren.value ? width : 0
  }

  let w = isVerticalMix.value || isHorizontalMix.value ? mixWidth : width

  if (isVerticalMix.value && appStore.mixSiderFixed && childLevelMenus.value.length) {
    w += mixChildMenuWidth
  }

  return w
}

// 获取侧边栏折叠宽度
function getSiderCollapsedWidth() {
  const { reverseHorizontalMix } = themeStore.layout
  const { collapsedWidth, mixCollapsedWidth, mixChildMenuWidth } = themeStore.sider

  if (isHorizontalMix.value && reverseHorizontalMix) {
    return isActiveFirstLevelMenuHasChildren.value ? collapsedWidth : 0
  }

  let w = isVerticalMix.value || isHorizontalMix.value ? mixCollapsedWidth : collapsedWidth

  if (isVerticalMix.value && appStore.mixSiderFixed && childLevelMenus.value.length) {
    w += mixChildMenuWidth
  }

  return w
}
</script>

<template>
  <!-- 主布局组件 -->
  <AdminLayout
    v-model:sider-collapse="appStore.siderCollapse"
    :mode="layoutMode"
    :scroll-el-id="LAYOUT_SCROLL_EL_ID"
    :scroll-mode="themeStore.layout.scrollMode"
    :is-mobile="appStore.isMobile"
    :full-content="appStore.fullContent"
    :fixed-top="themeStore.fixedHeaderAndTab"
    :header-height="themeStore.header.height"
    :tab-visible="themeStore.tab.visible"
    :tab-height="themeStore.tab.height"
    :content-class="appStore.contentXScrollable ? 'overflow-x-hidden' : ''"
    :sider-visible="siderVisible"
    :sider-width="siderWidth"
    :sider-collapsed-width="siderCollapsedWidth"
    :footer-visible="themeStore.footer.visible"
    :footer-height="themeStore.footer.height"
    :fixed-footer="themeStore.footer.fixed"
    :right-footer="themeStore.footer.right"
  >
    <!-- 头部插槽 -->
    <template #header>
      <GlobalHeader v-bind="headerProps" />
    </template>
    <!-- 标签页插槽 -->
    <template #tab>
      <GlobalTab />
    </template>
    <!-- 侧边栏插槽 -->
    <template #sider>
      <GlobalSider />
    </template>
    <!-- 全局菜单 -->
    <GlobalMenu />
    <!-- 内容区域 -->
    <GlobalContent />
    <!-- 主题抽屉 -->
    <ThemeDrawer />
    <!-- 页脚插槽 -->
    <template #footer>
      <GlobalFooter />
    </template>
  </AdminLayout>
</template>

<style lang="scss">
/* 滚动条样式 */
#__SCROLL_EL_ID__ {
  @include scrollbar();
}
</style>
