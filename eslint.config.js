import antfu from '@antfu/eslint-config'

export default antfu(
  {
    unocss: true,
    vue: true,
    ignores: [
      'apps/admin/src/router/elegant/*.ts',
      'apps/user/src/router/elegant/*.ts',
    ],
  },
  {
    // 针对.vue后缀的文件进行格式化
    files: ['**/*.vue'],
    rules: {
      'vue/max-attributes-per-line': ['error', {
        singleline: 10,
        multiline: {
          max: 1,
        },
      }],
    },
  },
  {
    // 针对.ts后缀的文件进行格式化
    files: ['**/*.ts'],
    rules: {
    },
  },
  {
    rules: {
      'eslint-comments/no-unlimited-disable': 'off',
      // 或者完全禁用这条规则
      'node/prefer-global/process': 'off',
      'no-console': 'warn', // 关键是这个
    },
  },
)
