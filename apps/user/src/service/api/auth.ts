import { adminRequest, request } from '@/service/request'

/**
 * 用户登录接口
 * @param phoneNo 用户名
 * @param password 密码
 * @returns 返回登录token信息
 */
export function fetchLogin(userName: string, password: string) {
  return adminRequest<Api.Auth.LoginToken>({
    url: 'Base_Manage/Home/SubmitLogin',
    method: 'post',
    data: {
      userName,
      password,
      type: 0,
    },
  }).then((res) => {
    if (res.data) {
      return {
        data: {
          token: res.data as unknown as string,
          refreshToken: '',
        },
        error: null,
      }
    }
    return res
  })
}

/**
 * 获取用户信息接口
 * @returns 返回用户信息
 */
export async function fetchGetUserInfo() {
  return adminRequest<Auth.UserInfo>({
    url: 'Base_Manage/Base_User/GetUserInfoByUserName',
    method: 'post',
  }).then((res) => {
    if (res.data) {
      const { User, ...aug } = res.data as any
      return {
        data: {
          userName: User.UserName,
          userId: User.Id,
          ClassId: aug.ClassId,
          ClassName: aug.ClassName,
          GradeId: aug.GradeId,
          IsDZBQX: aug.IsDZBQX,
          SchoolId: aug.SchoolId,
          SchoolName: aug.SchoolName,
          roles: aug.roles || [],
          buttons: [],
          Photo: User.Photo,
        },
        error: null,
      }
    }
    return res
  })
}

/**
 * 模拟后端错误接口
 * @param code 错误码
 * @param msg 错误信息
 * @returns 返回错误响应
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } })
}

/**
 * 刷新token接口
 * @param refreshToken 刷新token
 * @returns 返回新的token信息
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken,
    },
  })
}
