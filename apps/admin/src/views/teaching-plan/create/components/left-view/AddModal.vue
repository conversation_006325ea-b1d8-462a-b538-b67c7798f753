<script setup lang="ts">
import { ref, watch } from 'vue'
import { NButton, NForm, NFormItem, NInput, NModal, NSpace } from 'naive-ui'

interface Props {
  show: boolean
  title?: string
  placeholder?: string
}

interface Emits {
  (e: 'update:show', value: boolean): void

  (e: 'confirm', data: { title: string, description: string }): void

  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '添加',
  placeholder: 'AI会基于描述更精确的生成你想要的内容',
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  title: '',
  description: '',
})

// 表单验证规则
const rules = {
  title: {
    required: true,
    message: '请输入标题',
    trigger: ['blur', 'change'],
  },
}

// 监听弹窗显示状态，重置表单
watch(() => props.show, (newShow) => {
  if (newShow) {
    formData.value = {
      title: '',
      description: '',
    }
  }
})

// 关闭弹窗
function handleClose() {
  emit('update:show', false)
}

// 取消操作
function handleCancel() {
  emit('cancel')
  handleClose()
}

// 确认操作
function handleConfirm() {
  if (!formData.value.title.trim()) {
    return
  }

  emit('confirm', {
    title: formData.value.title.trim(),
    description: formData.value.description.trim(),
  })
  handleClose()
}

// 处理键盘事件
function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    handleConfirm()
  }
  else if (e.key === 'Escape') {
    handleCancel()
  }
}
</script>

<template>
  <NModal
    :show="show"
    :mask-closable="false"
    preset="card"
    :title="title"
    class="add-modal"
    :style="{ width: '445px' }"
    @update:show="handleClose"
    @keydown="handleKeydown"
  >
    <div class="modal-content">
      <NForm :model="formData" :rules="rules" label-placement="left" label-width="60">
        <!-- 标题输入框 -->
        <NFormItem label="标题" path="title" :show-require-mark="true">
          <NInput
            v-model:value="formData.title"
            placeholder="请输入"
            maxlength="10"
            show-count
            clearable
          />
        </NFormItem>

        <!-- 描述文本域 -->
        <NFormItem label="描述">
          <NInput
            v-model:value="formData.description"
            type="textarea"
            :placeholder="placeholder"
            :rows="4"
            maxlength="300"
            show-count
            clearable
          />
        </NFormItem>
      </NForm>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="modal-footer">
        <NSpace justify="end">
          <NButton @click="handleCancel">
            取消
          </NButton>
          <NButton
            type="primary"
            class="!bg-[linear-gradient(90deg_#A99FFF_0%_#CFA3FF_100%)]"
            :disabled="!formData.title.trim()"
            @click="handleConfirm"
          >
            确定
          </NButton>
        </NSpace>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
.add-modal {
  border-radius: 8px;
}

.modal-content {
  padding: 8px 0;
}

.modal-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.n-card-header) {
  padding: 20px 24px 16px;
  font-size: 16px;
  font-weight: 500;
}

:deep(.n-card__content) {
  padding: 0 24px 20px;
}

:deep(.n-card__footer) {
  padding: 0 24px 20px;
}

:deep(.n-form-item-label) {
  font-size: 14px;
  color: #333;
}

:deep(.n-form-item-label--required::before) {
  content: '*';
  color: #d03050;
  margin-right: 4px;
}

:deep(.n-input) {
  border-radius: 4px;
}

:deep(.n-input__input-el) {
  font-size: 14px;
}

:deep(.n-input--textarea .n-input__input-el) {
  min-height: 80px;
  resize: vertical;
}

:deep(.n-button) {
  border-radius: 4px;
  font-size: 14px;
  padding: 0 20px;
  height: 32px;
}

:deep(.n-button--primary-type) {
  background: #8b5cf6;
  border-color: #8b5cf6;
}

:deep(.n-button--primary-type:hover) {
  background: #7c3aed;
  border-color: #7c3aed;
}

:deep(.n-button--primary-type:disabled) {
  background: #d1d5db;
  border-color: #d1d5db;
  cursor: not-allowed;
}
</style>
