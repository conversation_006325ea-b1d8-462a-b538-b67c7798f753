<script setup lang="ts">
import { NForm, type NFormItem } from 'naive-ui'
import Task from './components/task.vue'
import Conversational from './components/Conversational.vue'
import Imperative from './components/Imperative.vue'

defineOptions({
  name: 'StepTwo',
})

// 表单引用
const leftFormRef = ref<InstanceType<typeof NForm>>()
const rightFormRef = ref<InstanceType<typeof NForm>>()
const taskFormItemRef = ref<InstanceType<typeof NFormItem>>()
const taskRef = ref()

const initialValue = {
  DialogueTask: () => ({
    Name: '',
  }),
  InstructTask: () => ({
    Name: '',
    ImgUrls: [],
    HtmlFileInfo: {},
    VerificationMode: 3, // 默认文字描述
  }),
}

const formModel = ref<AgentApi.SaveTeacherOralCommunicationInput>({
  Scene: '', // 场景
  SceneDetail: '', // 场景细节
  InteractiveMode: 2, // 互动模式(directive指令式、dialogue对话式、debate辩论式)

  DialogueTasks: [
    {
      ...initialValue.DialogueTask(),
      Name: '口语交际任务1',
    },
    {
      ...initialValue.DialogueTask(),
      Name: '口语交际任务2',
    },
  ], // 对话式任务
  InstructTasks: [
    {
      ...initialValue.InstructTask(),
      Name: '口语交际任务1',
    },
    {
      ...initialValue.InstructTask(),
      Name: '口语交际任务2',
    },
  ], // 指令式任务
})

const activeState = ref({
  taskIndex: 0,
})

function checkTask() {
  if (formModel.value.InteractiveMode === 1) {
    if ((formModel.value.InstructTasks ?? []).length === 0) {
      return new Error('请添加任务')
    }
    const arr: string[] = []

    formModel.value.InstructTasks!.forEach((item, i) => {
      const base = !item.Name
        || !item.InstructContent
        || !item.VerificationMode
        || !item.Result
      if (base) {
        arr.push(`${i + 1}`)
      }
    })

    if (arr.length) {
      return new Error(`请完善第${arr.join('、')}个任务配置`)
    }
  }

  if (formModel.value.InteractiveMode === 2) {
    if ((formModel.value.DialogueTasks ?? []).length === 0) {
      return new Error('请添加任务')
    }

    const arr: string[] = []

    formModel.value.DialogueTasks!.forEach((item, i) => {
      const base = !item.Name
        || !item.ValidRespond
        || !item.DialogueTarget
      if (base) {
        arr.push(`${i + 1}`)
      }
    })

    if (arr.length) {
      return new Error(`请完善第${arr.join('、')}个任务配置`)
    }
  }
  return true
}

// 左侧表单验证规则
const leftRules = {
  Scene: {
    required: true,
    message: '请输入场景类型',
    trigger: ['blur', 'input'],
  },
  TaskField: [
    {
      required: true,
      trigger: ['change'],
      validator: (_rule: any) => {
        return checkTask()
      },
    },
  ],
}

// 互动模式选项
const interactionModeOptions = [
  { label: '指令式', value: 1 },
  { label: '对话式', value: 2 },
]

// 处理互动模式变化事件
function handleInteractiveModeChange(mode: 1 | 2 | 3) {
  if (mode === 1 && (formModel.value.InstructTasks ?? []).length === 0) {
    formModel.value.InstructTasks = [initialValue.InstructTask()]
  }
  if (mode === 2 && (formModel.value.DialogueTasks ?? []).length === 0) {
    formModel.value.DialogueTasks = [initialValue.DialogueTask()]
  }
  formModel.value.InteractiveMode = mode
  activeState.value.taskIndex = 0 // 重置下标为0

  nextTick(() => {
    taskFormItemRef.value?.restoreValidation?.()
    // 如果验证通过，清除右侧表单的验证状态
    rightFormRef.value?.restoreValidation?.()
  })
}

// 验证右侧表单
async function validateRightForm() {
  try {
    await rightFormRef.value?.validate()
    return true
  }
  catch {
    return false
  }
}

async function handleTaskAdd(name: string, call?: (res: boolean) => void) {
  // 在切换任务前验证当前右侧表单
  const isValid = await validateRightForm()
  // 调用回调函数，传递验证结果
  if (!isValid) {
    window.$message?.error('请先完成当前任务必填项填写！')
    call?.(false)
    return
  }

  call?.(true)

  if (!name) {
    return
  }

  if (formModel.value.InteractiveMode === 1) {
    formModel.value.InstructTasks!.push({
      ...initialValue.InstructTask(),
      Name: name,
    })
  }

  if (formModel.value.InteractiveMode === 2) {
    formModel.value.DialogueTasks!.push({
      ...initialValue.DialogueTask(),
      Name: name,
    })
  }

  activeState.value.taskIndex = activeState.value.taskIndex + 1 // 默认选中这个

  nextTick(() => {
    // 如果验证通过，清除右侧表单的验证状态
    rightFormRef.value?.restoreValidation?.()
  })
}

// 处理任务点击事件
async function handleTaskClick(_task: any, active: number) {
  // 在切换任务前验证当前右侧表单
  const isValid = await validateRightForm()

  // 调用回调函数，传递验证结果
  if (!isValid) {
    window.$message?.error('请先完成当前任务必填项填写！')
    return
  }

  activeState.value.taskIndex = active

  nextTick(() => {
    taskFormItemRef.value?.validate?.()
    // 如果验证通过，清除右侧表单的验证状态
    rightFormRef.value?.restoreValidation?.()
  })
}

// 暴露校验方法和表单数据给父组件
defineExpose({
  validate: async () => {
    // 校验左侧和右侧组件的表单
    const promises = []
    if (leftFormRef.value?.validate) {
      promises.push(leftFormRef.value.validate())
    }
    if (rightFormRef.value?.validate) {
      promises.push(rightFormRef.value.validate())
    }

    // 等待所有校验完成
    if (promises.length > 0) {
      await Promise.all(promises)
    }
  },
  formModel,

  initData: (data: AgentApi.SaveTeacherOralCommunicationInput) => {
    Object.keys(formModel.value).forEach((key) => {
      // eslint-disable-next-line ts/ban-ts-comment
      // @ts-expect-error
      formModel.value[key] = data[key] as any
    })
  },
})
</script>

<template>
  <div class="gap-16px bg-white">
    <NGrid responsive="screen" :x-gap="24" :y-gap="12" item-responsive>
      <!-- 左侧内容区域 -->
      <NGridItem span="24 l:12">
        <NForm
          ref="leftFormRef"
          :model="formModel"
          :rules="leftRules"
          label-placement="left"
          label-align="left"
          :label-width="80"
          size="medium"
        >
          <!-- 场景类型 -->
          <NFormItem label="场景类型" path="Scene">
            <NInput
              v-model:value="formModel.Scene"
              placeholder="请输入场景类型"
              clearable
              maxlength="100"
            />
          </NFormItem>

          <!-- 场景细节 -->
          <NFormItem label="场景细节" path="sceneDetail">
            <NInput
              v-model:value="formModel.SceneDetail"
              type="textarea"
              placeholder="请输入场景细节"
              :rows="4"
              clearable
              maxlength="1000"
              show-count
            />
          </NFormItem>

          <!-- 互动模式 -->
          <NFormItem label="互动模式" path="InteractiveMode">
            <NRadioGroup
              v-model:value="formModel.InteractiveMode"
              @update:value="handleInteractiveModeChange"
            >
              <NSpace>
                <NRadio
                  v-for="option in interactionModeOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </NRadio>
              </NSpace>
            </NRadioGroup>
          </NFormItem>

          <!-- 任务列表 -->
          <NFormItem ref="taskFormItemRef" label="任务列表" path="TaskField">
            <Task
              ref="taskRef"
              v-model:active="activeState.taskIndex"
              :tasks="
                formModel.InteractiveMode === 2
                  ? formModel.DialogueTasks
                  : formModel.InstructTasks
              "
              @update:tasks="
                (tasks: any) => {
                  if (formModel.InteractiveMode === 2) {
                    formModel!.DialogueTasks = tasks;
                  }
                  else {
                    formModel!.InstructTasks = tasks;
                  }
                }
              "
              @task-click="handleTaskClick"
              @task-add="handleTaskAdd"
            />
          </NFormItem>
        </NForm>
      </NGridItem>
      <!-- 右侧内容区域 -->
      <NGridItem span="24 l:12">
        <NForm
          ref="rightFormRef"
          :model="
            formModel.InteractiveMode === 2
              ? formModel.DialogueTasks![activeState.taskIndex]
              : formModel.InstructTasks![activeState.taskIndex]
          "
          label-placement="left"
          label-align="left"
          :label-width="110"
          size="medium"
        >
          <!-- 对话式 -->
          <template v-if="formModel.InteractiveMode === 2">
            <Conversational
              v-if="formModel.DialogueTasks![activeState.taskIndex]"
              :key="activeState.taskIndex"
              v-model:dialogue="formModel.DialogueTasks![activeState.taskIndex]"
            />
          </template>
          <template v-if="formModel.InteractiveMode === 1">
            <Imperative
              v-if="formModel.InstructTasks![activeState.taskIndex]"
              :key="activeState.taskIndex"
              v-model:directive="
                formModel.InstructTasks![activeState.taskIndex]
              "
            />
          </template>
        </NForm>
      </NGridItem>
    </NGrid>
  </div>
</template>
