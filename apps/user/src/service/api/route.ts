import { request } from '@/service/request'

/**
 * 获取常量路由
 * @returns 返回不需要权限的常量路由列表
 */
export function fetchGetConstantRoutes() {
  return request<Api.Route.MenuRoute[]>({ url: '/route/getConstantRoutes' })
}

/**
 * 获取用户路由
 * @returns 返回当前用户有权限访问的路由配置
 */
export function fetchGetUserRoutes() {
  return request<Api.Route.UserRoute>({ url: '/route/getUserRoutes' })
}

/**
 * 检查路由是否存在
 * @param routeName 路由名称
 * @returns 返回布尔值表示路由是否存在
 */
export function fetchIsRouteExist(routeName: string) {
  return request<boolean>({ url: '/route/isRouteExist', params: { routeName } })
}
