module.exports = {
  types: [
    { value: 'feat', name: 'feat:     新功能' },
    { value: 'fix', name: 'fix:      修复Bug' },
    { value: 'docs', name: 'docs:     只涉及文档更新' },
    { value: 'style', name: 'style:    修改代码风格，不影响代码含义的变更' },
    { value: 'refactor', name: '代码重构，既不修复 bug 也不添加功能的代码变更' },
    { value: 'perf', name: 'perf:     可提高性能的代码更改' },
    { value: 'test', name: 'test:     添加缺失的测试或更正现有测试' },
    { value: 'build', name: 'build:    影响构建系统或外部依赖项的更改' },
    { value: 'chore', name: 'chore:    没有修改src或测试文件的其他变更' },
    { value: 'revert', name: 'revert:   回滚之前的提交' },
  ],
  scopes: '',
  usePreparedCommit: false,
  allowTicketNumber: false,
  isTicketNumberRequired: false,
  ticketNumberPrefix: 'TICKET-',
  ticketNumberRegExp: '\\d{1,5}',
  skipQuestions: ['body'],
  messages: {
    type: '选择您要进行的更改类型\'重新提交:',
    scope: '\n表示此更改的范围(可选):',
    customScope: '表示此更改的范围:',
    subject: '写一个简短的，祈使句式的变化描述(必选):\n',
    body: '提供更长的变更描述(可选)。使用"|"换行:\n',
    breaking: '列出任何重大更改(可选):\n',
    footer: '列出此更改关闭的任何问题(可选):\n',
    confirmCommit: '您确定要继续执行上面的提交吗?',
  },
  allowCustomScopes: true,
  allowBreakingChanges: ['feat', 'fix'],
  subjectLimit: 100,
}
