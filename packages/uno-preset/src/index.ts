// @unocss-include  // 标记此文件包含UnoCSS规则

import type { Preset } from '@unocss/core'
import type { Theme } from '@unocss/preset-uno'

// 导出presetSoybeanAdmin函数，创建一个UnoCSS预设
export function presetSoybeanAdmin(): Preset<Theme> {
  const preset: Preset<Theme> = {
    name: 'preset-soybean-admin', // 预设名称
    shortcuts: [ // 定义快捷类名组合
      // 第一组：flex布局相关快捷方式
      {
        'flex-center': 'flex justify-center items-center', // 水平垂直居中
        'flex-x-center': 'flex justify-center', // 水平居中
        'flex-y-center': 'flex items-center', // 垂直居中
        'flex-col': 'flex flex-col', // 纵向flex布局
        'flex-col-center': 'flex-center flex-col', // 纵向flex且居中
        'flex-col-stretch': 'flex-col items-stretch', // 纵向flex且拉伸子项
        'i-flex-center': 'inline-flex justify-center items-center', // 行内flex居中
        'i-flex-x-center': 'inline-flex justify-center', // 行内flex水平居中
        'i-flex-y-center': 'inline-flex items-center', // 行内flex垂直居中
        'i-flex-col': 'flex-col inline-flex', // 行内纵向flex
        'i-flex-col-center': 'flex-col i-flex-center', // 行内纵向flex居中
        'i-flex-col-stretch': 'i-flex-col items-stretch', // 行内纵向flex拉伸子项
        'flex-1-hidden': 'flex-1 overflow-hidden', // flex:1且隐藏溢出
      },
      // 第二组：定位相关快捷方式
      {
        'absolute-lt': 'absolute left-0 top-0', // 绝对定位左上角
        'absolute-lb': 'absolute left-0 bottom-0', // 绝对定位左下角
        'absolute-rt': 'absolute right-0 top-0', // 绝对定位右上角
        'absolute-rb': 'absolute right-0 bottom-0', // 绝对定位右下角
        'absolute-tl': 'absolute-lt', // 同absolute-lt
        'absolute-tr': 'absolute-rt', // 同absolute-rt
        'absolute-bl': 'absolute-lb', // 同absolute-lb
        'absolute-br': 'absolute-rb', // 同absolute-rb
        'absolute-center': 'absolute-lt flex-center size-full', // 绝对定位居中(全屏)
        'fixed-lt': 'fixed left-0 top-0', // 固定定位左上角
        'fixed-lb': 'fixed left-0 bottom-0', // 固定定位左下角
        'fixed-rt': 'fixed right-0 top-0', // 固定定位右上角
        'fixed-rb': 'fixed right-0 bottom-0', // 固定定位右下角
        'fixed-tl': 'fixed-lt', // 同fixed-lt
        'fixed-tr': 'fixed-rt', // 同fixed-rt
        'fixed-bl': 'fixed-lb', // 同fixed-lb
        'fixed-br': 'fixed-rb', // 同fixed-rb
        'fixed-center': 'fixed-lt flex-center size-full', // 固定定位居中(全屏)
      },
      // 第三组：文本溢出处理
      {
        'nowrap-hidden': 'overflow-hidden whitespace-nowrap', // 不换行且隐藏溢出
        'ellipsis-text': 'nowrap-hidden text-ellipsis', // 单行文本溢出显示省略号
      },
    ],
  }

  return preset
}

// 默认导出presetSoybeanAdmin函数
export default presetSoybeanAdmin
