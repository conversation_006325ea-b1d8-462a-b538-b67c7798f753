<script setup lang="tsx">
import { NButton } from 'naive-ui'

defineOptions({
  name: 'ScoreDistributionTable',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

export interface ScoreTableItem {
  studentName: string
  studentId: string
  grade: string
  action: string
}

interface Props {
  /** 成绩分布表格数据 */
  scoreTableData: ScoreTableItem[]
  /** 表格标题 */
  title?: string
  /** 是否显示分页 */
  pagination?: boolean
  /** 表格大小 */
  size?: 'small' | 'medium' | 'large'
}

interface Emits {
  /** 查看明细事件 */
  (e: 'viewDetail', row: ScoreTableItem): void
}

// 表格列配置
const columns = [
  {
    title: '学生姓名',
    key: 'studentName',
    align: 'left' as const,
    width: 80,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '学号',
    key: 'studentId',
    align: 'center' as const,
    width: 60,
  },
  {
    title: '等第',
    key: 'grade',
    align: 'center' as const,
    width: 60,
    render: (row: ScoreTableItem) => {
      return (
        <span>
          {row.grade}
        </span>
      )
    },
  },
  {
    title: '操作',
    key: 'action',
    align: 'center' as const,
    width: 70,
    render: (row: ScoreTableItem) => {
      return (
        <NButton type="primary" text size="tiny" onClick={() => handleViewDetail(row)}>
          查看明细
        </NButton>
      )
    },
  },
]

// 处理查看明细
function handleViewDetail(row: ScoreTableItem) {
  emit('viewDetail', row)
}
</script>

<template>
  <NDataTable
    :columns="columns"
    :data="props.scoreTableData"
    :size="size || 'small'"
    :bordered="false"
    :single-line="false"
    :max-height="400"
    class="score-table"
  />
</template>
