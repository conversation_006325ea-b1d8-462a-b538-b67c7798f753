declare namespace AuthApi {

  interface RoleIdList {
    RoleId: string
    RoleName: string
    SchoolInfo: any[]
  }

  interface Subject {
    Id: string
    Name: string
  }

  interface UserInfo {
    RoleIdList: RoleIdList[]
    userName: string
    roles: string[]
    buttons: string[]
    userId: string
    schoolId: string
    subjectList: Subject[]
    classList: AgentApi.GetTeachClassReponse[]
  }

}
