import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useContext } from '@sa/hooks'
import { useRouteStore } from '@/store/modules/route'

// 创建混合菜单上下文
export const { setupStore: setupMixMenuContext, useStore: useMixMenuContext } = useContext('mix-menu', useMixMenu)

// 混合菜单逻辑
function useMixMenu() {
  const route = useRoute()
  const routeStore = useRouteStore()
  const { selectedKey } = useMenu()

  // 当前激活的一级菜单key
  const activeFirstLevelMenuKey = ref('')

  // 设置激活的一级菜单key
  function setActiveFirstLevelMenuKey(key: string) {
    activeFirstLevelMenuKey.value = key
  }

  // 获取激活的一级菜单key
  function getActiveFirstLevelMenuKey() {
    const [firstLevelRouteName] = selectedKey.value.split('_')
    setActiveFirstLevelMenuKey(firstLevelRouteName!)
  }

  // 所有菜单
  const allMenus = computed<App.Global.Menu[]>(() => routeStore.menus)

  // 一级菜单（去除children属性）
  const firstLevelMenus = computed<App.Global.Menu[]>(() =>
    routeStore.menus.map((menu) => {
      const { children: _, ...rest } = menu
      return rest
    }),
  )

  // 当前激活菜单的子菜单
  const childLevelMenus = computed<App.Global.Menu[]>(
    () => routeStore.menus.find(menu => menu.key === activeFirstLevelMenuKey.value)?.children || [],
  )

  // 判断当前激活的一级菜单是否有子菜单
  const isActiveFirstLevelMenuHasChildren = computed(() => {
    if (!activeFirstLevelMenuKey.value) {
      return false
    }
    const findItem = allMenus.value.find(item => item.key === activeFirstLevelMenuKey.value)
    return Boolean(findItem?.children?.length)
  })

  // 监听路由变化，更新激活菜单
  watch(
    () => route.name,
    () => {
      getActiveFirstLevelMenuKey()
    },
    { immediate: true },
  )

  return {
    allMenus,
    firstLevelMenus,
    childLevelMenus,
    isActiveFirstLevelMenuHasChildren,
    activeFirstLevelMenuKey,
    setActiveFirstLevelMenuKey,
    getActiveFirstLevelMenuKey,
  }
}

// 菜单相关逻辑
export function useMenu() {
  const route = useRoute()

  // 计算当前选中的菜单key
  const selectedKey = computed(() => {
    const { hideInMenu, activeMenu } = route.meta
    const name = route.name as string
    const routeName = (hideInMenu ? activeMenu : name) || name
    return routeName
  })

  return {
    selectedKey,
  }
}
