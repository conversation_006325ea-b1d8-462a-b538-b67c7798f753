import { computed, reactive, ref } from 'vue'
import type { Ref } from 'vue'
import { jsonClone } from '@sa/utils'
import useBoolean from './use-boolean'
import useLoading from './use-loading'

/**
 * 可能为Promise的类型
 * @template T 值类型
 */
export type MaybePromise<T> = T | Promise<T>

/**
 * API函数类型
 */
export type ApiFn = (args: any) => Promise<unknown>

/**
 * 表格列勾选配置
 */
export interface TableColumnCheck {
  key: string // 列唯一标识
  title: string // 列标题
  checked: boolean // 是否勾选
}

/**
 * 带索引的表格数据类型
 * @template T 数据类型
 */
export type TableDataWithIndex<T> = T & { index: number }

/**
 * 转换后的表格数据结构
 * @template T 数据类型
 */
export interface TransformedData<T> {
  data: TableDataWithIndex<T>[] // 表格数据
  pageNum: number // 当前页码
  pageSize: number // 每页条数
  total: number // 总条数
}

/**
 * 数据转换函数类型
 * @template T 数据类型
 * @template Response API响应类型
 */
export type Transformer<T, Response> = (response: Response) => TransformedData<T>

/**
 * 表格配置接口
 * @template A API函数类型
 * @template T 数据类型
 * @template C 列类型
 */
export interface TableConfig<A extends ApiFn, T, C> {
  apiFn: A // 获取表格数据的API函数
  apiParams?: Parameters<A>[0] // API参数
  transformer: Transformer<T, Awaited<ReturnType<A>>> // 数据转换函数
  columns: () => C[] // 列配置工厂函数
  getColumnChecks: (columns: C[]) => TableColumnCheck[] // 获取列勾选配置
  getColumns: (columns: C[], checks: TableColumnCheck[]) => C[] // 获取实际显示的列
  onFetched?: (transformed: TransformedData<T>) => MaybePromise<void> // 数据获取后回调
  immediate?: boolean // 是否立即获取数据
}

/**
 * 表格钩子函数
 * @template A API函数类型
 * @template T 数据类型
 * @template C 列类型
 * @param config 表格配置
 */
export default function useHookTable<A extends ApiFn, T, C>(config: TableConfig<A, T, C>) {
  // 加载状态
  const { loading, startLoading, endLoading } = useLoading()
  // 空状态
  const { bool: empty, setBool: setEmpty } = useBoolean()

  const { apiFn, apiParams, transformer, immediate = true, getColumnChecks, getColumns } = config

  // 搜索参数(响应式)
  const searchParams: NonNullable<Parameters<A>[0]> = reactive(jsonClone({ ...apiParams }))

  // 所有列配置
  const allColumns = ref(config.columns()) as Ref<C[]>

  // 表格数据
  const data: Ref<TableDataWithIndex<T>[]> = ref([])

  // 列勾选状态
  const columnChecks: Ref<TableColumnCheck[]> = ref(getColumnChecks(config.columns()))

  // 实际显示的列(计算属性)
  const columns = computed(() => getColumns(allColumns.value, columnChecks.value))

  /**
   * 重新加载列配置
   */
  function reloadColumns() {
    allColumns.value = config.columns()

    const checkMap = new Map(columnChecks.value.map(col => [col.key, col.checked]))

    const defaultChecks = getColumnChecks(allColumns.value)

    columnChecks.value = defaultChecks.map(col => ({
      ...col,
      checked: checkMap.get(col.key) ?? col.checked,
    }))
  }

  /**
   * 获取表格数据
   */
  async function getData() {
    startLoading()

    const formattedParams = formatSearchParams(searchParams)

    const response = await apiFn(formattedParams)

    const transformed = transformer(response as Awaited<ReturnType<A>>)

    data.value = transformed.data

    setEmpty(transformed.data.length === 0)

    await config.onFetched?.(transformed)

    endLoading()
  }

  /**
   * 格式化搜索参数
   * @param params 原始参数
   */
  function formatSearchParams(params: Record<string, unknown>) {
    const formattedParams: Record<string, unknown> = {}

    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        formattedParams[key] = value
      }
    })

    return formattedParams
  }

  /**
   * 更新搜索参数
   * @param params 新参数
   */
  function updateSearchParams(params: Partial<Parameters<A>[0]>) {
    Object.assign(searchParams, params)
  }

  /**
   * 重置搜索参数
   */
  function resetSearchParams() {
    Object.assign(searchParams, jsonClone(apiParams))
  }

  // 立即获取数据
  if (immediate) {
    getData()
  }

  return {
    loading,
    empty,
    data,
    columns,
    columnChecks,
    reloadColumns,
    getData,
    searchParams,
    updateSearchParams,
    resetSearchParams,
  }
}
