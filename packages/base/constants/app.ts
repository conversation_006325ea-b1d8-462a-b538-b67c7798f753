// 导入工具函数
import { transformRecordToOption } from '@sa/utils'

// 全局菜单ID常量
export const GLOBAL_HEADER_MENU_ID = '__GLOBAL_HEADER_MENU__'
export const GLOBAL_SIDER_MENU_ID = '__GLOBAL_SIDER_MENU__'

// 主题模式配置
export const themeSchemaRecord: Record<UnionKey.ThemeScheme, string> = {
  light: '亮色模式',
  dark: '暗黑模式',
  auto: '跟随系统',
}

// 主题模式选项
export const themeSchemaOptions = transformRecordToOption(themeSchemaRecord)

// 登录模块配置
export const loginModuleRecord: Record<UnionKey.LoginModule, string> = {
  'pwd-login': 'page.login.pwdLogin.title', // 密码登录
  'code-login': 'page.login.codeLogin.title', // 验证码登录
  'qrcode-login': 'page.login.qrcodeLogin.title', // 二维码登录
  'reset-pwd': 'page.login.resetPwd.title', // 重置密码
}

// 布局模式配置
export const themeLayoutModeRecord: Record<UnionKey.ThemeLayoutMode, string> = {
  'vertical': '左侧菜单模式',
  'vertical-mix': '左侧菜单混合模式',
  'horizontal': '顶部菜单模式',
  'horizontal-mix': '顶部菜单混合模式',
}

export const themeLayoutModeOptions = transformRecordToOption(themeLayoutModeRecord)

// 滚动模式配置
export const themeScrollModeRecord: Record<UnionKey.ThemeScrollMode, string> = {
  wrapper: '外层滚动',
  content: '主体滚动',
}

export const themeScrollModeOptions = transformRecordToOption(themeScrollModeRecord)

// 标签页模式配置
export const themeTabModeRecord: Record<UnionKey.ThemeTabMode, string> = {
  chrome: '谷歌风格',
  button: '按钮风格',
}
export const themeTabModeOptions = transformRecordToOption(themeTabModeRecord)

// 页面动画模式配置
export const themePageAnimationModeRecord: Record<UnionKey.ThemePageAnimateMode, string> = {
  'fade-slide': '滑动',
  'fade': '淡入淡出',
  'fade-bottom': '底部消退',
  'fade-scale': '缩放消退',
  'zoom-fade': '渐变',
  'zoom-out': '闪现',
  'none': '无',
}
export const themePageAnimationModeOptions = transformRecordToOption(themePageAnimationModeRecord)

// 缓存重置策略配置
export const resetCacheStrategyRecord: Record<UnionKey.ResetCacheStrategy, string> = {
  close: '关闭页面',
  refresh: '刷新页面',
}

export const resetCacheStrategyOptions = transformRecordToOption(resetCacheStrategyRecord)

// 暗黑模式类名
export const DARK_CLASS = 'dark'
