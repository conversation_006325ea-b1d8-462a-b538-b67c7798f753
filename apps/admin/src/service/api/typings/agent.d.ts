declare namespace AgentApi {
  interface AgentTeacherHomePageListInput {
    /** 教师Id */
    teacherId?: string
    /** 学校ID */
    schoolId?: string
    /** 学科Id */
    subjectId?: string
  }

  interface AgentTeacherHomePageListOutPut {
    /** 智能体_教师首页父级类型 */
    ParentTypes?: AgentTeacherHomePageParentType[]
  }

  interface AgentTeacherHomePageParentType {
    /** 类型Id */
    Id?: string
    /** 类型名称 */
    Title?: string
    /** 智能体_教师首页子级类型 */
    ChildrenTypes?: AgentTeacherHomePageChildrenType[]
  }

  interface AgentTeacherHomePageChildrenType {
    /** 类型Id */
    Id?: string
    /** 类型名称 */
    Title?: string
    /** 智能体信息 */
    AgentInfo?: AgentTeacherHomePageAgentInfo[]
  }

  interface AgentTeacherHomePageAgentInfo {
    /** 智能体Id */
    Id?: string
    /** 智能体名称 */
    AgentName?: string
    /** 智能体应用编码 */
    AgentBotCode?: string
    /** 智能体类型 */
    AgentTypeId?: string
    /** 智能体概述 */
    Summarize?: string
    /** 图标Logo */
    Logo?: string
    /** 创建时间 */
    CreateTime?: string
    /** 是否收藏 */
    IsCollection?: boolean
  }

  interface SaveTeacherOralCommunicationInput {
    /** 智能体任务Id */
    Id?: string
    /** 智能体Id */
    AgentId?: string
    /** 名称 */
    Name?: string
    /** 项目背景介绍 */
    Introduce?: string
    /** 章节Id */
    ChapterId?: string
    /** 教学目标 */
    Target?: string
    /** 评价体系Id */
    EvaluateId?: string
    /** 开场白 */
    Prologue?: string
    /** 场景 */
    Scene?: string
    /** 场景细节 */
    SceneDetail?: string
    /** 互动模式(1指令式、2对话式、3辩论式) */
    InteractiveMode?: 1 | 2 | 3
    /** 教师Id */
    TeacherId?: string
    /** 学校Id */
    SchoolId?: string
    /** 发布班级Id */
    ClassId?: string[]
    /** 发布时间范围(下标0开始、下标1结束) */
    TimeRange?: [string, string]
    /** 学科Id */
    SubjectId?: string
    /** 对话式任务 */
    DialogueTasks?: OralCommunicationDialogueTaskInput[]
    /** 指令式任务 */
    InstructTasks?: OralCommunicationInstructTaskInput[]
  }

  interface OralCommunicationDialogueTaskInput {
    /** Id */
    Id?: string
    /** 任务名称 */
    Name?: string
    /** 对话目标 */
    DialogueTarget?: string
    /** 有效回应标准 */
    ValidRespond?: string
    /** 追问话术 */
    Asked?: string
    /** 是否编辑中 本地使用 */
    IsEditing?: boolean
  }

  interface ImgInput {
    /** 图片地址 */
    ImgUrl?: string
    /** 1本地上传、2AI生成 */
    Type?: number
    /** 上传状态 - 由上传组件使用 */
    status?: 'uploading' | 'finished' | 'error'
  }

  interface AIFileInfoDto {
    /** ID */
    Id?: string
    /** 文件名称 */
    Name?: string
    /** 文件地址 */
    Url?: string
    /** 创建时间 */
    CreateTime?: string

    /** 是否是html代码 */
    _isHtmlCode?: boolean
  }

  interface AIGenerateHTMLCodeOutput {
    /** ID */
    Id?: string
    /** 名称 */
    Name?: string
    /** HTML代码 */
    HtmlCode?: string
    /** 创建时间 */
    CreateTime?: string
  }

  interface OralCommunicationInstructTaskInput {
    /** Id */
    Id?: string
    /** 任务名称 */
    Name?: string
    /** 指令内容 */
    InstructContent?: string
    /** 验证方式（1图片、2动画(Html文件)、3文本) */
    VerificationMode?: number | null
    /** 图片地址 */
    ImgUrls?: ImgInput[]
    HtmlFileInfo?: AIFileInfoDto
    /** 成果要求 */
    Result?: string

    /** 是否编辑中 本地使用 */
    IsEditing?: boolean
  }
  //
  interface GetTeachClassReponse {
    /** 班级名称 */
    ClassName: string
    /** 班级Id */
    ClassId: string
    /** 年级 */
    Grade: number
    /** 学校ID */
    SchoolId: string
    /** 学校名称 */
    SchoolName: string
  }
  interface GradeResponse {
    Id: string
    Gradename: string
  }
  interface YearSemesterResponse {
    Years: number[]
    NowTerm: number | null
    NowYear: number | null
  }
  interface ChapterResponse {
    ChapterId: string
    ChapterName: string
    ParentId: string
    Second: ChapterResponse[]
  }
  // 智能体->教师端口语交际列表
  interface OralCommunicationListReponse {
    TotalCount: number
    Datas: OralCommunicationListOutput[]
  }
  interface OralCommunicationListOutput {
    /** 智能体任务Id */
    Id: string
    /** 名称 */
    Name: string
    /** 项目背景介绍 */
    Introduce: string
    /** 互动模式(1指令式、2对话式、3辩论式) */
    InteractiveMode: number
    /** 章节Id */
    ChapterId: string
    /** 章节名称 */
    ChapterName: string
    /** 开始时间 */
    BeginTime: string
    /** 结束时间 */
    EndTime: string
    /** 创建时间 */
    CreateTime: string
    /** 发布的班级 */
    ClassName: string
    /** Logo */
    Logo: string
  }
  interface OralCommunicationListRequest {
    PageIndex: number
    PageSize: number
    AgentId: string
    Name: string
    InteractiveMode: number | string
    ClassId: string

    SubjectId?: string
    TeacherId?: string
    SchoolId?: string

  }

  interface AgentOralCommunicationDetailOutput {
    /** 是否存在问答数据 */
    IsData?: boolean
    OralCommunicationInfo?: SaveTeacherOralCommunicationInput
  }

  interface GetOralCommunicationAnalyseInput {
    /** 智能体任务Id */
    AgentTaskId?: string
    /** 班级Id */
    ClassId?: string
  }

  interface GetOralCommunicationAnalyseLevelOutput {
    /** 等第名称 */
    LevelName: string
    /** 等第数量 */
    LevelCount: number
  }

  interface GetOralCommunicationAnalyseScoreOutput {
    /** 学生Id */
    StudentId: string
    /** 学生学号 */
    StudentNum: string
    /** 学生姓名 */
    StudentName: string
    /** 学生等第 */
    StudentLevel: string
  }

  interface GetOralCommunicationAnalyseOutput {
    /** 智能体Id */
    AgentId: string
    /** 智能体任务Id */
    AgentTaskId: string
    /** 智能体任务名称 */
    AgentTaskName: string
    /** 智能体任务项目背景介绍 */
    AgentTaskIntroduce: string
    /** 智能体Logo */
    Logo: string
    /** 互动模式 */
    InteractiveMode: string
    /** 提交人数 */
    SubmitCount: number
    /** 平均等第 */
    AvgLevel: string
    /** 等第分布 */
    LevelList: GetOralCommunicationAnalyseLevelOutput[]
    /** 成绩分布 */
    ScoreList: GetOralCommunicationAnalyseScoreOutput[]
  }

  /**
   * 获取AI对话内容记录
   */
  interface AgentStudentOralCommunicationDialogueRequest {
    /**
     * 页码（默认1）
     */
    PageIndex: number
    /**
     * 每页数量（默认10）
     */
    PageSize: number
    /**
     * 智能体Id
     */
    AgentId: string
    /**
     * 智能体任务Id
     */
    AgentTaskId: string
    /**
     * 学生Id
     */
    StudentId: string
    /**
     * 类型（0智能体）
     */
    Type: 0
  }

  interface AgentTeacherHomePageAgentTaskOuputResponse {
    /**
     * 数据集
     */
    Datas: AgentTeacherHomePageAgentTaskOuput[] | null
    /**
     * 数据总量
     */
    TotalCount: number
  }

  interface AgentTeacherHomePageAgentTaskOuput {
    /**
     * 会话Id
     */
    Id: string
    /**
     * 问
     */
    Ask: string
    AskInfo: AgentModelAIAIDialogueASKDto
    /**
     * 答
     */
    Answer: string
    /**
     * 消息类型(1系统消息、2用户消息)
     */
    MessageType: 1 | 2
    /**
     * 创建时间
     */
    CreateTime: string
  }

  interface AgentModelAIAIDialogueASKDto {
    /**
     * “问”文本
     */
    AskText?: null | string
    AudioFile?: AgentModelAIAIDialogueASKAudioFileInfo | null
    /**
     * 文件消息
     */
    Files?: AgentModelAIAIDialogueASKFileInfo[] | null
  }

  interface AgentModelAIAIDialogueASKAudioFileInfo {
    /**
     * 语音地址
     */
    AudioUrl: string
    /**
     * 时长（单位:秒）
     */
    Duration: number
  }

  interface AgentModelAIAIDialogueASKFileInfo {
    /**
     * 文件名称
     */
    FileName: string
    /**
     * 文件大小
     */
    FileSize: string
    /**
     * 文件类型
     */
    FileType: string
    /**
     * 文件地址
     */
    FileUrl: string
  }

  interface GetOralCommunicationStudentResultInput {
    /** 智能体任务Id */
    AgentTaskId: string
    /** 学生Id */
    StudentId: string
  }

  interface GetOralCommunicationStudentResultOutput {
    /** 智能体任务名称 */
    AgentTaskName: string
    /** 智能体任务项目背景介绍 */
    AgentTaskIntroduce: string
    /** 学生评估结果 */
    AssessmentResult: string
    /** 类型 */
    InteractiveMode: string
    /** 智能体LOGO */
    Logo: string
    /**
     * 学生头像
     */
    StudentLogo: string
    /**
     * 学生姓名
     */
    StudentName: string
  }
}
