import { computed, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { defineStore } from 'pinia'
import { useLoading, useRouterPush } from '@sa/hooks'
import { localStg } from '@sa/utils'
import { SetupStoreId } from '@sa/enum'
import { useRouteStore } from '../route'
import { useTabStore } from '../tab'
import { clearAuthStorage, getToken } from './shared'
import { fetchGetSubjectListByStudentResponse, fetchGetUserInfo, fetchLogin } from '@/service/api'

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute()
  const routeStore = useRouteStore()
  const tabStore = useTabStore()
  const { toLogin, redirectFromLogin } = useRouterPush(false)
  const { loading: loginLoading, startLoading, endLoading } = useLoading()

  const token = ref(getToken())
  const userInfo: Auth.UserInfo = reactive({
    userName: '',
    userId: '',
    ClassId: '',
    ClassName: '',
    GradeId: '',
    IsDZBQX: 0,
    SchoolId: '',
    SchoolName: '',
    roles: [],
    buttons: [],
    Photo: '',
  })

  // 学科数据状态
  const subjectState = reactive<{
    list: Dictionary.GetSubjectListByStudentResponse[]
    currentSubjectId: string
  }>({
    list: [],
    currentSubjectId: '',
  })

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE)
  })

  /** Is login */
  const isLogin = computed(() => Boolean(token.value))

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore()

    clearAuthStorage()

    authStore.$reset()

    if (!route.meta.constant) {
      await toLogin()
    }

    tabStore.cacheTabs()
    routeStore.resetStore()
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param [redirect] Whether to redirect after login. Default is `true`
   */
  async function login(userName: string, password: string, redirect = true) {
    startLoading()

    const { data: loginToken, error } = await fetchLogin(userName, password)

    if (!error) {
      const pass = await loginByToken(loginToken)

      if (pass) {
        await redirectFromLogin(redirect)

        window.$notification?.success({
          title: '登录成功',
          content: `欢迎回来，${userInfo.userName} !`,
          duration: 4500,
        })
      }
    }
    else {
      resetStore()
    }

    endLoading()
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token)
    localStg.set('refreshToken', loginToken.refreshToken || '')

    // 2. get user info
    const pass = await getUserInfo()

    if (pass) {
      token.value = loginToken.token

      return true
    }

    return false
  }

  async function getUserInfo() {
    const { data: info, error } = await fetchGetUserInfo()

    if (!error) {
      // update store
      Object.assign(userInfo, info)

      // 获取用户信息成功后，获取学科列表
      await getSubjectList()

      return true
    }

    return false
  }

  // 获取学科列表
  async function getSubjectList() {
    if (!userInfo.userId) {
      return false
    }

    const { data, error } = await fetchGetSubjectListByStudentResponse({
      subject: '-1', // 学科缩写，可以为空获取所有学科
      userid: userInfo.userId,
    })

    if (!error) {
      // 在数据前面添加"全部"选项
      data.unshift({
        Id: '',
        Name: '全部',
      })
      subjectState.list = data
      // 如果有数据且没有选中的学科，默认选择第一个
      if (data.length > 0) {
        if (!localStg.get('defaultSubjectId')) {
          subjectState.currentSubjectId = data[0].Id
          localStg.set('defaultSubjectId', data[0].Id)
        }
        else {
          subjectState.currentSubjectId = localStg.get('defaultSubjectId')!
        }
      }
    }
  }

  // 设置当前选中的学科
  function setCurrentSubject(subjectId: string) {
    subjectState.currentSubjectId = subjectId
    localStg.set('defaultSubjectId', subjectId)
    window.location.reload()
  }

  async function initUserInfo() {
    const hasToken = getToken()

    if (hasToken) {
      const pass = await getUserInfo()

      if (!pass) {
        resetStore()
      }
    }
  }

  return {
    token,
    userInfo,
    subjectState,
    isStaticSuper,
    isLogin,
    loginLoading,
    resetStore,
    login,
    initUserInfo,
    loginByToken,
    getSubjectList,
    setCurrentSubject,
  }
})
