// 导入SVG图标渲染Hook和组件
import { useSvgIconRender } from '@sa/hooks'
import SvgIcon from '@sa/components/custom/svg-icon.vue'

type IconReturnType = ReturnType<typeof useSvgIconRender>

/**
 * SVG图标Hook
 * @returns 包含SVG图标VNode渲染函数的对象
 */
export function useSvgIcon(): {
  SvgIconVNode: IconReturnType['SvgIconVNode']
} {
  // 使用SVG图标渲染Hook，传入SVG图标组件
  const { SvgIconVNode } = useSvgIconRender(SvgIcon)

  // 返回可复用的SVG图标渲染函数
  return {
    SvgIconVNode, // SVG图标的VNode渲染函数
  }
}
