/** 存储命名空间 */
declare namespace StorageType {
  interface Session {
    /** 主题颜色 */
    themeColor: string
    // /**
    //  * 主题设置
    //  */
    // themeSettings: App.Theme.ThemeSetting;
  }

  interface Local {
    /** 令牌 */
    token: string
    /** 固定混合菜单的侧边栏 */
    mixSiderFixed: CommonType.YesOrNo
    /** 刷新令牌 */
    refreshToken: string
    /** 主题颜色 */
    themeColor: string
    /** 深色模式 */
    darkMode: boolean
    /** 主题设置 */
    themeSettings: App.Theme.ThemeSetting
    /**
     * 覆盖主题标志
     *
     * 值为项目的构建时间
     */
    overrideThemeFlag: string
    /** 全局标签 */
    globalTabs: App.Global.Tab[]
    /** 在移动设备之前备份的主题设置 */
    backupThemeSettingBeforeIsMobile: {
      layout: UnionKey.ThemeLayoutMode
      siderCollapse: boolean
    }

    defaultSubjectId: string
  }
}
