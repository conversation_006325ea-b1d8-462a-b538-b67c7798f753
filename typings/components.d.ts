/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {};

/* prettier-ignore */
declare module "vue" {
  export interface GlobalComponents {
    NBackTop: typeof import("naive-ui")["NBackTop"];
    NBreadcrumb: typeof import("naive-ui")["NBreadcrumb"];
    NBreadcrumbItem: typeof import("naive-ui")["NBreadcrumbItem"];
    NButton: typeof import("naive-ui")["NButton"];
    NCard: typeof import("naive-ui")["NCard"];
    NCheckbox: typeof import("naive-ui")["NCheckbox"];
    NCollapse: typeof import("naive-ui")["NCollapse"];
    NCollapseItem: typeof import("naive-ui")["NCollapseItem"];
    NColorPicker: typeof import("naive-ui")["NColorPicker"];
    NDataTable: typeof import("naive-ui")["NDataTable"];
    NDatePicker: typeof import("naive-ui")["NDatePicker"];
    NDialogProvider: typeof import("naive-ui")["NDialogProvider"];
    NDivider: typeof import("naive-ui")["NDivider"];
    NDrawer: typeof import("naive-ui")["NDrawer"];
    NDrawerContent: typeof import("naive-ui")["NDrawerContent"];
    NDropdown: typeof import("naive-ui")["NDropdown"];
    NDynamicInput: typeof import("naive-ui")["NDynamicInput"];
    NEmpty: typeof import("naive-ui")["NEmpty"];
    NForm: typeof import("naive-ui")["NForm"];
    NFormItem: typeof import("naive-ui")["NFormItem"];
    NFormItemGi: typeof import("naive-ui")["NFormItemGi"];
    NGrid: typeof import("naive-ui")["NGrid"];
    NGridItem: typeof import("naive-ui")["NGridItem"];
    NInput: typeof import("naive-ui")["NInput"];
    NInputGroup: typeof import("naive-ui")["NInputGroup"];
    NInputNumber: typeof import("naive-ui")["NInputNumber"];
    NLoadingBarProvider: typeof import("naive-ui")["NLoadingBarProvider"];
    NMenu: typeof import("naive-ui")["NMenu"];
    NMessageProvider: typeof import("naive-ui")["NMessageProvider"];
    NModal: typeof import("naive-ui")["NModal"];
    NNotificationProvider: typeof import("naive-ui")["NNotificationProvider"];
    NPopconfirm: typeof import("naive-ui")["NPopconfirm"];
    NPopover: typeof import("naive-ui")["NPopover"];
    NQrCode: typeof import("naive-ui")["NQrCode"];
    NRadio: typeof import("naive-ui")["NRadio"];
    NRadioGroup: typeof import("naive-ui")["NRadioGroup"];
    NResult: typeof import("naive-ui")["NResult"];
    NScrollbar: typeof import("naive-ui")["NScrollbar"];
    NSelect: typeof import("naive-ui")["NSelect"];
    NSpace: typeof import("naive-ui")["NSpace"];
    NSpin: typeof import("naive-ui")["NSpin"];
    NStep: typeof import("naive-ui")["NStep"];
    NSteps: typeof import("naive-ui")["NSteps"];
    NSwitch: typeof import("naive-ui")["NSwitch"];
    NTab: typeof import("naive-ui")["NTab"];
    NTable: typeof import("naive-ui")["NTable"];
    NTabPane: typeof import("naive-ui")["NTabPane"];
    NTabs: typeof import("naive-ui")["NTabs"];
    NTabsPane: typeof import("naive-ui")["NTabsPane"];
    NTag: typeof import("naive-ui")["NTag"];
    NTooltip: typeof import("naive-ui")["NTooltip"];
    NTree: typeof import("naive-ui")["NTree"];
    NUpload: typeof import("naive-ui")["NUpload"];
    NUploadDragger: typeof import("naive-ui")["NUploadDragger"];
    NWatermark: typeof import("naive-ui")["NWatermark"];
    RouterLink: typeof import("vue-router")["RouterLink"];
    RouterView: typeof import("vue-router")["RouterView"];
  }
}
