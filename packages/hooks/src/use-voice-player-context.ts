import useContext from './use-context'
import useVoicePlayer from './use-voice-player'
import type { VoicePlayerCallbacks, VoicePlayerOptions } from './use-voice-player'

/**
 * 语音播放上下文配置
 */
export interface VoicePlayerContextConfig {
  /** 语音播放选项 */
  options?: VoicePlayerOptions
  /** 语音播放回调 */
  callbacks?: VoicePlayerCallbacks
}

/**
 * 创建语音播放上下文
 * 使用项目的useContext模式来创建provider和inject
 */
export const { setupStore: setupVoicePlayerContext, useStore: useVoicePlayerContext } = useContext(
  'voice-player',
  (config: VoicePlayerContextConfig = {}) => {
    const { options = {}, callbacks = {} } = config

    // 创建语音播放器实例
    const voicePlayer = useVoicePlayer(options, callbacks)

    return {
      ...voicePlayer,
      // 可以在这里添加额外的上下文特定方法
    }
  },
)

/**
 * 语音播放上下文Hook
 * 提供便捷的方式来设置和使用语音播放上下文
 */
export function useVoicePlayerProvider(config: VoicePlayerContextConfig = {}) {
  return setupVoicePlayerContext(config)
}

/**
 * 语音播放上下文消费Hook
 * 在子组件中使用语音播放功能
 */
export function useVoicePlayerConsumer() {
  const context = useVoicePlayerContext()

  if (!context) {
    throw new Error('useVoicePlayerConsumer must be used within a VoicePlayerProvider')
  }

  return context
}
