/**
 * 模拟文件上传服务
 * 用于开发和测试环境
 */
/** 上传结果 */
interface UploadResult {
  /** 文件ID */
  fileId: string
  /** 文件名称 */
  fileName: string
  /** 文件大小 */
  fileSize: number
  /** 文件类型 */
  fileType: string
  /** 文件URL */
  fileUrl: string
  /** 缩略图URL（可选） */
  thumbUrl?: string
  /** 上传时间 */
  uploadTime: string
}
/**
 * 模拟上传文件接口
 * @param file 要上传的文件
 * @param onProgress 上传进度回调函数
 * @returns 返回模拟的上传结果
 */
export function mockUploadFile(file: File, onProgress?: (percent: number) => void): Promise<UploadResult> {
  return new Promise((resolve, reject) => {
    // 模拟上传进度
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 20 // 随机增加进度
      if (progress > 100) {
        progress = 100
      }

      // 调用进度回调
      if (onProgress) {
        onProgress(Math.floor(progress))
      }

      // 上传完成
      if (progress >= 100) {
        clearInterval(interval)

        // 模拟随机成功或失败（90%成功率）
        if (Math.random() > 0.1) {
          // 模拟成功响应
          const mockResult: UploadResult = {
            fileId: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type || 'application/octet-stream',
            fileUrl: URL.createObjectURL(file), // 使用本地URL作为模拟
            uploadTime: new Date().toISOString(),
          }

          // 如果是图片，添加缩略图
          if (file.type.startsWith('image/')) {
            mockResult.thumbUrl = mockResult.fileUrl
          }

          resolve(mockResult)
        }
        else {
          // 模拟失败
          reject(new Error('模拟上传失败：网络错误'))
        }
      }
    }, 100) // 每100ms更新一次进度
  })
}

/**
 * 模拟批量上传文件接口
 * @param files 要上传的文件数组
 * @param onProgress 上传进度回调函数
 * @returns 返回模拟的上传结果数组
 */
export function mockUploadMultipleFiles(
  files: File[],
  onProgress?: (fileIndex: number, percent: number) => void,
): Promise<UploadResult[]> {
  // 创建上传任务数组
  const uploadTasks = files.map((file, index) => {
    return mockUploadFile(file, (percent) => {
      if (onProgress) {
        onProgress(index, percent)
      }
    })
  })

  // 并行执行所有上传任务
  return Promise.all(uploadTasks)
}
