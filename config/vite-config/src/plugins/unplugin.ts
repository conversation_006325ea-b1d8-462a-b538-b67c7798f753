import * as process from 'node:process'
import * as path from 'node:path'
import type { PluginOption } from 'vite'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import AutoImport from 'unplugin-auto-import/vite'
import checker from 'vite-plugin-checker'

export function setupUnplugin(viteEnv: Env.ImportMeta) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv

  const localIconPath = path.join(process.cwd(), 'src/assets/svg-icon')

  /** The name of the local icon collection */
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(
    `${VITE_ICON_PREFIX}-`,
    '',
  )

  const plugins: PluginOption[] = [
    checker({
      vueTsc: true,
      eslint: {
        lintCommand: 'eslint .',
        useFlatConfig: true,
        dev: {
          logLevel: ['error'],
        },
      },
    }),

    Icons({
      compiler: 'vue3',
      customCollections: {
        [collectionName]: FileSystemIconLoader(localIconPath, svg =>
          svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')),
      },
      scale: 1,
      defaultClass: 'inline-block',
    }),
    AutoImport({
      dts: path.join(process.cwd(), '../../typings/auto-imports.d.ts'),
      imports: ['vue', 'vue-router', 'pinia', '@vueuse/core'], // 注册的全局导入
      vueTemplate: true, // 是否在 vue 模板中自动导入
      dirs: [], // 对自己封装的api和utils完成自动导入   注意：这里的路径以自己的项目路径为主
      eslintrc: {
        enabled: false, // 是否自动生成 eslint 规则，建议生成之后设置 false
        filepath: './.eslintrc-auto-import.json', // 指定自动导入函数 eslint 规则的文件
        globalsPropValue: true,
      },
      resolvers: [NaiveUiResolver()],
    }),
    Components({
      dirs: [
        path.join(process.cwd(), '../../packages/base/components'),
        'src/components',
      ],
      dts: 'src/typings/components.d.ts',
      types: [{ from: 'vue-router', names: ['RouterLink', 'RouterView'] }],
      resolvers: [
        NaiveUiResolver(),
        IconsResolver({
          customCollections: [collectionName],
          componentPrefix: VITE_ICON_PREFIX,
        }),
      ],
    }),
    createSvgIconsPlugin({
      iconDirs: [localIconPath],
      symbolId: `${VITE_ICON_LOCAL_PREFIX}-[dir]-[name]`,
      inject: 'body-last',
      customDomId: '__SVG_ICON_LOCAL__',
    }),
  ]

  return plugins
}
