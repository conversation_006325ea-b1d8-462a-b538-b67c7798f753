import type { CustomRoute } from '@elegant-router/types'
import { layouts, views } from '../elegant/imports'
import { getRoutePath, transformElegantRoutesToVueRoutes } from '../elegant/transform'

/**
 * 根路由配置
 * @description
 * 1. 路径为'/'
 * 2. 重定向到环境变量配置的首页路由
 * 3. 标记为常量路由(不会被权限系统过滤)
 */
export const ROOT_ROUTE: CustomRoute = {
  name: 'root',
  path: '/',
  redirect: getRoutePath(import.meta.env.VITE_ROUTE_HOME) || '/agent',
  meta: {
    title: 'root',
    constant: true, // 标记为常量路由
  },
}

/**
 * 404路由配置
 * @description
 * 1. 使用通配符路径匹配所有未定义路由
 * 2. 使用空白布局和404视图组件
 * 3. 标记为常量路由
 */
const NOT_FOUND_ROUTE: CustomRoute = {
  name: 'not-found',
  path: '/:pathMatch(.*)*', // 通配符路径
  component: 'layout.blank$view.404', // 空白布局+404页面
  meta: {
    title: 'not-found',
    constant: true, // 标记为常量路由
  },
}

/**
 * 内置路由表
 * @description 包含根路由和404路由
 */
const builtinRoutes: CustomRoute[] = [ROOT_ROUTE, NOT_FOUND_ROUTE]

/**
 * 创建内置Vue路由
 * @returns 返回转换后的Vue路由数组
 * @description 将优雅路由配置转换为Vue路由配置
 */
export function createBuiltinVueRoutes() {
  return transformElegantRoutesToVueRoutes(builtinRoutes, layouts, views)
}
