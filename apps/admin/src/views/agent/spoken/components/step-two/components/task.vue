<script setup lang="ts">
import { type SortableEvent, VueDraggable } from 'vue-draggable-plus'

defineOptions({
  name: 'FileTask',
})

const props = withDefaults(
  defineProps<{
    active: number
  }>(),
  {},
)
const emit = defineEmits<{
  'update:tasks': [tasks: Task[]]
  'update:active': [active: number]
  'taskClick': [task: Task, active: number]
  'taskAdd': [text: string, call?: (res: boolean) => void]
  'taskEdit': [task: Task]
  'taskDelete': [task: Task]
}>()

const tasks = defineModel<Task[]>('tasks', { default: [] })

type Task = Pick<AgentApi.OralCommunicationDialogueTaskInput, 'Id' | 'Name'> & {
  IsEditing?: boolean
}

// 新增任务的输入框显示状态
const showAddInput = ref(false)
const newTaskText = ref('')

// 编辑任务的文本
const editingText = ref('')

// 添加新任务
function handleAddTask() {
  emit('taskAdd', '', (isValid) => {
    if (!isValid) {
      return
    }
    showAddInput.value = true
    newTaskText.value = ''
  })
}

// 确认添加任务
function confirmAddTask() {
  if (!newTaskText.value.trim()) {
    return
  }
  const newName = newTaskText.value.trim()
  showAddInput.value = false
  newTaskText.value = ''
  emit('taskAdd', newName)
}

// 取消添加任务
function cancelAddTask() {
  showAddInput.value = false
  newTaskText.value = ''
}

// 点击任务项
async function handleTaskClick(task: Task, active: number) {
  if (task.IsEditing)
    return
  emit('taskClick', task, active)
}

// 开始编辑任务
function startEditTask(task: Task, event: Event) {
  event.stopPropagation()
  task.IsEditing = true
  editingText.value = task.Name ?? ''
}

// 确认编辑任务
function confirmEditTask(task: Task) {
  if (editingText.value.trim()) {
    task.Name = editingText.value.trim()
    task.IsEditing = false
    emit('taskEdit', task)
  }
}

// 取消编辑任务
function cancelEditTask(task: Task) {
  task.IsEditing = false
  editingText.value = ''
}

// 拖拽结束事件
function onDragEnd(params: SortableEvent) {
  const { newIndex, oldIndex } = params

  if (typeof newIndex !== 'number' || typeof oldIndex !== 'number') {
    return
  }
  // 计算拖拽后当前选中项的新位置
  let newActive = props.active

  if (props.active === oldIndex) {
    // 如果拖拽的是当前选中的项，直接更新为新位置
    newActive = newIndex
  }
  else if (props.active > oldIndex && props.active <= newIndex) {
    // 如果当前选中项在拖拽项的后面，且在新位置之前或等于新位置
    // 当前选中项会向前移动一位
    newActive = props.active - 1
  }
  else if (props.active < oldIndex && props.active >= newIndex) {
    // 如果当前选中项在拖拽项的前面，且在新位置之后或等于新位置
    // 当前选中项会向后移动一位
    newActive = props.active + 1
  }
  // 其他情况下，当前选中项的位置不变

  console.log('新的 active 位置:', newActive)

  // 更新 active 位置
  if (newActive !== props.active) {
    emit('update:active', newActive)
  }
}

function handleNegativeClick() {}

function handlePositiveClick(task: Task) {
  if (tasks.value.length === 1) {
    window.$message?.warning('至少有一个任务')
    return
  }

  if (props.active <= -1) {
    return
  }
  const nextActive = props.active - 1 // 选中上一个
  tasks.value.splice(props.active, 1) // 删除当前的
  emit('update:active', nextActive) // 选中上一个
  emit('taskDelete', task)
}
</script>

<template>
  <NCard title="任务列表">
    <template #header-extra>
      <NButton type="primary" size="small" @click="handleAddTask">
        <template #icon>
          <SvgIcon icon="ic:round-plus" />
        </template>
        添加任务
      </NButton>
    </template>
    <!-- 任务列表 -->
    <VueDraggable
      v-model="tasks!"
      :animation="200"
      handle=".drag-handle"
      @end="onDragEnd"
    >
      <div
        v-for="(task, index) in tasks"
        :key="index"
        class="mb-12px flex cursor-pointer items-center gap-12px border border-gray-200 rounded-8px bg-white p-12px transition-all duration-200 dark:border-gray-600 hover:border-primary dark:bg-gray-800 hover:shadow-sm"
        :class="{
          'border-primary bg-primary/5 dark:bg-primary/10 shadow-sm':
            active === index,
        }"
        @click="handleTaskClick(task, index)"
      >
        <!-- 拖拽手柄 -->
        <div
          class="drag-handle flex-shrink-0 cursor-move rounded-4px p-4px transition-colors duration-150 hover:bg-gray-100 dark:hover:bg-gray-600"
        >
          <SvgIcon
            icon="mdi:drag"
            class="size-16px text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          />
        </div>

        <!-- 任务内容 -->
        <div class="min-w-0 flex-1">
          <!-- 编辑状态 -->
          <NInput
            v-if="task.IsEditing"
            v-model:value="editingText"
            type="textarea"
            placeholder="请输入任务内容"
            @keyup.enter="confirmEditTask(task)"
            @keyup.esc="cancelEditTask(task)"
            @blur="confirmEditTask(task)"
            @click.stop
          />
          <!-- 显示状态 -->
          <div
            v-else
            class="whitespace-pre-wrap break-words text-14px text-gray-700 leading-relaxed dark:text-gray-300"
          >
            {{ task.Name }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center gap-4">
          <NButton
            quaternary
            size="small"
            @click.stop="
              task.IsEditing
                ? confirmEditTask(task)
                : startEditTask(task, $event)
            "
          >
            <template #icon>
              <SvgIcon
                :icon="task.IsEditing ? 'ic:round-check' : 'ic:round-edit'"
                class="size-16px text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              />
            </template>
          </NButton>
          <NPopconfirm
            v-if="(tasks ?? []).length > 1 || active !== index"
            @positive-click="handlePositiveClick(task)"
            @negative-click="handleNegativeClick"
          >
            <template #trigger>
              <NButton quaternary size="small">
                <template #icon>
                  <SvgIcon
                    icon="ic:round-delete"
                    class="size-16px text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  />
                </template>
              </NButton>
            </template>
            确定要删除这个任务吗？
          </NPopconfirm>
          <!-- 当只有一个任务且被选中时，显示禁用的删除按钮 -->
          <NButton v-else quaternary size="small" disabled @click.stop>
            <template #icon>
              <SvgIcon icon="ic:round-delete" class="size-16px text-gray-300" />
            </template>
          </NButton>
        </div>
      </div>
    </VueDraggable>

    <!-- 添加任务输入框 -->
    <div
      v-if="showAddInput"
      class="flex items-center gap-12px border-2 border-primary rounded-8px border-dashed bg-white p-12px shadow-sm dark:bg-gray-800"
    >
      <!-- 拖拽手柄 -->
      <div
        class="flex-shrink-0 cursor-not-allowed rounded-4px p-4px opacity-50"
      >
        <SvgIcon icon="mdi:drag" class="size-16px text-gray-300" />
      </div>
      <!-- 任务内容 -->
      <div class="min-w-0 flex-1">
        <NInput
          v-model:value="newTaskText"
          type="textarea"
          :rows="2"
          placeholder="请输入任务内容"
          @keyup.enter="confirmAddTask"
          @keyup.esc="cancelAddTask"
        />
      </div>
      <!-- 操作按钮 -->
      <div class="flex items-center gap-4px">
        <NButton quaternary size="small" @click.stop="confirmAddTask">
          <template #icon>
            <SvgIcon
              icon="ic:round-check"
              class="size-16px text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            />
          </template>
        </NButton>
        <NButton quaternary size="small" @click.stop="cancelAddTask">
          <template #icon>
            <SvgIcon
              icon="ic:round-close"
              class="size-16px text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            />
          </template>
        </NButton>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
/* 所有样式已转换为内联Tailwind CSS类 */
</style>
