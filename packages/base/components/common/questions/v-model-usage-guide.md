# 题目组件 v-model 使用指南

## 问题说明

之前在 `QuestionItemContainer` 组件中同时使用了：
```vue
v-model="itemInfo"
v-model:item="itemInfo"
```

这会造成冲突，因为：
- `v-model="itemInfo"` → `:modelValue="itemInfo"` + `@update:modelValue`
- `v-model:item="itemInfo"` → `:item="itemInfo"` + `@update:item`

同一个数据被绑定到两个不同的 prop，会导致混乱。

## 正确的设计（已修复）

### 统一的数据结构

现在所有数据都存储在 `Question.ProcessedQuestionData` 中：
- **题目数据**：标题、选项、正确答案等
- **用户答案**：通过 `userAnswer` 字段存储
  - 单选题：`string` 类型
  - 多选题：`string[]` 类型

### 在题目组件中的设计

每个题目组件（如 `QMultiChoice`、`QSingleChoice`）只需要：

1. **`v-model:item`**：用于绑定**完整的题目数据**（包含用户答案）
   - 类型：`Question.ProcessedQuestionData`

### 在容器组件中的正确用法

```vue
<template>
  <component
    :is="activeModule.component"
    v-model:item="itemInfo"     <!-- 完整的题目数据（包含用户答案） -->
    :type="props.type"
  />
</template>

<script setup>
// 完整的题目数据（包含用户答案）
const itemInfo = defineModel<Question.ProcessedQuestionData>('itemInfo', {
  default: () => ({}),
})
</script>
```

### 在父组件中的使用

```vue
<template>
  <QuestionItemContainer
    v-model:item-info="questionData"    <!-- 完整的题目数据 -->
    :type="'answer'"
  />
</template>

<script setup>
const questionData = ref({
  id: '1',
  title: '这是一道多选题',
  options: [
    { label: '选项A', value: 'A' },
    { label: '选项B', value: 'B' },
    { label: '选项C', value: 'C' }
  ],
  correctAnswer: 'A,B', // 正确答案
  userAnswer: [], // 用户答案（多选题用数组，单选题用字符串）
  // ... 其他属性
})
</script>
```

## 修改 item 属性的方法

### 方法一：通过 v-model:item 直接修改

```javascript
// 修改题目标题
questionData.value = {
  ...questionData.value,
  title: '新的题目标题'
}

// 添加新选项
questionData.value = {
  ...questionData.value,
  options: [
    ...questionData.value.options,
    { label: '新选项D', value: 'D' }
  ]
}

// 修改用户答案
questionData.value = {
  ...questionData.value,
  userAnswer: ['A', 'B'] // 多选题
}
```

### 方法二：通过组件实例方法

```vue
<template>
  <QuestionItemContainer
    ref="questionRef"
    v-model:item-info="questionData"
  />
</template>

<script setup>
const questionRef = ref()

function updateTitle() {
  // 如果子组件暴露了 updateItemProperty 方法
  questionRef.value?.updateItemProperty('title', '新标题')
}

function updateUserAnswer() {
  questionRef.value?.updateItemProperty('userAnswer', ['A', 'C'])
}
</script>
```

## 总结

- **只使用 `v-model:item`**：绑定完整的题目数据（包含用户答案）
- **用户答案存储在 `item.userAnswer` 中**：
  - 单选题：`string` 类型
  - 多选题：`string[]` 类型
- **不再需要单独的用户答案绑定**
- **修改任何属性（包括用户答案）都通过 `v-model:item` 绑定的数据进行**
