// 导入存储工具库
import { createLocalforage, createStorage } from '../storage'

// 设置存储前缀，从环境变量获取，默认为空字符串
const storagePrefix = import.meta.env.VITE_STORAGE_PREFIX || ''

/**
 * 创建本地存储实例
 * @type {StorageInstance<StorageType.Local>}
 */
export const localStg = createStorage<StorageType.Local>(
  'local',
  storagePrefix,
)

/**
 * 创建会话存储实例
 * @type {StorageInstance<StorageType.Session>}
 */
export const sessionStg = createStorage<StorageType.Session>(
  'session',
  storagePrefix,
)

/**
 * 创建本地forage存储实例(基于IndexedDB/WebSQL)
 * @type {LocalForageInstance<StorageType.Local>}
 */
export const localforage = createLocalforage<StorageType.Local>('local')
