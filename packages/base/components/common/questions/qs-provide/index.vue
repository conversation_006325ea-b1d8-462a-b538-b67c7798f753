<script setup lang="ts">
import { qsFormInjectionKey } from './context'

interface Props {
  type?: 'edit' | 'answer' | 'preview'
}

defineOptions({
  name: 'QsProvide',
})

const props = withDefaults(defineProps<Props>(), {
  type: 'preview',
})

// 提供上下文
provide(qsFormInjectionKey, {
  props,
})

// 处理点击事件
function handleClick(event: MouseEvent) {
  if ((event.target as HTMLElement).tagName === 'IMG') {
    // 实现图片放大逻辑
    const imgSrc = (event.target as HTMLImageElement).src
    // 放大图片
    console.log(imgSrc)
  }
}
</script>

<template>
  <div @click="handleClick">
    <slot />
  </div>
</template>
