/**
 * Setup vconsole for mobile debugging
 * vconsole 移动端调试工具配置
 */

import VConsole from 'vconsole'

/**
 * Setup vconsole
 * 设置 vconsole
 */
export function setupVConsole() {
  // 只在开发环境或测试环境中启用 vconsole
  if (import.meta.env.DEV || import.meta.env.MODE === 'test') {
    // 检测是否为移动设备或平板设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

    // 或者根据屏幕宽度判断
    const isSmallScreen = window.innerWidth <= 1024

    // 在移动设备、平板设备或小屏幕设备上启用 vconsole
    if (isMobile || isSmallScreen) {
      const vConsole = new VConsole({
        theme: 'dark', // 主题：'light' | 'dark'
        defaultPlugins: ['system', 'network', 'element', 'storage'], // 默认插件
        maxLogNumber: 1000, // 最大日志数量
        onReady() {
          console.log('vConsole is ready.')
        },
        onClearLog() {
          console.log('on clearLog')
        },
      })

      // 可以通过全局变量访问 vConsole 实例
      ;(window as any).vConsole = vConsole

      console.log('vConsole initialized for mobile/tablet debugging')
    }
  }
}
