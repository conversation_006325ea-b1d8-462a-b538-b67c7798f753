<script setup lang="ts">
import { useRouterPush } from '@sa/hooks'
import { GLOBAL_HEADER_MENU_ID } from '@sa/constants/app'
import { useMenu } from '../../../context'
import { useRouteStore } from '@/store/modules/route'

defineOptions({
  name: 'HorizontalMenu',
})

const routeStore = useRouteStore()
const { routerPushByKeyWithMetaQuery } = useRouterPush()
const { selectedKey } = useMenu()
</script>

<template>
  <Teleport :to="`#${GLOBAL_HEADER_MENU_ID}`">
    <NMenu
      mode="horizontal"
      :value="selectedKey"
      :options="routeStore.menus"
      :indent="18"
      responsive
      @update:value="routerPushByKeyWithMetaQuery"
    />
  </Teleport>
</template>

<style scoped></style>
