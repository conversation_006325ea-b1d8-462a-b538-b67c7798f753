<script lang="ts" setup>
import { computed } from 'vue'
import { useEcharts } from '@sa/hooks'
import type { ECOption } from '@sa/hooks'

defineOptions({
  name: 'GradeDistributionChart',
})

const props = withDefaults(defineProps<Props>(), {
  title: '等级分布',
  height: '350px',
})

interface GradeDistributionItem {
  grade: string
  count: number
  color: string
}

interface Props {
  title?: string
  height?: string
  gradeDistributionData: GradeDistributionItem[]
}

// 计算图表数据
const chartData = computed(() => {
  const categories = props.gradeDistributionData.map(item => item.grade)
  const data = props.gradeDistributionData.map(item => ({
    value: item.count,
    itemStyle: {
      color: item.color,
    },
  }))

  return {
    categories,
    data,
  }
})

// 图表配置
function createChartOptions(): ECOption {
  return {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const data = props.gradeDistributionData[params.dataIndex]
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${data!.grade}</div>
            <div>人数: ${data!.count}</div>
          </div>
        `
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '8%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: chartData.value.categories,
      axisLine: {
        lineStyle: {
          color: '#e5e7eb',
        },
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12,
        fontWeight: 500,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '等级分布',
        type: 'bar',
        data: chartData.value.data,
        barWidth: '60%',
        itemStyle: {
          borderRadius: [6, 6, 0, 0],
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.2)',
          },
        },
        animationDelay: (idx: number) => idx * 100,
      },
    ],
    animationEasing: 'elasticOut',
    animationDelayUpdate: (idx: number) => idx * 50,
  }
}

// 使用图表Hook
const { domRef, updateOptions } = useEcharts(createChartOptions, {
  onRender: (_chart) => {
    // 图表渲染完成后的回调
  },
  onUpdated: (_chart) => {
    // 图表更新完成后的回调
  },
})

watch(chartData, () => {
  updateOptions(createChartOptions)
})
</script>

<template>
  <div>
    <div ref="domRef" :style="{ height: props.height }" class="w-full rounded-8px" />
  </div>
</template>

<style scoped>
/* 图表容器样式 */
.w-full {
  transition: all 0.3s ease;
}
</style>
