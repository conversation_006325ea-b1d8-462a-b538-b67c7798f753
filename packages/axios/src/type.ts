import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
/**
 * 支持的HTTP内容类型
 */
export type ContentType =
  | 'text/html' // HTML文本
  | 'text/plain' // 纯文本
  | 'multipart/form-data' // 表单数据
  | 'application/json' // JSON数据
  | 'application/x-www-form-urlencoded' // URL编码表单
  | 'application/octet-stream' // 二进制流

/**
 * 请求配置选项接口
 * @template ResponseData 响应数据类型
 */
export interface RequestOption<ResponseData = any> {
  /**
   * 请求前拦截器钩子
   * @param config Axios配置对象
   */
  onRequest: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>

  /**
   * 检查后端响应是否成功的钩子
   * @param response Axios响应对象
   */
  isBackendSuccess: (response: AxiosResponse<ResponseData>) => boolean

  /**
   * 后端请求失败处理钩子
   * @param response Axios响应对象
   * @param instance Axios实例
   */
  onBackendFail: (
    response: AxiosResponse<ResponseData>,
    instance: AxiosInstance
  ) => Promise<AxiosResponse | null> | Promise<void>

  /**
   * 转换JSON响应数据的钩子
   * @param response Axios响应对象
   */
  transformBackendResponse: (response: AxiosResponse<ResponseData>) => any | Promise<any>

  /**
   * 错误处理钩子
   * @param error Axios错误对象
   */
  onError: (error: AxiosError<ResponseData>) => void | Promise<void>
}

/**
 * 响应类型映射接口
 */
interface ResponseMap {
  blob: Blob // 二进制大对象
  text: string // 文本
  arrayBuffer: ArrayBuffer // 数组缓冲区
  stream: ReadableStream<Uint8Array> // 数据流
  document: Document // HTML文档
}

/**
 * 支持的响应类型
 */
export type ResponseType = keyof ResponseMap | 'json'

/**
 * 根据响应类型映射返回的数据类型
 * @template R 响应类型
 * @template JsonType JSON类型
 */
export type MappedType<R extends ResponseType, JsonType = any> = R extends keyof ResponseMap
  ? ResponseMap[R]
  : JsonType

/**
 * 自定义Axios请求配置
 * @template R 响应类型
 */
export type CustomAxiosRequestConfig<R extends ResponseType = 'json'> = Omit<AxiosRequestConfig, 'responseType'> & {
  responseType?: R // 响应类型
}

/**
 * 请求实例通用接口
 * @template T 状态类型
 */
export interface RequestInstanceCommon<T> {
  /**
   * 根据请求ID取消请求
   * @param requestId 请求ID
   */
  cancelRequest: (requestId: string) => void

  /**
   * 取消所有请求
   */
  cancelAllRequest: () => void

  /**
   * 请求实例状态
   */
  state: T
}

/**
 * 标准请求实例接口
 * @template S 状态类型
 */
export interface RequestInstance<S = Record<string, unknown>> extends RequestInstanceCommon<S> {
  <T = any, R extends ResponseType = 'json'>(config: CustomAxiosRequestConfig<R>): Promise<MappedType<R, T>>
}

/**
 * 扁平化响应成功数据结构
 * @template T 数据类型
 * @template ResponseData 响应数据类型
 */
export interface FlatResponseSuccessData<T = any, ResponseData = any> {
  data: T // 响应数据
  error: null // 错误对象(成功时为null)
  response: AxiosResponse<ResponseData> // 完整响应对象
}

/**
 * 扁平化响应失败数据结构
 * @template ResponseData 响应数据类型
 */
export interface FlatResponseFailData<ResponseData = any> {
  data: null // 数据(失败时为null)
  error: AxiosError<ResponseData> // 错误对象
  response: AxiosResponse<ResponseData> // 完整响应对象
}

/**
 * 扁平化响应数据联合类型
 * @template T 数据类型
 * @template ResponseData 响应数据类型
 */
export type FlatResponseData<T = any, ResponseData = any> =
  | FlatResponseSuccessData<T, ResponseData>
  | FlatResponseFailData<ResponseData>

/**
 * 扁平化请求实例接口
 * @template S 状态类型
 * @template ResponseData 响应数据类型
 */
export interface FlatRequestInstance<S = Record<string, unknown>, ResponseData = any> extends RequestInstanceCommon<S> {
  <T = any, R extends ResponseType = 'json'>(
    config: CustomAxiosRequestConfig<R>
  ): Promise<FlatResponseData<MappedType<R, T>, ResponseData>>
}
