{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  "eslint.useFlatConfig": true,
  "editor.formatOnSave": false,
  "prettier.enable": false,
  "typescript.tsdk": "node_modules/typescript/lib",
  "unocss.root": ["./"],
  "vue.server.hybridMode": true,
  // Disable the default formatter, use eslint instead

  // Silent the stylistic rules in you IDE, but still auto fix them
  // "eslint.rules.customizations": [
  //   { "rule": "style/*", "severity": "off", "fixable": true },
  //   { "rule": "format/*", "severity": "off", "fixable": true },
  //   { "rule": "*-indent", "severity": "off", "fixable": true },
  //   { "rule": "*-spacing", "severity": "off", "fixable": true },
  //   { "rule": "*-spaces", "severity": "off", "fixable": true },
  //   { "rule": "*-order", "severity": "off", "fixable": true },
  //   { "rule": "*-dangle", "severity": "off", "fixable": true },
  //   { "rule": "*-newline", "severity": "off", "fixable": true },
  //   { "rule": "*quotes", "severity": "off", "fixable": true },
  //   { "rule": "*semi", "severity": "off", "fixable": true }
  // ],

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "json5",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "svelte",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ]
}
