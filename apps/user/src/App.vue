<script setup lang="ts">
import { computed } from 'vue'
import { NConfigProvider, darkTheme, dateZhCN, zhCN } from 'naive-ui'
import type { WatermarkProps } from 'naive-ui'
import { useCaptcha } from '@sa/hooks'
import { useThemeStore } from '@sa/store/modules/theme'

defineOptions({
  name: 'App',
})
// 使用主题存储
const themeStore = useThemeStore()

// 计算暗黑主题配置
const naiveDarkTheme = computed(() =>
  themeStore.darkMode ? darkTheme : undefined,
)

// 计算水印配置
const watermarkProps = computed<WatermarkProps>(() => {
  return {
    content: themeStore.watermark.text, // 水印文本
    cross: true, // 交叉水印
    fullscreen: true, // 全屏水印
    fontSize: 16, // 字体大小
    lineHeight: 16, // 行高
    width: 384, // 水印宽度
    height: 384, // 水印高度
    xOffset: 12, // X轴偏移
    yOffset: 60, // Y轴偏移
    rotate: -15, // 旋转角度
    zIndex: 9999, // 层级
  }
})

const { getCaptcha } = useCaptcha()

getCaptcha('123123')
</script>

<template>
  <!-- Naive UI配置提供器 -->
  <NConfigProvider
    :theme="naiveDarkTheme"
    :theme-overrides="themeStore.naiveTheme"
    :locale="zhCN"
    :date-locale="dateZhCN"
    class="h-full"
  >
    <!-- 应用提供器 -->
    <AppProvider>
      <!-- 路由视图 -->
      <RouterView class="bg-layout" />
      <!-- 水印组件 -->
      <NWatermark v-if="themeStore.watermark.visible" v-bind="watermarkProps" />
    </AppProvider>
  </NConfigProvider>
</template>

<style scoped></style>
