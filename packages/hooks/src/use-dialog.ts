import { useDialog } from 'naive-ui'
import type { DialogApiInjection } from 'naive-ui/es/dialog/src/DialogProvider'

export function usePromiseDialog() {
  const dialog = useDialog()

  function showPromiseDialog(
    options: {
      title?: string
      content?: string
      positiveText?: string
      negativeText?: string
      type?: keyof DialogApiInjection
    } = {},
  ) {
    return new Promise((resolve, reject) => {
      dialog[options.type ?? 'warning']({
        title: options.title ?? '提示',
        content: options.content,
        positiveText: options.positiveText ?? '确认',
        negativeText: options.negativeText ?? '取消',
        onPositiveClick: () => {
          resolve(true)
        },
        onNegativeClick: () => {
          reject(new Error('取消'))
        },
      })
    })
  }

  return { dialog, showPromiseDialog }
}
