import type { App } from 'vue'
import {
  type RouterHistory,
  createMemoryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router'
import { createBuiltinVueRoutes } from './routes/builtin'
import { createRouterGuard } from './guard'

// 从环境变量获取路由历史模式，默认为history模式
const { VITE_ROUTER_HISTORY_MODE = 'history', VITE_BASE_URL } = import.meta.env

// 路由历史模式创建器映射表
const historyCreatorMap: Record<Env.RouterHistoryMode, (base?: string) => RouterHistory> = {
  hash: createWebHashHistory, // hash模式
  history: createWebHistory, // history模式
  memory: createMemoryHistory, // 内存模式(主要用于测试)
}

// 创建Vue Router实例
export const router = createRouter({
  // 根据配置选择路由历史模式
  history: historyCreatorMap[VITE_ROUTER_HISTORY_MODE](VITE_BASE_URL),
  // 初始化内置路由
  routes: createBuiltinVueRoutes(),
})

/**
 * 设置Vue Router
 * @param app Vue应用实例
 * @description
 * 1. 将router实例挂载到Vue应用
 * 2. 创建路由守卫
 * 3. 等待路由就绪
 */
export async function setupRouter(app: App) {
  app.use(router) // 使用路由插件
  createRouterGuard(router) // 添加路由守卫
  await router.isReady() // 等待路由初始化完成
}
