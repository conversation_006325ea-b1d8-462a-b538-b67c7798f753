<script setup lang="ts">
import { computed, reactive } from 'vue'
import { useFormRules, useNaiveForm, useRouterPush } from '@sa/hooks'
import { useAuthStore } from '@/store/modules/auth'

defineOptions({
  name: 'PwdLogin',
})

const authStore = useAuthStore()
const { toggleLoginModule } = useRouterPush()
const { formRef, validate } = useNaiveForm()

interface FormModel {
  userName: string
  password: string
}

const model: FormModel = reactive({
  userName: '',
  password: '',
})

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  // inside computed to make locale reactive, if not apply i18n, you can define it without computed
  const { formRules } = useFormRules()

  return {
    userName: formRules.userName,
    password: formRules.pwd,
  }
})

async function handleSubmit() {
  await validate()
  await authStore.login(model.userName, model.password)
}

// type AccountKey = 'super' | 'admin' | 'user'

// interface Account {
//   key: AccountKey
//   label: string
//   userName: string
//   password: string
// }

// const accounts = computed<Account[]>(() => [
//   {
//     key: 'super',
//     label: '超级管理员',
//     userName: 'Super',
//     password: '123456',
//   },
//   {
//     key: 'admin',
//     label: '管理员',
//     userName: 'Admin',
//     password: '123456',
//   },
//   {
//     key: 'user',
//     label: '普通用户',
//     userName: 'User',
//     password: '123456',
//   },
// ])
</script>

<template>
  <NForm
    ref="formRef"
    :model="model"
    :rules="rules"
    size="large"
    :show-label="false"
    @keyup.enter="handleSubmit"
  >
    <NFormItem path="userName">
      <NInput v-model:value="model.userName" placeholder="请输入用户名" />
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        placeholder="请输入密码"
      />
    </NFormItem>
    <NSpace vertical :size="24">
      <div class="flex-y-center justify-between">
        <NCheckbox>记住我</NCheckbox>
        <NButton quaternary @click="toggleLoginModule('reset-pwd')">
          忘记密码？
        </NButton>
      </div>
      <NButton
        type="primary"
        size="large"
        round
        block
        :loading="authStore.loginLoading"
        @click="handleSubmit"
      >
        确认
      </NButton>
      <div v-if="false" class="flex-y-center justify-between gap-12px">
        <NButton class="flex-1" block @click="toggleLoginModule('code-login')">
          验证码登录
        </NButton>
        <NButton class="flex-1" block @click="toggleLoginModule('qrcode-login')">
          二维码登录
        </NButton>
      </div>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
