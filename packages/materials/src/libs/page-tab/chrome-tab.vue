<script setup lang="ts">
import type { PageTabProps } from '../../types'
import ChromeTabBg from './chrome-tab-bg.vue'
import style from './index.module.css'

defineOptions({
  name: 'ChromeTab',
})

defineProps<PageTabProps>()

defineSlots<Slots>()

type SlotFn = (props?: Record<string, unknown>) => any

// 定义插槽类型
interface Slots {
  /**
   * 默认插槽 - 标签页中间内容
   */
  default?: SlotFn
  /**
   * 前缀插槽 - 标签页左侧内容
   */
  prefix?: SlotFn
  /**
   * 后缀插槽 - 标签页右侧内容
   */
  suffix?: SlotFn
}
</script>

<template>
  <!-- Chrome浏览器样式的标签页容器 -->
  <div
    class=":soy: relative inline-flex cursor-pointer items-center justify-center gap-16px whitespace-nowrap px-24px py-6px -mr-18px"
    :class="[
      style['chrome-tab'], // 基础样式
      { [style['chrome-tab_dark']]: darkMode }, // 暗黑模式样式
      { [style['chrome-tab_active']]: active }, // 激活状态样式
      { [style['chrome-tab_active_dark']]: active && darkMode }, // 暗黑模式下的激活状态
    ]"
  >
    <!-- 背景部分(使用ChromeTabBg组件) -->
    <div class=":soy: pointer-events-none absolute left-0 top-0 h-full w-full -z-1" :class="[style['chrome-tab__bg']]">
      <ChromeTabBg />
    </div>
    <!-- 左侧内容插槽 -->
    <slot name="prefix" />
    <!-- 默认内容插槽 -->
    <slot />
    <!-- 右侧内容插槽 -->
    <slot name="suffix" />
    <!-- 右侧分隔线 -->
    <div class=":soy: absolute right-7px h-16px w-1px bg-#1f2225" :class="[style['chrome-tab-divider']]" />
  </div>
</template>

<style scoped></style>
