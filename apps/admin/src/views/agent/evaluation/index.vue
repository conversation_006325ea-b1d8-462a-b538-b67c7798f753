<script setup lang="ts">
import ChatContainer from '@sa/components/common/chat/ChatContainer.vue'
import { useRequest } from '@sa/hooks'
import { XMarkdown } from 'vue-element-plus-x'
import { convertHistoryDataToMessages } from '@sa/utils'
import AgentInfo from './components/agent-info-header.vue'
import { CHAT_CONFIG as CONFIG, formatApiParams } from './utils/chat-helpers'
import {
  fetchGetAIDialogueContentRecord,
  fetchGetOralCommunicationStudentResult,
} from '@/service/api'

const initialMessages = ref<Chat.MessageItem[]>()
const chatContainerRef = ref<InstanceType<typeof ChatContainer>>()

const aiName = import.meta.env.VITE_AI_NAME
const aiAvatar = import.meta.env.VITE_AI_AVAYAR

const query = useRoute().query

const agentTaskId = query.taskId as string
const agentId = query.agentId as string
const studentId = query.studentId as string

// 路由参数
const queryParams = computed(() => ({
  AgentId: agentId as string,
  AgentTaskId: agentTaskId as string,
}))

// 分页状态管理 (0表示没有更多数据)
const historyPageIndex = ref<number>(CONFIG.PAGINATION.INITIAL_PAGE)

const { data: studentResult, run: fetchStudentResult } = useRequest(
  () => {
    return fetchGetOralCommunicationStudentResult({
      AgentTaskId: agentTaskId,
      StudentId: studentId,
    })
  },
  {
    manual: true,
  },
)

/**
 * 公共的历史消息请求逻辑
 * @returns 历史消息数组或null
 */
async function requestHistoryMessages(): Promise<Chat.MessageItem[] | null> {
  try {
    const { data, error } = await fetchGetAIDialogueContentRecord(
      formatApiParams(queryParams.value, historyPageIndex.value, {
        userId: studentId,
      }),
    )

    if (error) {
      console.error('获取历史消息失败:', error)
      return null
    }

    if (data?.TotalCount && data.Datas && data.Datas.length > 0) {
      // 转换历史消息数据为组件所需格式
      const historyMessages = convertHistoryDataToMessages({
        historyData: data.Datas,
        userInfo: {
          userName: studentResult.value?.StudentName || '',
          userAvatar: studentResult.value?.StudentLogo || '',
        },
        aiInfo: { aiName, aiAvatar },
      })

      // 增加页码，准备下次加载
      historyPageIndex.value++
      return historyMessages
    }
    else {
      // 没有更多数据时，将 PageIndex 设为 0，表示不再加载
      historyPageIndex.value = 0
      return []
    }
  }
  catch (error) {
    console.error('请求历史消息异常:', error)
    return null
  }
}

// 初始化时获取历史对话记录
async function fetchHistoryMessages() {
  const historyMessages = await requestHistoryMessages()
  if (historyMessages) {
    initialMessages.value = historyMessages
  }
}

/**
 * 加载更多历史消息
 * 实现上拉加载更多功能，将新消息插入到列表顶部
 * 并保持用户当前的阅读位置
 */
async function loadMoreMessages() {
  // 如果没有更多数据可加载，直接返回
  if (!historyPageIndex.value) {
    chatContainerRef.value?.finishLoadMore()
    return
  }

  try {
    const historyMessages = await requestHistoryMessages()

    if (historyMessages && historyMessages.length > 0) {
      // 将新的历史消息插入到现有消息列表的前面
      if (initialMessages.value) {
        initialMessages.value = [...historyMessages, ...initialMessages.value]
      }
      else {
        initialMessages.value = historyMessages
      }

      // 等待DOM更新后，滚动到加载前的第一条消息位置
      await nextTick()
      // 原来的第一条消息现在的索引是新加载的消息数量
      const targetIndex = historyMessages.length
      chatContainerRef.value?.scrollToBubble(targetIndex, 'instant')
    }
  }
  catch (error) {
    console.error('加载历史消息异常:', error)
  }
  finally {
    // 无论成功还是失败，都要重置加载状态
    chatContainerRef.value?.finishLoadMore()
  }
}

const drawerVisible = ref(true)

onMounted(async () => {
  await fetchStudentResult()
  fetchHistoryMessages()
})
</script>

<template>
  <div class="h-full w-full">
    <div class="h-full flex gap-12px rounded-12px bg-white p-12px">
      <div
        class="relative h-full min-w-0 flex flex-col flex-1 transition-all duration-300 ease-in-out"
      >
        <!-- 顶部按钮区域 -->
        <ButtonIcon
          class="absolute right-20px top-20px z-10000 bg-gray-100"
          :icon="drawerVisible ? 'mdi:close' : 'mdi:menu'"
          :tooltip-content="drawerVisible ? '关闭侧边栏' : '打开侧边栏'"
          @click="drawerVisible = !drawerVisible"
        />

        <div class="shrink-0">
          <AgentInfo
            v-if="studentResult"
            :agent-info="{
              id: agentId,
              name: studentResult.AgentTaskName,
              description: studentResult.AgentTaskIntroduce,
              avatar: studentResult.Logo,
              category: studentResult.InteractiveMode,
            }"
          />
        </div>

        <div class="min-h-0 flex-1">
          <ChatContainer
            ref="chatContainerRef"
            :initial-messages="initialMessages"
            :show-sender="false"
            @load-more="loadMoreMessages"
          />
        </div>
      </div>
      <div
        class="h-full flex flex-col shrink-0 overflow-hidden border-1 border-gray-200 rounded-12px bg-white transition-all duration-300 ease-in-out"
        :class="drawerVisible ? 'w-400px opacity-100' : 'w-0 opacity-0'"
      >
        <div
          class="h-70px flex shrink-0 items-center justify-between from-[rgba(54,192,253,0.1)] via-[rgba(54,120,253,0.1)] to-[rgba(154,54,253,0.1)] bg-gradient-to-r px-32px"
        >
          <span
            class="from-[#36BCFD] via-[#7193FF] to-[#E572FE] bg-gradient-to-r bg-clip-text text-24px text-transparent font-500"
          >AI评估结果</span>
          <span class="text-[#7E51E9]">此内容由AI生成，仅供参考</span>
        </div>
        <div class="min-h-0 flex-1">
          <NScrollbar class="h-full">
            <XMarkdown
              v-if="studentResult?.AssessmentResult"
              :markdown="studentResult?.AssessmentResult"
              :code-x-slot="{
                codeHeaderControl: () => h('div', ''),
              }"
              class="markdown-body"
              default-theme-mode="light"
            />
          </NScrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
