import { addColorAlpha, transformColorWithOpacity } from '@sa/color'
import type { PageTabCssVars, PageTabCssVarsProps } from '../../types'

/** 标签页激活状态默认颜色 */
export const ACTIVE_COLOR = '#1890ff'

/**
 * 创建标签页CSS变量
 * @param props CSS变量属性
 * @returns 返回生成的CSS变量对象
 */
function createCssVars(props: PageTabCssVarsProps) {
  const cssVars: PageTabCssVars = {
    '--soy-primary-color': props.primaryColor, // 主色
    '--soy-primary-color1': props.primaryColor1, // 主色变体1
    '--soy-primary-color2': props.primaryColor2, // 主色变体2
    '--soy-primary-color-opacity1': props.primaryColorOpacity1, // 主色透明度1
    '--soy-primary-color-opacity2': props.primaryColorOpacity2, // 主色透明度2
    '--soy-primary-color-opacity3': props.primaryColorOpacity3, // 主色透明度3
  }

  return cssVars
}

/**
 * 根据主色创建标签页CSS变量
 * @param primaryColor 主色值
 * @returns 返回生成的CSS变量对象
 */
export function createTabCssVars(primaryColor: string) {
  const cssProps: PageTabCssVarsProps = {
    primaryColor,
    primaryColor1: transformColorWithOpacity(primaryColor, 0.1, '#ffffff'), // 主色与白色混合
    primaryColor2: transformColorWithOpacity(primaryColor, 0.3, '#000000'), // 主色与黑色混合
    primaryColorOpacity1: addColorAlpha(primaryColor, 0.1), // 10%透明度
    primaryColorOpacity2: addColorAlpha(primaryColor, 0.15), // 15%透明度
    primaryColorOpacity3: addColorAlpha(primaryColor, 0.3), // 30%透明度
  }

  return createCssVars(cssProps)
}
