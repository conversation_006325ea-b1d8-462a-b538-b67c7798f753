import type { RouteMeta } from 'vue-router'
import ElegantVueRouter from '@elegant-router/vue/vite'
import type { RouteKey } from '@elegant-router/types'

export function setupElegantRouter() {
  return ElegantVueRouter({
    layouts: {
      base: 'src/layouts/base-layout/index.vue',
      blank: 'src/layouts/blank-layout/index.vue',
    },
    routePathTransformer(routeName, routePath) {
      const key = routeName as RouteKey

      if (key === 'login') {
        const modules: UnionKey.LoginModule[] = ['pwd-login', 'code-login', 'qrcode-login', 'reset-pwd']

        const moduleReg = modules.join('|')

        return `/login/:module(${moduleReg})?`
      }

      return routePath
    },
    onRouteMetaGen(routeName) {
      const key = routeName as RouteKey

      const constantRoutes: RouteKey[] = ['login', '403', '404', '500']

      const meta: Partial<RouteMeta> = {
        title: key,
      }

      if (constantRoutes.includes(key)) {
        meta.constant = true
      }

      return meta
    },
  })
}
