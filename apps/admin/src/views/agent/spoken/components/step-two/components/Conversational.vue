<script setup lang="ts">
const Dialogue = defineModel<AgentApi.OralCommunicationDialogueTaskInput>('dialogue')

watch(Dialogue, (val) => {
  console.log(val)
})
</script>

<template>
  <!-- 对话目标 -->
  <NFormItem
    label="对话目标"
    path="DialogueTarget"
    :rule="[
      {
        required: true,
        message: '请输入对话目标',
        trigger: ['blur', 'input'],
      },
    ]"
  >
    <NInput
      v-model:value="Dialogue!.DialogueTarget"
      type="textarea"
      placeholder="请输入对话目标"
      :rows="4"
      clearable
      maxlength="1000"
      show-count
    />
  </NFormItem>
  <!-- 有效回应标准 -->
  <NFormItem
    label="有效回应标准"
    path="ValidRespond"
    :rule="[
      {
        required: true,
        message: '请输入有效回应标准',
        trigger: ['blur', 'input'],
      },
    ]"
  >
    <NInput
      v-model:value="Dialogue!.ValidRespond"
      type="textarea"
      placeholder="判断回应合格的条件，如必须包含「书名＋用途＋归还时间」，语气礼貌（使用'请''麻烦'）"
      :rows="4"
      clearable
      maxlength="1000"
      show-count
    />
  </NFormItem>
  <!-- 追问话术 -->
  <NFormItem
    label="追问话术"
    path="Asked"
    :rule="[
      {
        required: false,
        message: '请输入追问话术',
        trigger: ['blur', 'input'],
      },
    ]"
  >
    <NInput
      v-model:value="Dialogue!.Asked"
      type="textarea"
      placeholder="如果你没听清对方需求，会怎么反问？示例：'你能再说清楚想借哪本书吗？'"
      :rows="4"
      clearable
      maxlength="1000"
      show-count
    />
  </NFormItem>
</template>

<style scoped>

</style>
