<script setup lang="ts">
import QuestionItemContainer from '@sa/components/common/questions/question-item-container.vue'
import EditQuestionDrawer from './components/edit-question-drawer/index.vue'
import ChangeUpModal from './components/change-up-modal/index.vue'

defineOptions({
  name: 'RightView',
})

// 定义props
const props = defineProps<{
  questionsList: Question.ProcessedQuestionData[]
  currentProgress: { current: number, total: number, description: string }
  isGenerating: boolean
  generationError: string
}>()

// 定义emits
const emit = defineEmits<{
  deleteQuestion: [questionId: string]
  clearAll: []
}>()

// 是否显示题目结果
const showQuestionResult = computed(() => props.questionsList.length > 0 || props.isGenerating)

// 生成标题信息
const titleInfo = computed(() => {
  if (props.isGenerating) {
    return props.currentProgress.description || '正在生成题目...'
  }
  if (props.questionsList.length > 0) {
    const typeCount = props.questionsList.reduce((acc, question) => {
      acc[question.typeText] = (acc[question.typeText] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const typeDescriptions = Object.entries(typeCount)
      .map(([type, count]) => `${count} 道 ${type}`)
      .join(' ')

    return `AI一共为您生成了 ${typeDescriptions}`
  }
  return ''
})

// 滚动容器引用
const scrollbarRef = ref()

// 滚动到底部的方法
function scrollToBottom() {
  nextTick(() => {
    console.log('scrollbarRef.value:', scrollbarRef.value)
    if (scrollbarRef.value) {
      // 尝试不同的滚动方法
      scrollbarRef.value.scrollTop()
      return
      const scrollContainer = scrollbarRef.value.containerRef || scrollbarRef.value.$el
      console.log('scrollContainer:', scrollContainer)
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
        console.log(123456)
      }
    }
  })
}

// 监听题目列表变化，自动滚动到底部
watch(
  () => props.questionsList,
  (newQuestions) => {
    // 当题目列表有内容时，滚动到底部
    // 这里改为只要有题目就滚动，因为题目可能是批量替换而不是逐个添加
    if (newQuestions.length > 0) {
      console.log(123)
      scrollToBottom()
    }
  },
  { deep: true },
)

// 监听生成状态变化，当生成完成时滚动到底部
watch(
  () => props.isGenerating,
  (isGenerating, wasGenerating) => {
    // 当从生成中变为生成完成时，滚动到底部
    if (wasGenerating && !isGenerating && props.questionsList.length > 0) {
      scrollToBottom()
    }
  },
)

// 抽屉显示状态
const showEditDrawer = ref(false)
const currentEditQuestion = ref<Question.ProcessedQuestionData | null>(null)

// 换一题 modal 显示状态
const showChangeUpModal = ref(false)
const currentReplaceQuestion = ref<Question.ProcessedQuestionData | null>(null)

// 操作方法
function clearAll() {
  emit('clearAll')
}

function addToBank() {
  console.log('加入题库')
}

function replaceQuestion(question: Question.ProcessedQuestionData) {
  console.log('换一题', question)
  currentReplaceQuestion.value = question
  showChangeUpModal.value = true
}

function addToTest() {
  console.log('加入题库')
}

function editQuestion(question: Question.ProcessedQuestionData) {
  currentEditQuestion.value = question
  showEditDrawer.value = true
}

// 处理抽屉保存事件
function handleEditSave(question: Question.ProcessedQuestionData) {
  console.log('保存编辑的题目:', question)
  // 这里可以添加保存逻辑，比如更新题目列表或发送到服务器
}

// 处理抽屉取消事件
function handleEditCancel() {
  console.log('取消编辑')
  currentEditQuestion.value = null
}

function handlePositiveClick(questionId: string) {
  console.log('删除题目:', questionId)
  emit('deleteQuestion', questionId)
}

// 处理换一题 modal 事件
function handleReplaceContent(content: string) {
  console.log('替换题目内容:', content)
  // 这里可以添加替换题目的逻辑
  currentReplaceQuestion.value = null
}

function handleInsertContent(content: string) {
  console.log('插入题目内容:', content)
  // 这里可以添加插入题目的逻辑
  currentReplaceQuestion.value = null
}
</script>

<template>
  <div class="h-full w-full rounded-8px bg-white p-12px">
    <div v-if="showQuestionResult" class="felx h-full flex-col">
      <div class="shrink-0">
        <!-- 顶部标题栏 -->
        <div class="mb-6 flex items-center justify-between border-b border-gray-100 pb-4">
          <div class="flex items-center gap-2">
            <div class="h-4 w-4 rounded-sm bg-blue-500" />
            <span class="text-lg text-gray-800 font-medium">本次出题结果</span>
          </div>
          <div class="flex gap-3">
            <NPopconfirm
              @positive-click="clearAll"
              @negative-click="() => {}"
            >
              <template #trigger>
                <NButton
                  quaternary
                  type="primary"
                  class="border border-blue-400 rounded-full text-blue-500 hover:bg-blue-50"
                >
                  一键清空
                </NButton>
              </template>
              确定要清空所有题目吗？
            </NPopconfirm>
            <NButton
              type="primary"
              class="rounded-full"
              @click="addToBank"
            >
              加入题库
            </NButton>
          </div>
        </div>

        <!-- AI生成信息 -->
        <div class="mb-6 rounded-lg bg-gray-50 px-4 py-3 text-gray-600">
          {{ titleInfo }}
        </div>
      </div>
      <div class="min-h-0 shrink-1">
        <NScrollbar ref="scrollbarRef" class="h-full">
          <!-- 题目卡片列表 -->
          <div v-for="(question, index) in questionsList" :key="question.id" class="mb-6">
            <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
              <!-- 题目类型和操作按钮 -->
              <div class="mb-4 flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <NCheckbox />
                  <span class="text-gray-600">{{ question.typeText }}</span>
                  <span class="text-sm text-gray-400">第{{ index + 1 }}题</span>
                </div>
                <div class="flex items-center gap-4 text-blue-500">
                  <NButton text class="flex items-center gap-1" type="primary" @click="() => replaceQuestion(question)">
                    <SvgIcon icon="mdi:refresh" class="h-4 w-4" />
                    换一题
                  </NButton>
                  <NButton text class="flex items-center gap-1" type="primary" @click="addToTest">
                    <SvgIcon icon="mdi:plus" class="h-4 w-4" />
                    加入题库
                  </NButton>
                  <NButton text class="flex items-center gap-1" type="primary" @click="() => editQuestion(question)">
                    <SvgIcon icon="mdi:pencil" class="h-4 w-4" />
                    编辑
                  </NButton>
                  <NPopconfirm
                    @positive-click="() => handlePositiveClick(question.id)"
                    @negative-click="() => {}"
                  >
                    <template #trigger>
                      <NButton text class="flex items-center gap-1" type="primary">
                        <SvgIcon icon="mdi:delete" class="h-4 w-4" />
                        删除
                      </NButton>
                    </template>
                    确定要删除吗？
                  </NPopconfirm>
                </div>
              </div>

              <!-- 选项 - 根据题型动态渲染组件 -->
              <div v-if="question.options" class="mb-6">
                <QuestionItemContainer :item-info="question" type="preview" />
              </div>

              <!-- 正确答案 -->
              <div class="mb-6">
                <span class="text-green-600 font-medium">正确答案：{{ question.correctAnswer }}</span>
              </div>

              <!-- 答案解析 -->
              <div class="mb-6 flex">
                <div class="mb-2 mr-4 text-gray-800 font-medium">
                  答案解析:
                </div>
                <div v-katex class="flex flex-1 text-gray-700 leading-relaxed space-y-1" v-html="question.analysis" />
              </div>

              <!-- 知识点 -->
              <div v-if="question.knowledgePoints.length > 0" class="flex flex-wrap items-center gap-2">
                <span class="text-gray-600">知识点：</span>
                <div class="flex flex-wrap gap-2">
                  <NTag
                    v-for="point in question.knowledgePoints"
                    :key="point"
                    type="info"
                  >
                    {{ point }}
                  </NTag>
                </div>
              </div>
            </div>
          </div>
        </NScrollbar>
      </div>
    </div>

    <!-- 内容为空状态 -->
    <div v-else class="text-group_3 empty h-full flex flex-col items-center justify-center">
      <img src="@/assets/imgs/empty.png" alt="" class="h-272px w-272px">
      <span class="text-center text-20px">出题指南</span>
      <span class="mt-14px text-center text-14px text-[rgba(172,172,172,1)] font-normal">
        1.选择出题方式，境好必要的信息<br>
        2.系统将自动保存您的出题设置<br>
        3. 点击”立即出题”，题目结果将显示在右侧
      </span>
    </div>

    <!-- 编辑题目抽屉 -->
    <EditQuestionDrawer
      v-model:visible="showEditDrawer"
      :question="currentEditQuestion"
      @save="handleEditSave"
      @cancel="handleEditCancel"
    />

    <!-- 换一题 modal -->
    <ChangeUpModal
      v-model:show="showChangeUpModal"
      @replace="handleReplaceContent"
      @insert="handleInsertContent"
    />
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
