<script setup lang="ts">
import { useRouterPush } from '@sa/hooks'

const { routerBack } = useRouterPush()

function handleBack() {
  routerBack()
}
</script>

<template>
  <div
    class="absolute left-16px top-16px z-10 flex cursor-pointer items-center gap-3 rounded-lg bg-white/80 px-4 py-2 backdrop-blur-sm transition-all duration-300 hover:bg-white/95 hover:-translate-x-0.5"
    @click="handleBack"
  >
    <div class="h-3.5 w-3.5 flex items-center justify-center">
      <svg width="8" height="14" viewBox="0 0 8 14" fill="none">
        <path
          d="M7 1L1 7L7 13"
          stroke="#333333"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
    <span class="text-base text-[#333333] font-medium">返回</span>
  </div>
</template>

<style scoped>

</style>
