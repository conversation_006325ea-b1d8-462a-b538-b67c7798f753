import { onUnmounted, readonly, ref } from 'vue'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { PCMPlayer, base64ToArrayBuffer, getServiceBaseURL } from '@sa/utils'

/**
 * 语音播放状态接口
 */
export interface VoicePlayerState {
  /** 当前播放的消息ID */
  currentMessageId: string
  /** 是否正在播放 */
  isPlaying: boolean
  /** PCM播放器实例 */
  pcmPlayerInstance: PCMPlayer | null
  /** 当前请求控制器 */
  currentAbortController: AbortController | null
}

/**
 * 语音播放配置选项
 */
export interface VoicePlayerOptions {
  /** 音频采样率 */
  sampleRate?: number
  /** 刷新时间 */
  flushTime?: number
  /** 是否使用代理 */
  isHttpProxy?: boolean
  /** 服务基础URL */
  baseURL?: string
}

/**
 * 语音播放事件回调
 */
export interface VoicePlayerCallbacks {
  /** 播放状态变化回调 */
  onPlayStateChange?: (item: Chat.MessageItem, isPlaying: boolean) => void
  /** 播放结束回调 */
  onPlayEnded?: (item: Chat.MessageItem) => void
  /** 播放错误回调 */
  onPlayError?: (error: Error, item: Chat.MessageItem) => void
}

/**
 * 语音播放Hook
 * 提供完整的语音播放功能，包括TTS流式播放、播放控制等
 */
export default function useVoicePlayer(
  options: VoicePlayerOptions = {},
  callbacks: VoicePlayerCallbacks = {},
) {
  const {
    sampleRate = 24000,
    flushTime = 100, // 改为100ms，避免过于频繁的刷新
    isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y',
  } = options

  const {
    onPlayStateChange,
    onPlayEnded,
    onPlayError,
  } = callbacks

  // 获取服务基础URL
  const { baseURL } = options.baseURL
    ? { baseURL: options.baseURL }
    : getServiceBaseURL(import.meta.env, isHttpProxy)

  // 播放状态管理
  const state = ref<VoicePlayerState>({
    currentMessageId: '',
    isPlaying: false,
    pcmPlayerInstance: null,
    currentAbortController: null,
  })

  /**
   * 检查指定消息是否正在播放
   */
  function isMessagePlaying(messageId: string): boolean {
    return state.value.currentMessageId === messageId && state.value.isPlaying
  }

  /**
   * 停止当前播放
   */
  function stopCurrentPlayback(item?: Chat.MessageItem) {
    // 中止当前请求
    if (state.value.currentAbortController) {
      state.value.currentAbortController.abort()
      state.value.currentAbortController = null
    }

    // 停止并销毁PCM播放器
    if (state.value.pcmPlayerInstance) {
      state.value.pcmPlayerInstance.pause()
      state.value.pcmPlayerInstance.destroy()
      state.value.pcmPlayerInstance = null
    }

    // 更新状态
    const wasPlaying = state.value.isPlaying
    state.value.isPlaying = false
    state.value.currentMessageId = ''

    // 触发回调
    if (wasPlaying && item) {
      onPlayStateChange?.(item, false)
      onPlayEnded?.(item)
    }
  }

  /**
   * 创建PCM播放器实例
   */
  function createPCMPlayer(item: Chat.MessageItem): PCMPlayer {
    const player = new PCMPlayer({
      sampleRate,
      flushTime,
      onstatechange: () => {
        // PCM播放器状态变化处理
      },
      onended: () => {
        // 检查是否真的应该结束播放
        if (player.getActiveSourcesCount() === 0 && !player.hasData()) {
          // 所有音频段都播放完毕，触发播放结束
          state.value.isPlaying = false
          onPlayStateChange?.(item, false)
          onPlayEnded?.(item)

          // 清理播放器实例
          if (state.value.pcmPlayerInstance) {
            state.value.pcmPlayerInstance.destroy()
            state.value.pcmPlayerInstance = null
          }
          state.value.currentMessageId = ''
        }
      },
    })
    return player
  }

  /**
   * 获取音频数据流
   */
  async function fetchAudioData(text: string, item: Chat.MessageItem): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      fetchEventSource(`${baseURL}/AgentCommon/AgentCommon/TextChangeVoice`, {
        signal: state.value.currentAbortController?.signal,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ Text: text }),
        openWhenHidden: true,
        onopen: async (response) => {
          if (response.ok) {
            state.value.isPlaying = true
            onPlayStateChange?.(item, true)
          }
          else {
            reject(new Error(`HTTP ${response.status}: ${response.statusText}`))
          }
        },
        onmessage: async (msg) => {
          try {
            const { Content, Success } = JSON.parse(msg.data)

            if (!Success) {
              state.value.currentAbortController?.abort()
              const error = new Error(Content)
              onPlayError?.(error, item)
              reject(error)
              return
            }

            if (Content.startsWith('DONE')) {
              resolve()
              return
            }

            // 解码并播放音频数据
            const audioBuffer = await base64ToArrayBuffer(Content) as ArrayBuffer
            state.value.pcmPlayerInstance?.feed(audioBuffer)
          }
          catch (error) {
            const err = error instanceof Error ? error : new Error('音频数据处理失败')
            onPlayError?.(err, item)
            reject(err)
          }
        },
        onclose: () => {
          resolve()
        },
        onerror: (err) => {
          const error = err instanceof Error ? err : new Error('音频流请求失败')
          onPlayError?.(error, item)
          reject(error)
        },
      })
    })
  }

  /**
   * 处理语音播放
   */
  async function handleVoicePlay(item: Chat.MessageItem): Promise<void> {
    try {
      // 验证内容
      if (!item.content?.trim()) {
        const error = new Error('没有可播放的内容')
        onPlayError?.(error, item)
        return
      }

      // 如果点击的是不同的消息，开始新的播放
      if (state.value.currentMessageId !== item.key) {
        // 停止当前播放
        stopCurrentPlayback()

        // 更新当前播放的消息ID
        state.value.currentMessageId = item.key

        // 创建新的控制器和播放器
        state.value.currentAbortController = new AbortController()
        state.value.pcmPlayerInstance = createPCMPlayer(item)

        // 开始获取音频数据
        await fetchAudioData(item.content, item)
      }
      else {
        // 如果点击的是同一个消息，切换播放/暂停状态
        if (!state.value.pcmPlayerInstance) {
          return
        }

        if (state.value.isPlaying) {
          state.value.pcmPlayerInstance.pause()
          state.value.isPlaying = false
        }
        else {
          state.value.pcmPlayerInstance.continue()
          state.value.isPlaying = true
        }

        onPlayStateChange?.(item, state.value.isPlaying)
      }
    }
    catch (error) {
      console.error('语音播放失败:', error)
      const err = error instanceof Error ? error : new Error('语音播放失败，请重试')
      onPlayError?.(err, item)
      stopCurrentPlayback(item)
    }
  }

  /**
   * 暂停播放
   */
  function pausePlayback(item?: Chat.MessageItem): void {
    if (state.value.pcmPlayerInstance && state.value.isPlaying) {
      state.value.pcmPlayerInstance.pause()
      state.value.isPlaying = false

      if (item) {
        onPlayStateChange?.(item, false)
      }
    }
  }

  /**
   * 继续播放
   */
  function resumePlayback(item?: Chat.MessageItem): void {
    if (state.value.pcmPlayerInstance && !state.value.isPlaying) {
      state.value.pcmPlayerInstance.continue()
      state.value.isPlaying = true

      if (item) {
        onPlayStateChange?.(item, true)
      }
    }
  }

  /**
   * 获取当前播放状态
   */
  function getCurrentPlayingState() {
    return {
      currentMessageId: state.value.currentMessageId,
      isPlaying: state.value.isPlaying,
      hasActivePlayer: !!state.value.pcmPlayerInstance,
    }
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    stopCurrentPlayback()
  })

  return {
    // 状态
    state: readonly(state),

    // 方法
    handleVoicePlay,
    stopCurrentPlayback,
    pausePlayback,
    resumePlayback,
    isMessagePlaying,
    getCurrentPlayingState,
  }
}
