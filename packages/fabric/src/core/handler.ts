import type { fabric } from 'fabric'
import { getTransformedPosX, getTransformedPosY } from '../util/transformedPos'
import { resetTmpText } from '../util/handlerText'
import type FabricDrawingBoard from '../types'

export function initText(fdb: FabricDrawingBoard) {
  if (!fdb._fcvs) {
    return
  }
  if (!fdb._tmpText) {
    fdb._tmpText = new fdb._fabric.Textbox('', {
      left: getTransformedPosX(fdb._fcvs, fdb._mouseFrom.x),
      top: getTransformedPosY(fdb._fcvs, fdb._mouseFrom.y),
      fontSize: fdb._fontSize,
      fill: fdb._strokeColor,
      hasControls: false,
      editable: true,
      width: 30,
      backgroundColor: fdb._textEditBgColor,
      selectable: false,
    })

    fdb._fcvs.add(fdb._tmpText)
    fdb._tmpText.enterEditing()
    fdb._tmpText.hiddenTextarea?.focus()
  }
  else {
    resetTmpText(fdb)
  }
}

function drawing(fdb: FabricDrawingBoard, drawObj: fabric.Object) {
  if (!fdb._fcvs) {
    return
  }
  drawObj.selectable = false
  if (fdb._tmpDrawingObj) {
    fdb._fcvs.remove(fdb._tmpDrawingObj)
  }
  fdb._fcvs.add(drawObj)
  fdb._tmpDrawingObj = drawObj
}

export function initLine(fdb: FabricDrawingBoard) {
  if (!fdb._fcvs) {
    return
  }
  drawing(
    fdb,
    new fdb._fabric.Line(
      [
        getTransformedPosX(fdb._fcvs, fdb._mouseFrom.x),
        getTransformedPosY(fdb._fcvs, fdb._mouseFrom.y),
        getTransformedPosX(fdb._fcvs, fdb._mouseTo.x),
        getTransformedPosY(fdb._fcvs, fdb._mouseTo.y),
      ],
      {
        fill: fdb._fillColor,
        stroke: fdb._strokeColor,
        strokeWidth: fdb._strokeSize,
      },
    ),
  )
}

export function initRect(fdb: FabricDrawingBoard) {
  if (!fdb._fcvs) {
    return
  }
  const left = fdb._mouseTo.x > fdb._mouseFrom.x ? getTransformedPosX(fdb._fcvs, fdb._mouseFrom.x) : getTransformedPosX(fdb._fcvs, fdb._mouseTo.x)
  const top = fdb._mouseTo.y > fdb._mouseFrom.y ? getTransformedPosY(fdb._fcvs, fdb._mouseFrom.y) : getTransformedPosY(fdb._fcvs, fdb._mouseTo.y)
  const width = Math.abs(fdb._mouseTo.x - fdb._mouseFrom.x)
  const height = Math.abs(fdb._mouseTo.y - fdb._mouseFrom.y)
  drawing(
    fdb,
    new fdb._fabric.Rect({
      left,
      top,
      width,
      height,
      stroke: fdb._strokeColor,
      fill: fdb._fillColor,
      strokeWidth: fdb._strokeSize,
    }),
  )
}

export function initCircle(fdb: FabricDrawingBoard) {
  if (!fdb._fcvs) {
    return
  }
  const left = fdb._mouseFrom.x < fdb._mouseTo.x ? getTransformedPosX(fdb._fcvs, fdb._mouseFrom.x) : getTransformedPosX(fdb._fcvs, fdb._mouseTo.x)
  const top = fdb._mouseFrom.y < fdb._mouseTo.y ? getTransformedPosY(fdb._fcvs, fdb._mouseFrom.y) : getTransformedPosY(fdb._fcvs, fdb._mouseTo.y)
  const rx = Math.abs(fdb._mouseTo.x - fdb._mouseFrom.x) / 2
  const ry = Math.abs(fdb._mouseTo.y - fdb._mouseFrom.y) / 2

  drawing(
    fdb,
    new fdb._fabric.Ellipse({
      left,
      top,
      stroke: fdb._strokeColor,
      fill: fdb._fillColor,
      strokeWidth: fdb._strokeSize,
      rx,
      ry,
    }),
  )
}

export function initMove(fdb: FabricDrawingBoard) {
  if (!fdb._fcvs) {
    return
  }
  const vpt = fdb._fcvs.viewportTransform
  vpt[4]! += fdb._mouseTo.x - fdb._mouseFrom.x
  vpt[5]! += fdb._mouseTo.y - fdb._mouseFrom.y
  fdb._fcvs.requestRenderAll()
  fdb._mouseFrom.x = fdb._mouseTo.x
  fdb._mouseFrom.y = fdb._mouseTo.y
}
