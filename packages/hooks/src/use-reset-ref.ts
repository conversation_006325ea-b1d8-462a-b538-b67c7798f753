import { ref } from 'vue'

/**
 * 克隆给定的值。如果值是null或不是对象类型，则直接返回该值。
 * 对于对象类型，通过JSON序列化和反序列化的方式进行克隆。
 * 注意：这种方法不适用于具有循环引用或无法序列化的对象。
 *
 * @param value 任意类型的值，将被克隆。
 * @returns 克隆后的值，如果输入是null或非对象类型，则返回原始值。
 */
function defaultClone(value: any) {
  // 检查值是否为null或非对象类型，如果是，则直接返回值
  if (value === null || typeof value !== 'object')
    return value
  // 对于对象类型，通过JSON序列化和反序列化的方式进行克隆
  return JSON.parse(JSON.stringify(value))
}
/**
 * 使用useResetRef钩子来管理一个可以重置的响应式状态
 * 这个钩子的主要作用是维护一个状态，并提供一个方法来重置这个状态到初始值
 *
 * @param value - 初始状态的值
 * @param clone - 克隆函数，用于创建初始值的深拷贝，默认为defaultClone函数
 * @returns 返回一个常量数组，包含两个元素：
 *          - state: 一个响应式状态对象，用于读取和修改状态
 *          - reset: 一个函数，用于将状态重置为初始值
 */
export function useResetRef<T>(value: T, clone = defaultClone) {
  // 创建初始值的深拷贝，以避免原始值被直接修改
  const initialValue = clone(value)
  // 创建一个响应式状态对象，用于存储当前状态值
  const state = ref(value)
  /**
   * 重置函数，用于将状态值重置为初始值
   * 它通过调用clone函数来创建初始值的一个新深拷贝，并将其赋值给状态对象
   */
  const reset = () => {
    state.value = clone(initialValue)
  }

  // 返回一个常量数组，包含状态对象和重置函数，使得它们可以在组件或其他地方使用
  return [state, reset] as const
}
