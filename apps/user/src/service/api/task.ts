import { request } from '@/service/request'

/**
 * 获取教师主页任务列表
 * @param data 请求参数，类型为Task.AgentTeacherHomePageListInputRequest
 * @returns 返回Promise，包含学生主页任务列表响应数据，类型为Task.AgentStudentHomePageAgentTaskOuputResponse
 */
export function fetchGetStudentHomePageAgentTaskList(data: Task.AgentTeacherHomePageListInputRequest) {
  return request<Task.AgentStudentHomePageAgentTaskOuputResponse>({
    url: 'AgentStudentHomePage/AgentStudentHomePage/GetStudentHomePageAgentTaskList',
    method: 'POST',
    data,
  })
}
/**
 * 获取获取AI对话内容记录
 * @param data 请求参数，
 * @returns
 */
export function fetchGetAIDialogueContentRecord(data: Task.AgentStudentOralCommunicationDialogueRequest) {
  return request<Task.AgentTeacherHomePageAgentTaskOuputResponse>({
    url: '/AgentCommon/AgentCommon/GetAIDialogueContentRecord',
    method: 'POST',
    data,
  })
}

/**
 * 获取智能体口语交际详情
 * @param data 请求参数，
 * @returns
 */
export function fetchGetOralCommunicationDetail(data: { Id: string }) {
  return request<Task.AgentTeacherOralCommunicationGetOralCommunicationDetailInputResponse>({
    url: '/AgentTeacherOralCommunication/AgentTeacherOralCommunication/GetOralCommunicationDetail',
    method: 'POST',
    data,
  })
}

/**
 * 发送智能体口语交际对话
 * @param data 请求参数，
 * @returns
 */
export function fetchAgentStudentOralCommunicationDialogue(data: Task.AgentStudentOralCommunicationDialogueInputRequest) {
  return request<Task.AgentTeacherOralCommunicationGetOralCommunicationDetailInputResponse>({
    url: '/AgentStudentOralCommunication/AgentStudentOralCommunication/AgentStudentOralCommunicationDialogue',
    method: 'POST',
    data,
  })
}

/**
 * 智能体_学生端口语交际提交入参
 * @param data 请求参数，
 * @returns
 */
export function fetchAgentStudentOralCommunicationSubmit(data: Task.AgentStudentOralCommunicationSubmitInputRequest) {
  return request({
    url: '/AgentStudentOralCommunication/AgentStudentOralCommunication/AgentStudentOralCommunicationSubmit',
    method: 'POST',
    data,
  })
}
/**
 * 获取学生评估结果
 * @param data
 */
export function fetchGetOralCommunicationStudentResult(data: {
  /** 智能体任务Id */
  AgentTaskId: string
  /** 学生Id */
  StudentId: string
}) {
  return request<{ AssessmentResult: string }>({
    url: 'AgentTeacherOralCommunication/AgentTeacherOralCommunication/GetOralCommunicationStudentResult',
    method: 'POST',
    data,
  })
}
