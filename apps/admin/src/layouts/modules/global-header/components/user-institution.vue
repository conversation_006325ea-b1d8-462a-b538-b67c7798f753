<script setup lang="ts">
import { h } from 'vue'
import { localStg } from '@sa/utils'
import { useAuthStore } from '@/store/modules/auth'

defineOptions({
  name: 'UserSchool',
})
// 提取学校数据为常量

const { userInfo, activeState, gradeList } = useAuthStore()

// 创建带 Tooltip 的选项渲染函数
function createOptionWithTooltip(option: { label: string, value: string }) {
  return () =>
    h(
      NTooltip,
      {
        placement: 'top',
      },
      {
        trigger: () => h('div', { class: 'ellipsis-text' }, option.label),
        default: () => option.label,
      },
    )
}

const roleValue = ref()
const gradeValue = ref('')

const subjectValue = ref('')
const subjectOptions = computed(() => {
  return (userInfo.subjectList ?? []).map(v => ({
    label: createOptionWithTooltip({
      label: v.Name,
      value: v.Id,
    }),
    value: v.Id,
    disabled: subjectValue.value === v.Id,
  }))
})

const roleOptions = computed(() =>
  (userInfo.RoleIdList ?? []).map(role => ({
    label: createOptionWithTooltip({
      label: role.RoleName,
      value: role.RoleId,
    }),
    value: role.RoleId,
    disabled: roleValue.value === role.RoleId,
  })),
)

const gradeOptions = computed(() =>
  (gradeList ?? []).map(item => ({
    label: item.Gradename,
    value: item.Id,
    disabled: gradeValue.value === `${item.Id}`,
  })),
)

function handleSubjectChange(_value: string | null) {
  // 这里可以添加您的选择逻辑
  localStg.set('defaultSubjectId', _value!)
  window.location.reload()
}

function handleRoleChange(_value: string | null) {
  // 这里可以添加您的选择逻辑
  localStg.set('defaultRoleId', _value!)
  window.location.reload()
}
function handleGradeChange(_value: string | null) {
  // 这里可以添加您的选择逻辑
  localStg.set('defaultGradeId', _value!)
  window.location.reload()
}
</script>

<template>
  <NSpace>
    <!-- 年级 -->
    <NSelect
      v-model:value="activeState.grade"
      :bordered="false"
      :options="gradeOptions"
      class="w-120px"
      @update:value="handleGradeChange"
    />
    <!-- 角色 -->
    <NSelect
      v-if="false"
      v-model:value="activeState.roleId"
      :bordered="false"
      :options="roleOptions"
      class="w-120px"
      @update:value="handleRoleChange"
    />
    <!-- 学科 -->
    <NSelect
      v-model:value="activeState.subjectId"
      :bordered="false"
      :options="subjectOptions"
      class="w-120px"
      @update:value="handleSubjectChange"
    />
  </NSpace>
</template>
