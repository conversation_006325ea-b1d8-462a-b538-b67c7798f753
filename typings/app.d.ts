/* eslint-disable ts/consistent-type-definitions */
/** 应用程序全局命名空间 */
declare namespace App {
  /** 主题命名空间 */
  namespace Theme {
      type ColorPaletteNumber = import('@sa/color').ColorPaletteNumber

      /** 主题设置 */
      interface ThemeSetting {
        /** 主题方案 */
        themeScheme: UnionKey.ThemeScheme
        /** 灰度模式 */
        grayscale: boolean
        /** 色盲模式 */
        colourWeakness: boolean
        /** 是否推荐颜色 */
        recommendColor: boolean
        /** 主题颜色 */
        themeColor: string
        /** 其他颜色 */
        otherColor: OtherColor
        /** 是否跟随主色 */
        isInfoFollowPrimary: boolean
        /** 重置缓存策略 */
        resetCacheStrategy: UnionKey.ResetCacheStrategy
        /** 布局 */
        layout: {
          /** 布局模式 */
          mode: UnionKey.ThemeLayoutMode
          /** 滚动模式 */
          scrollMode: UnionKey.ThemeScrollMode
          /**
           * 是否反转水平混合布局
           *
           * 如果为 true，左侧为垂直子级菜单，顶部为水平一级菜单
           */
          reverseHorizontalMix: boolean
        }
        /** 页面 */
        page: {
          /** 是否显示页面过渡动画 */
          animate: boolean
          /** 页面动画模式 */
          animateMode: UnionKey.ThemePageAnimateMode
        }
        /** 页眉 */
        header: {
          /** 页眉高度 */
          height: number
          /** 页眉面包屑 */
          breadcrumb: {
            /** 是否显示面包屑 */
            visible: boolean
            /** 是否显示面包屑图标 */
            showIcon: boolean
          }
          /** 多语言 */
          multilingual: {
            /** 是否显示多语言 */
            visible: boolean
          }
        }
        /** 标签 */
        tab: {
          /** 是否显示标签 */
          visible: boolean
          /**
           * 是否缓存标签
           *
           * 如果缓存，刷新页面时标签将从本地存储中获取
           */
          cache: boolean
          /** 标签高度 */
          height: number
          /** 标签模式 */
          mode: UnionKey.ThemeTabMode
        }
        /** 固定页眉和标签 */
        fixedHeaderAndTab: boolean
        /** 侧边栏 */
        sider: {
          /** 反转侧边栏 */
          inverted: boolean
          /** 侧边栏宽度 */
          width: number
          /** 折叠侧边栏宽度 */
          collapsedWidth: number
          /** 混合布局时侧边栏宽度 */
          mixWidth: number
          /** 混合布局时折叠侧边栏宽度 */
          mixCollapsedWidth: number
          /** 混合布局时子菜单宽度 */
          mixChildMenuWidth: number
        }
        /** 页脚 */
        footer: {
          /** 是否显示页脚 */
          visible: boolean
          /** 是否固定页脚 */
          fixed: boolean
          /** 页脚高度 */
          height: number
          /** 布局为 'horizontal-mix' 时是否将页脚浮动到右侧 */
          right: boolean
        }
        /** 水印 */
        watermark: {
          /** 是否显示水印 */
          visible: boolean
          /** 水印文本 */
          text: string
        }
        /** 定义一些主题设置令牌，将转换为 CSS 变量 */
        tokens: {
          light: ThemeSettingToken
          dark?: {
            [K in keyof ThemeSettingToken]?: Partial<ThemeSettingToken[K]>;
          }
        }
      }

      interface OtherColor {
        info: string
        success: string
        warning: string
        error: string
      }

      interface ThemeColor extends OtherColor {
        primary: string
      }

      type ThemeColorKey = keyof ThemeColor

      type ThemePaletteColor = {
        [key in ThemeColorKey | `${ThemeColorKey}-${ColorPaletteNumber}`]: string;
      }

      type BaseToken = Record<string, Record<string, string>>

      interface ThemeSettingTokenColor {
        /** 进度条颜色，如果不设置，则使用主色 */
        'nprogress'?: string
        'container': string
        'layout': string
        'inverted': string
        'base-text': string
      }

      interface ThemeSettingTokenBoxShadow {
        header: string
        sider: string
        tab: string
      }

      interface ThemeSettingToken {
        colors: ThemeSettingTokenColor
        boxShadow: ThemeSettingTokenBoxShadow
      }

      type ThemeTokenColor = ThemePaletteColor & ThemeSettingTokenColor

      /** 主题令牌 CSS 变量 */
      type ThemeTokenCSSVars = {
        colors: ThemeTokenColor & { [key: string]: string }
        boxShadow: ThemeSettingTokenBoxShadow & { [key: string]: string }
      }
  }

  /** 全局命名空间 */
  namespace Global {
      type VNode = import('vue').VNode
      type RouteLocationNormalizedLoaded = import('vue-router').RouteLocationNormalizedLoaded
      type RouteKey = import('@elegant-router/types').RouteKey
      type RouteMap = import('@elegant-router/types').RouteMap
      type RoutePath = import('@elegant-router/types').RoutePath
      type LastLevelRouteKey = import('@elegant-router/types').LastLevelRouteKey

      /** 全局页眉属性 */
      interface HeaderProps {
        /** 是否显示 logo */
        showLogo?: boolean
        /** 是否显示菜单切换按钮 */
        showMenuToggler?: boolean
        /** 是否显示菜单 */
        showMenu?: boolean
      }

      /** 全局菜单 */
      type Menu = {
        /**
         * 菜单键
         *
         * 等于路由键
         */
        key: string
        /** 菜单标签 */
        label: string
        /** 路由键 */
        routeKey: RouteKey
        /** 路由路径 */
        routePath: RoutePath
        /** 菜单图标 */
        icon?: () => VNode
        /** 菜单子项 */
        children?: Menu[]
      }

      type Breadcrumb = Omit<Menu, 'children'> & {
        options?: Breadcrumb[]
      }

      /** 标签路由 */
      type TabRoute = Pick<RouteLocationNormalizedLoaded, 'name' | 'path' | 'meta'> &
        Partial<Pick<RouteLocationNormalizedLoaded, 'fullPath' | 'query' | 'matched'>>

      /** 全局标签 */
      type Tab = {
        /** 标签 ID */
        id: string
        /** 标签标签 */
        label: string
        /**
         * 新标签标签
         *
         * 如果设置，标签标签将被替换为此值
         */
        newLabel?: string
        /**
         * 旧标签标签
         *
         * 重置标签标签时，标签标签将被替换为此值
         */
        oldLabel?: string
        /** 标签路由键 */
        routeKey: LastLevelRouteKey
        /** 标签路由路径 */
        routePath: RouteMap[LastLevelRouteKey]
        /** 标签路由完整路径 */
        fullPath: string
        /** 标签固定索引 */
        fixedIndex?: number | null
        /**
         * 标签图标
         *
         * Iconify 图标
         */
        icon?: string
        /**
         * 标签本地图标
         *
         * 本地图标
         */
        localIcon?: string
      }

      /** 表单规则 */
      type FormRule = import('naive-ui').FormItemRule

      /** 全局下拉菜单键 */
      type DropdownKey = 'closeCurrent' | 'closeOther' | 'closeLeft' | 'closeRight' | 'closeAll'
  }

  /** 服务命名空间 */
  namespace Service {
      /** 其他 baseURL 键 */
      type OtherBaseURLKey = 'adminApi' | 'appApi'

      interface ServiceConfigItem {
        /** 后端服务基础 URL */
        baseURL: string
        /** 后端服务基础 URL 的代理模式 */
        proxyPattern: string
      }

      interface OtherServiceConfigItem extends ServiceConfigItem {
        key: OtherBaseURLKey
      }

      /** 后端服务配置 */
      interface ServiceConfig extends ServiceConfigItem {
        /** 其他后端服务配置 */
        other: OtherServiceConfigItem[]
      }

      interface SimpleServiceConfig extends Pick<ServiceConfigItem, 'baseURL'> {
        other: Record<OtherBaseURLKey, string>
      }

      /** 后端服务响应数据 */
      type Response<T = unknown> = {
        /** 后端服务响应码 */
        code: string
        /** 后端服务响应消息 */
        msg: string
        /** 后端服务响应数据 */
        data: T
      }

      /** 演示后端服务响应数据 */
      type DemoResponse<T = unknown> = {
        /** 后端服务响应码 */
        status: string
        /** 后端服务响应消息 */
        message: string
        /** 后端服务响应数据 */
        result: T
      }
  }
}
