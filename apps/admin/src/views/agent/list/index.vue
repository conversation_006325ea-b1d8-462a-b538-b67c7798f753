<script setup lang="ts">
import { usePaginatedRequest, usePromiseDialog, useRouterPush } from '@sa/hooks'
import List from '@sa/components/common/list/List.vue'
import SimpleAgentCard from './components/agent-card.vue'
import {
  fetchDelOralCommunication,
  fetchGetOralCommunicationList,
  fetchOralCommunicationDetail,
} from '@/service/api/agent'
import { useAuthStore } from '@/store/modules/auth'

defineOptions({
  name: 'AiOralCommunication',
})
const { routerPushByKey, routerBack } = useRouterPush()
const authStore = useAuthStore()
const { userInfo } = authStore

const route = useRoute()
const agentId = computed(() => route.query.agentId as string)

const searchInfo = reactive<{
  ClassId: string
  InteractiveMode: number
  Name: string
}>({
  ClassId: '',
  InteractiveMode: 0,
  Name: '',
})

const {
  data: list,
  run: init,
  pagination,
  loadMore,
  loading,
  loadingMore,
} = usePaginatedRequest(
  ({ current, pageSize }) => {
    return fetchGetOralCommunicationList({
      PageIndex: current,
      PageSize: pageSize,
      AgentId: agentId.value,
      ...searchInfo,
    })
  },
  {
    transform: (data) => {
      return {
        list: data.Datas ?? [],
        total: data.TotalCount,
      }
    },
  },
)

const searchFn = useDebounceFn(init, 500)

// 分类下拉选择
const categoryOptions = [
  { label: '全部分类', value: 0 },
  { label: '指令式', value: 1 },
  { label: '对话式', value: 2 },
  { label: '辩论式', value: 3 },
]

const sortOptions = computed(() => {
  const baseOptions = [{ label: '全部班级', value: '' }]
  const classOptions = (userInfo.classList || []).map(classItem => ({
    label: classItem.ClassName,
    value: classItem.ClassId,
  }))
  return [...baseOptions, ...classOptions]
})

// 编辑
function handleEdit(task: AgentApi.OralCommunicationListOutput) {
  routerPushByKey('agent_spoken', {
    query: {
      taskId: task.Id,
      agentId: agentId.value,
    },
  })
}

const { showPromiseDialog } = usePromiseDialog()

// 删除
async function handleDelete(agent: AgentApi.OralCommunicationListOutput) {
  const { error, data } = await fetchOralCommunicationDetail({
    Id: agent.Id,
  })
  if (error) {
    return
  }

  try {
    // 是否存在问答数据
    if (data?.IsData) {
      await showPromiseDialog({
        content: '当前已有问答数据，删除后，问答数据和任务数据均会清空且无法恢复，是否继续？',
        positiveText: '继续',
      })
    }
    else {
      await showPromiseDialog({
        content: '是否确认删除？删除后无法恢复',
      })
    }
  }
  catch {
    return
  }

  const res = await fetchDelOralCommunication({
    AgentTaskId: agent.Id,
  })
  if (!res.error) {
    window.$message?.success('删除成功')
    init()
  }
}

// 查看统计
function handleViewStats(task: AgentApi.OralCommunicationListOutput) {
  routerPushByKey('agent_statistics', {
    query: {
      taskId: task.Id,
      agentId: agentId.value,
    },
  })
}

function handleBack() {
  routerBack()
}
</script>

<template>
  <div class="h-full flex-col-stretch gap-16px">
    <!-- 搜索和筛选区域 -->
    <NCard :bordered="false" size="small" class="shrink-0 card-wrapper">
      <!-- 页面头部 -->
      <div class="mb-16px flex items-center justify-between">
        <div class="flex items-center gap-8px">
          <ButtonIcon
            icon="ph:arrow-left"
            tooltip-content="返回"
            class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            @click="handleBack"
          />
          <h1 class="text-18px text-gray-900 font-semibold dark:text-gray-100">
            AI口语交际
          </h1>
        </div>
        <NButton
          size="large"
          class="!h-42px !w-128px !text-white"
          style="background: linear-gradient(90deg, #6A5EFF 0%, #B065FF 100%);"
          @click="
            routerPushByKey('agent_spoken', {
              query: { agentId },
            })
          "
        >
          新建交际任务
        </NButton>
      </div>

      <!-- 搜索和筛选控件 -->
      <NGrid responsive="screen" :x-gap="16" :y-gap="16" item-responsive>
        <!-- 分类筛选 -->
        <NGridItem span="24 s:6 m:4 l:3">
          <NSelect
            v-model:value="searchInfo.InteractiveMode"
            :options="categoryOptions"
            placeholder="选择分类"
            @update:value="init()"
          />
        </NGridItem>

        <!-- 班级筛选 -->
        <NGridItem span="24 s:6 m:4 l:3">
          <NSelect
            v-model:value="searchInfo.ClassId"
            :options="sortOptions"
            placeholder="选择班级"
            @update:value="init()"
          />
        </NGridItem>
        <!-- 搜索框 -->
        <NGridItem span="24 s:12 m:8 l:6">
          <NInput
            v-model:value="searchInfo.Name"
            placeholder="实践任务名称"
            clearable
            @input="searchFn"
          >
            <template #prefix>
              <SvgIcon icon="ph:magnifying-glass" class="text-gray-400" />
            </template>
          </NInput>
        </NGridItem>
        <NGridItem span="24 m:8 l:12">
          <!-- 列表头部信息 -->
          <div class="mb-16px flex items-center justify-end">
            <div class="text-14px text-gray-600 dark:text-gray-400">
              共 {{ pagination.total }} 条任务
            </div>
          </div>
        </NGridItem>
      </NGrid>
    </NCard>

    <div class="min-h-0 flex-1">
      <!-- 智能体列表 -->
      <List
        class="bg-white"
        :pagination="pagination"
        :loading="loading"
        :loading-more="loadingMore"
        @load-more="loadMore"
      >
        <NCard
          :bordered="false"
          size="small"
          class="sm:flex-1-hidden card-wrapper"
        >
          <!-- 智能体卡片网格 - 2列布局 -->
          <NGrid responsive="screen" :x-gap="16" :y-gap="16" item-responsive>
            <NGridItem
              v-for="(agent, index) in list"
              :key="agent.Id"
              span="24"
            >
              <SimpleAgentCard
                :index="index"
                :agent="agent"
                size="medium"
                @edit="handleEdit"
                @delete="handleDelete"
                @view-stats="handleViewStats"
              />
            </NGridItem>
          </NGrid>
        </NCard>
      </List>
    </div>
  </div>
</template>
