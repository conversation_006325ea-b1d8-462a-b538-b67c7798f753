<script setup lang="ts">
import { usePaginatedRequest, useRouterPush } from '@sa/hooks'
import BackButton from '@sa/components/common/back-button.vue'
import img1 from '@/assets/imgs/teaching-plan/img1.png'
import img2 from '@/assets/imgs/teaching-plan/img2.png'
import img3 from '@/assets/imgs/teaching-plan/img3.png'
import img4 from '@/assets/imgs/teaching-plan/img4.png'
import { fetchTeachingPlanCreateRecord } from '@/service/api/teaching-plan'
import { useAuthStore } from '@/store/modules/auth'

const { activeState } = useAuthStore()

const { data: records, loadMore } = usePaginatedRequest(
  ({ current: PageIndex, pageSize: PageSize }) => {
    return fetchTeachingPlanCreateRecord({
      PageIndex,
      PageSize,
      Grade: activeState.grade!,
    })
  },
  {
    transform: (data) => {
      return {
        list: data.Datas ?? [],
        total: data.TotalCount,
      }
    },
    initialValue: [],
  },
)

// 创建方式选项
const createMethods = [
  {
    id: 'title',
    name: '标题创建',
    color: '#8862FF',
    icon: img1,
    bgColor:
      'linear-gradient(-140deg, #E2D8FF 0%, rgba(226, 216, 255, 0.2) 100%)',
    borderColor:
      'linear-gradient(180deg, #8862FF 0%, rgba(136, 98, 255, 0) 100%)',
    iconBg:
      'linear-gradient(180deg, rgba(136, 98, 255, 0.3) 0%, rgba(136, 98, 255, 0.05) 100%)',
  },
  {
    id: 'text',
    name: '文本创建',
    color: '#4FB752',
    icon: img2,
    bgColor:
      'linear-gradient(-140deg, #E1FDC9 0%, rgba(225, 253, 201, 0.2) 100%)',
    borderColor:
      'linear-gradient(180deg, #91DD61 0%, rgba(145, 221, 97, 0) 100%)',
    iconBg:
      'linear-gradient(180deg, rgba(79, 183, 82, 0.3) 0%, rgba(79, 183, 82, 0.05) 100%)',
  },
  {
    id: 'chapter',
    name: '章节创建',
    color: '#61A5FF',
    icon: img3,
    bgColor:
      'linear-gradient(-140deg, #D5E5FF 0%, rgba(213, 229, 255, 0.2) 100%)',
    borderColor:
      'linear-gradient(180deg, #61A5FF 0%, rgba(97, 165, 255, 0) 100%)',
    iconBg:
      'linear-gradient(180deg, rgba(97, 165, 255, 0.3) 0%, rgba(97, 165, 255, 0.05) 100%)',
  },
  {
    id: 'document',
    name: '文档创建',
    color: '#ED9A15',
    icon: img4,
    bgColor:
      'linear-gradient(-140deg, #FFEBC7 0%, rgba(255, 235, 199, 0.2) 100%)',
    borderColor:
      'linear-gradient(180deg, #ED9A15 0%, rgba(237, 154, 21, 0) 100%)',
    iconBg:
      'linear-gradient(180deg, rgba(237, 154, 21, 0.3) 0%, rgba(237, 154, 21, 0.05) 100%)',
  },
]

const { routerPushByKey } = useRouterPush()

// 处理创建方式点击
function handleCreateMethod(type: number) {
  // 这里可以跳转到对应的创建页面
  routerPushByKey('teaching-plan_create', {
    query: {
      type: type.toString(),
    },
  })
}

// 处理记录点击
function handleRecordClick(record: TeachingPlanApi.GetTeachingPlanCreateRecordOutput) {
  // 这里可以跳转到详情页面
  routerPushByKey('teaching-plan_create', {
    query: {
      recordId: record.Id as string,
    },
  })
}

function onScroll(e: Event) {
  const el = e.target as HTMLElement
  if (!el) {
    return
  }
  const isEnd = Math.abs(el.scrollWidth - el.scrollLeft - el.clientWidth) < 1
  if (isEnd) {
    loadMore()
  }
}
</script>

<template>
  <div class="relative h-full w-full overflow-hidden bg-[#F2F6FC]">
    <!-- 背景 -->
    <div class="absolute inset-0 z-0 h-full">
      <div class="absolute inset-0 h-full w-full bg-[#F2F6FC]" />
      <div
        class="absolute inset-0 h-full w-full from-[#8AD1F9] via-[#F3F6FF] to-[#ECD9FF] bg-gradient-to-b opacity-80"
      />
    </div>

    <!-- 返回按钮 -->
    <BackButton />

    <NScrollbar class="h-full">
      <!-- 主要内容 -->
      <div class="relative z-5 w-full flex flex-col items-center pb-15 pt-30">
        <!-- 标题 -->
        <div class="mb-22 w-full flex flex-col items-center text-center">
          <img
            class="mb-4.5 w-50% text-[50px] text-black font-bold leading-[1.5] tracking-[0.1em]"
            src="@/assets/imgs/teaching-plan/img.png"
            alt=""
          >

          <p
            class="mx-auto max-w-[911px] text-base text-[#5F5E5E] leading-[1.4] tracking-[0.1em]"
          >
            选择编写教案的方式，AI智能生成教案内容，还可一键添加到教案模块、下载导出word教案文档、直接生成精美PPT!
          </p>
        </div>

        <!-- 创建方式选择 -->
        <div class="mb-22 flex flex-wrap justify-center gap-7.5">
          <div
            v-for="(method, index) in createMethods"
            :key="method.id"
            class="relative h-[199px] w-[237px] flex flex-col cursor-pointer items-center justify-center gap-5 border-2 border-white/80 rounded-4 shadow-lg backdrop-blur-sm transition-all duration-300 hover:border-white hover:shadow-2xl hover:-translate-y-1"
            :style="{ background: method.bgColor }"
            @click="handleCreateMethod(index + 1)"
          >
            <img class="size-86px" :src="method.icon" alt="">
            <div
              class="text-center text-lg font-medium leading-[1.4] tracking-[0.1em]"
              :style="{ color: method.color }"
            >
              {{ method.name }}
            </div>
          </div>
        </div>

        <!-- 最近生成记录 -->
        <div class="mb-10 max-w-[1564px] w-full overflow-hidden px-10">
          <div class="mb-6.5 flex items-center gap-3">
            <div class="h-6 w-6">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 2L2 7L12 12L22 7L12 2Z"
                  stroke="#44A9FB"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M2 17L12 22L22 17"
                  stroke="#44A9FB"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M2 12L12 17L22 12"
                  stroke="#44A9FB"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <h2
              class="m-0 text-[22px] text-[#474747] font-medium leading-[1.5]"
            >
              最近生成记录
            </h2>
          </div>

          <div class="relative w-full overflow-hidden">
            <NScrollbar
              x-scrollable
              class="w-full"
              @scroll="onScroll"
            >
              <div class="flex gap-x-16px">
                <div
                  v-for="record in records"
                  :key="record.Id"
                  class="min-h-[178px] min-w-80 w-80 flex flex-col flex-shrink-0 cursor-pointer border-4 border-white/80 rounded-4 bg-white/59 p-5 px-6.5 transition-all duration-300 hover:bg-white/80 hover:shadow-lg hover:-translate-y-0.5"
                  @click="handleRecordClick(record)"
                >
                  <h3
                    class="line-clamp-2 m-0 mb-3 whitespace-pre-wrap text-center text-lg text-[#474747] font-medium leading-[1.4]"
                  >
                    {{ record.Name }}
                  </h3>
                  <div class="mb-2.5 flex-1">
                    <p
                      class="line-clamp-3 m-0 text-base text-[#5C5C5C] leading-[1.5]"
                    >
                      {{ record.TeachingPlanText }}
                    </p>
                  </div>
                  <div class="flex items-center justify-start pl-1.5">
                    <div
                      class="flex items-center gap-2 text-sm text-[#969696] leading-[1.71]"
                    >
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <circle
                          cx="8"
                          cy="8"
                          r="7"
                          stroke="#969696"
                          stroke-width="2"
                        />
                        <path
                          d="M8 4V8L11 11"
                          stroke="#969696"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                      <span>{{ record.CreateTime }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </NScrollbar>
          </div>
        </div>
      </div>
    </NScrollbar>
  </div>
</template>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 隐藏记录区域的滚动条 */
.scrollbar-none::-webkit-scrollbar {
  display: none;
}

.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
</style>
