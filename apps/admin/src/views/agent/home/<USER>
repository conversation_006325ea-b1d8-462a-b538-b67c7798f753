<script setup lang="ts">
import { ref } from 'vue'
import { useRequest, useRouterPush } from '@sa/hooks'
import { nanoid } from '@sa/utils'
import AgentCard from './components/agent-card.vue'
import { fetchTeacherHomePageAgentListInfo, teacherHomePageAgentCollection } from '@/service/api/agent'

defineOptions({
  name: 'AgentHome',
})

// 搜索关键词
const { data, run: fetchAgents } = useRequest(
  () => {
    return fetchTeacherHomePageAgentListInfo({})
  },
  {
    manual: false,
  },
)

const parentTypes = computed(() => {
  return (data.value?.ParentTypes ?? []).map(v => ({ ...v, Id: v.Id ?? nanoid() }))
})

// 导航标签数据
const navTabs = computed(() => {
  if (!data.value) {
    return []
  }
  return (
    parentTypes.value.map(item => ({
      label: item.Title,
      key: item.Id,
    })) ?? []
  )
})

// 当前激活的标签
const activeTab = ref()

watch(navTabs, (newVal) => {
  if (!newVal || !newVal.length) {
    return
  }
  if (activeTab.value) {
    return
  }
  nextTick(() => {
    activeTab.value = newVal[1]?.key
  })
})

const currentData = computed(() => {
  if (!data.value) {
    return null
  }
  return parentTypes.value.find(v => v.Id === activeTab.value)
})

// 切换收藏状态
async function handleToggleStar(agent: AgentApi.AgentTeacherHomePageAgentInfo) {
  const { error } = await teacherHomePageAgentCollection({
    agentId: agent.Id,
  })
  if (error) {
    return
  }
  fetchAgents()
  window.$message?.success(!agent.IsCollection ? '已收藏' : '已取消收藏')
}

// 点赞
function handleLike(agent: any) {
  agent.likes += 1
  window.$message?.success('点赞成功')
}

// 查看详情
function handleViewDetail(agent: any) {
  goToAgent(agent)
}

// 查看统计
function handleViewStats() {
}

const { routerPushByKey } = useRouterPush()

type AgentInfo = AgentApi.AgentTeacherHomePageAgentInfo

const agentPushFnMap = {
  /**
   * 口语交际
   */
  Agent_OralCommunication: (agent: AgentInfo) => {
    routerPushByKey('agent_list', {
      query: {
        agentId: agent.Id!,
      },
    })
  },
  /**
   * AI教案
   */
  Agent_TeachingPlan: (agent: AgentInfo) => {
    routerPushByKey('teaching-plan_home', {
      query: {
        agentId: agent.Id!,
      },
    })
  },

  /**
   * AI出题
   */
  Agent_Question: (agent: AgentInfo) => {
    routerPushByKey('questions', {
      query: {
        agentId: agent.Id!,
      },
    })
  },
}

function goToAgent(agent: AgentInfo) {
  const pushFn = agentPushFnMap[agent.AgentBotCode as keyof typeof agentPushFnMap]
  if (!pushFn) {
    window.$message?.info('敬请期待')
    return
  }
  pushFn(agent)
}
</script>

<template>
  <div class="h-full w-full flex-col-stretch gap-16px overflow-y-auto">
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <!-- 导航标签 -->
      <NTabs v-model:value="activeTab" animated :bar-width="28" class="mb-24px">
        <NTab
          v-for="(item, index) in navTabs"
          :key="index"
          :name="item.key as string"
          :tab="item.label"
          class="!text-16px !font-medium"
        />
      </NTabs>

      <div
        v-for="(category, index) in currentData?.ChildrenTypes ?? []"
        :key="`${index}-${category.Id}`"
      >
        <!-- 页面头部 -->
        <div class="mb-12px flex items-center justify-between">
          <h3
            class="mb-4px text-14px text-gray-800 font-semibold dark:text-gray-200"
          >
            {{ category.Title }}
          </h3>
        </div>
        <!-- 智能体卡片网格 -->
        <NGrid responsive="screen" :x-gap="24" :y-gap="24" item-responsive>
          <NGridItem
            v-for="agent in category.AgentInfo"
            :key="agent.Id"
            span="24 s:12 m:8 l:6"
            :style="{ animationDelay: `${index * 100}ms` }"
          >
            <AgentCard
              :agent="agent"
              size="medium"
              @click="handleViewDetail"
              @toggle-star="handleToggleStar"
              @like="handleLike"
              @view-stats="handleViewStats"
              @use-now="goToAgent"
            />
          </NGridItem>
        </NGrid>

        <NEmpty
          v-if="category.AgentInfo?.length === 0"
          class="h-full flex items-center justify-center"
          description="暂无数据"
        />
      </div>

      <NEmpty
        v-if="(currentData?.ChildrenTypes ?? [])?.length === 0"
        class="h-full flex items-center justify-center"
        description="暂无数据"
      />
    </NCard>
  </div>
</template>
