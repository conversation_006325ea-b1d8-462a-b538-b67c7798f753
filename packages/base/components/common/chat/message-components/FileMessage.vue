<script setup lang="ts">
import { FilesCard } from 'vue-element-plus-x'

defineOptions({
  name: 'FileMessage',
})

const props = defineProps<{
  /** 消息项 */
  item: Chat.MessageItem
}>()
function handleClick() {
  window.$message?.success('下载文件')
}
</script>

<template>
  <div>
    <FilesCard
      v-for="(file, index) in props.item.files"
      :key="index"
      :name="file.fileName"
      :file-size="file.fileSize"
      class="mb-8px min-w-236px cursor-pointer"
      @click="handleClick"
    />
  </div>
</template>
