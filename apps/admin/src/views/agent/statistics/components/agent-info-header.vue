<script setup lang="ts">
import { useRouterPush } from '@sa/hooks'

defineOptions({
  name: 'AgentInfoHeader',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface AgentInfo {
  id: string
  name: string
  description: string
  avatar: string
  category: string
}

interface Props {
  /** 智能体信息 */
  agentInfo: AgentInfo
  /** 页面标题 */
  title?: string
}

interface Emits {
  /** 返回按钮点击事件 */
  (e: 'back'): void
}

const { routerBack } = useRouterPush()

// 处理返回
function handleBack() {
  emit('back')
  routerBack()
}
</script>

<template>
  <div class="border-b border-gray-200 dark:border-gray-700 dark:bg-gray-800">
    <div class="pb-12px">
      <!-- 返回按钮和标题 -->
      <div class="mb-12px flex items-center gap-12px">
        <ButtonIcon
          icon="ph:arrow-left"
          tooltip-content="返回"
          class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          @click="handleBack"
        />
        <span class="text-16px text-gray-800 font-medium dark:text-gray-200">
          {{ title || '查看统计' }}
        </span>
      </div>

      <!-- 智能体信息卡片 -->
      <div class="flex items-start gap-16px">
        <!-- 头像 -->
        <div class="flex-shrink-0">
          <img
            :src="props.agentInfo.avatar"
            :alt="props.agentInfo.name"
            class="s:h-48px s:w-48px h-56px w-56px rounded-12px object-cover"
          >
        </div>

        <!-- 信息内容 -->
        <div class="min-w-0 flex-1">
          <div class="mb-8px flex items-center gap-8px">
            <h2 class="s:text-16px text-18px text-gray-900 font-semibold dark:text-gray-100">
              {{ props.agentInfo.name }}
            </h2>
            <span class="rounded-4px bg-blue-100 px-8px py-2px text-12px text-blue-600 dark:bg-blue-900 dark:text-blue-300">
              {{ props.agentInfo.category }}
            </span>
          </div>
          <p class="s:text-13px line-clamp-3 text-14px text-gray-600 dark:text-gray-400">
            {{ props.agentInfo.description }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 文本截断样式 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
