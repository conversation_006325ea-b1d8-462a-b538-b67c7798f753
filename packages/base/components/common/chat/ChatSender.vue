<script setup lang="ts">
import { Attachments, Sender } from 'vue-element-plus-x'
import { useAudioRecorder } from '@sa/hooks'
import { HuaweiOBSUploader } from '@sa/utils'
import FilesSelect from './FilesSelect.vue'

defineOptions({
  name: 'ChatSender',
})
const props = withDefaults(defineProps<Props>(), {
  showActionList: false,
  showPrefix: true,
  isLoading: false,
  placeholder: '请输入消息或点击录音...',
})
const emit = defineEmits<Emits>()

const uploader = shallowRef<HuaweiOBSUploader>()
onMounted(() => {
  uploader.value = new HuaweiOBSUploader({
    bucket: 'aidialoguefileu',
  })
})
interface Props {
  showActionList?: boolean
  placeholder?: string
  showPrefix?: boolean
  isLoading?: boolean
}

// 定义发射的事件类型
interface Emits {
  startSse: [data: {
    inputContent: string
    uploadedFiles: any[]
    audioData?: any
  }]
  cancelSse: []
}

const senderRef = ref()
const senderValue = ref('')

// 获取 FilesSelect 组件的引用
const filesSelectRef = ref<InstanceType<typeof FilesSelect>>()

// 处理文件删除
function handleDeleteCard(_item: any, index: number) {
  filesSelectRef.value?.removeFile(index)
}

// 组件挂载后打开头部
onMounted(() => {
  // 延迟一下确保组件完全挂载
  nextTick(() => {
    // openSenderHeader()
  })
})

// 处理录音达到最大时长的回调
function handleMaxDurationReached() {
  // 重置 Sender 组件的录音状态
  senderRef.value.stopRecognition()
  // 通过 nextTick 确保状态更新后再显示消息
  nextTick(() => {
    window.$message?.warning('录音时间已达上限，已自动发送')
  })
}

// 使用音频录制 Hook
const {
  audioBlob,
  recordingData,
  error: recordingError,
  startRecording: startAudioRecording,
  stopRecording: stopAudioRecording,
  playAudio,
  formatFileSize,
  clearAudioData,
  isRecording,
  remainingTime,
  showCountdown,
} = useAudioRecorder({
  sampleRate: 44100,
  echoCancellation: true,
  noiseSuppression: true,
  dataInterval: 100,
  maxDuration: 20,
  countdownThreshold: 10,
  onMaxDurationReached: handleMaxDurationReached,
})

// 播放状态
const isPlaying = ref(false)

// 监听 Sender 组件的录音状态变化
function onRecordingChange(recording: boolean) {
  if (recording) {
    window.$message?.success('开始录音')
    startRecording()
  }
  else {
    window.$message?.success('结束录音')
    stopRecording()
  }
}

// 开始录音的方法
async function startRecording() {
  try {
    await startAudioRecording()
  }
  catch (error) {
    console.error('开始录音失败:', error)
    window.$message?.error(recordingError.value || '录音启动失败，请检查麦克风权限')
  }
}

// 停止录音的方法
async function stopRecording() {
  try {
    await stopAudioRecording()

    // 等待一小段时间确保录音数据处理完成
    await new Promise(resolve => setTimeout(resolve, 100))

    // 获取音频数据
    if (audioBlob.value && recordingData.value) {
      const audioData = {
        audioBlob: audioBlob.value,
        recordingData: recordingData.value,
        audioSize: audioBlob.value.size,
        audioSizeFormatted: formatFileSize(audioBlob.value.size),
        duration: recordingData.value.duration,
        mimeType: recordingData.value.mimeType,
      }

      // 停止录音后立即发送录音数据给SSE
      sendAudioToSSE(audioData)

      return audioData
    }
    else {
      console.warn('未获取到音频数据')
      return null
    }
  }
  catch (error) {
    console.error('停止录音失败:', error)
    window.$message?.error(recordingError.value || '录音停止失败')
    return null
  }
}

// 单独发送录音数据给SSE
async function sendAudioToSSE(audioData: any) {
  try {
    // 构建要发送的数据
    const data = {
      inputContent: '',
      uploadedFiles: [],
      audioData,
    }

    uploadAudioToOBS(audioData, (_progress) => {
    }).then((audioDataWithOBS) => {
      if (audioDataWithOBS) {
        data.audioData = audioDataWithOBS
        emit('startSse', data)
      }
    })
    // 清空音频数据
    clearAudioData()
  }
  catch (error) {
    console.error('发送录音数据失败:', error)
    window.$message?.error('录音发送失败')
  }
}

// 上传语音的方法
async function uploadAudioToOBS(audioData: any, onProgress?: (progress: number) => void) {
  try {
    // 检查 uploader 是否已初始化
    if (!uploader.value) {
      throw new Error('文件上传器未初始化')
    }

    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    const dynamicKey = `audio/${timestamp}_${randomStr}.wav`

    // 上传音频文件
    const obsResult = await uploader.value.upload({
      file: audioData.audioBlob,
      key: dynamicKey,
      onProgress: (progress: number) => {
        // 调用传入的进度回调
        if (onProgress) {
          onProgress(progress)
        }
        // 可以在这里显示上传进度提示
        if (progress < 100) {
          // window.$message?.loading(`音频上传中... ${progress}%`, { duration: 0 })
        }
        else {
          // window.$message?.destroyAll()
          // window.$message?.success('音频上传完成')
        }
      },
    })

    // 构建音频数据
    const audioDataWithOBS = {
      ...audioData,
      audioUrl: obsResult.url,
      obsKey: obsResult.key,
      uploadTime: obsResult.uploadTime.toISOString(),
    }

    return audioDataWithOBS
  }
  catch (error) {
    console.error('音频上传失败:', error)
    window.$message?.error('音频上传失败，请重试')
    return null
  }
}

// 播放录音的方法
async function playRecording() {
  if (!audioBlob.value) {
    window.$message?.warning('没有可播放的录音')
    return
  }

  try {
    isPlaying.value = true
    window.$message?.success('开始播放录音')

    await playAudio(audioBlob.value)

    isPlaying.value = false
    window.$message?.success('录音播放完成')
  }
  catch (error) {
    console.error('播放录音失败:', error)
    window.$message?.error('录音播放失败')
    isPlaying.value = false
  }
}

function startSSE() {
  // 获取当前输入内容
  const inputContent = senderValue.value

  // 获取上传成功的文件列表（只获取状态为 'done' 的文件）
  const uploadedFiles = filesSelectRef.value?.filesList?.filter((file: any) => file.status === 'done') || []

  // 获取音频数据（如果有的话）
  const audioData = audioBlob.value
    ? {
        audioBlob: audioBlob.value,
        recordingData: recordingData.value,
        audioSize: audioBlob.value.size,
        audioSizeFormatted: formatFileSize(audioBlob.value.size),
        duration: recordingData.value?.duration,
        mimeType: recordingData.value?.mimeType,
      }
    : undefined

  // 构建要暴露的数据
  const data = {
    inputContent,
    uploadedFiles,
    audioData,
  }
  console.log(data)

  // 通过事件暴露数据
  emit('startSse', data)

  // 发送SSE后清空用户输入的内容
  clearUserInput()
}

// 清空用户输入的所有内容
function clearUserInput() {
  // 清空文本输入
  senderValue.value = ''

  // 清空上传的文件
  filesSelectRef.value?.clearFiles()

  // 清空音频数据
  clearAudioData()
}

function cancelSSE() {
  // 发射取消事件
  emit('cancelSse')
}

watch(
  () => filesSelectRef.value?.getFilesCount() || 0,
  (val) => {
    if (val > 0) {
      nextTick(() => {
        senderRef.value?.openHeader()
      })
    }
    else {
      nextTick(() => {
        senderRef.value?.closeHeader()
      })
    }
  },
)

// 暴露方法给父组件
defineExpose({
  senderRef,
  senderValue: computed(() => senderValue.value),
  clearUserInput,
  filesSelectRef,
})
</script>

<template>
  <!-- 发送消息组件 -->
  <Sender
    ref="senderRef"
    v-model="senderValue"
    variant="updown"
    :auto-size="{ minRows: 2, maxRows: 6 }"
    :loading="props.isLoading"
    clearable
    allow-speech
    :placeholder="props.placeholder"
    @recording-change="onRecordingChange"
    @submit="startSSE"
    @cancel="cancelSSE"
  >
    <template #header>
      <div class="sender-header p-12px pb-0px pt-6px">
        <Attachments :items="filesSelectRef?.filesList || []" :hide-upload="true" @delete-card="handleDeleteCard">
          <template #prev-button="{ show, onScrollLeft }">
            <div
              v-if="show"
              class="prev-next-btn left-8px"
              @click="onScrollLeft"
            >
              <SvgIcon icon="mdi:chevron-left" />
            </div>
          </template>

          <template #next-button="{ show, onScrollRight }">
            <div
              v-if="show"
              class="prev-next-btn right-8px"
              @click="onScrollRight"
            >
              <SvgIcon icon="mdi:chevron-right" />
            </div>
          </template>
        </Attachments>
      </div>
    </template>

    <!-- 是否展示右侧自定义功能 -->
    <template v-if="props.showPrefix" #prefix>
      <div class="w-fit flex flex-1 flex-none items-center gap-8px overflow-hidden">
        <FilesSelect ref="filesSelectRef" />
        <!-- <ModelSelect /> -->

        <!-- 录音倒计时显示 -->
        <div
          v-if="showCountdown && isRecording"
          class="countdown-display"
          :class="{ warning: remainingTime <= 5 }"
        >
          {{ remainingTime }}s
        </div>

        <!-- 播放录音按钮 -->
        <div
          v-if="audioBlob"
          class="play-button"
          :class="{ 'is-playing': isPlaying }"
          :title="isPlaying ? '正在播放录音' : '播放录音'"
          @click="playRecording"
        >
          <SvgIcon :icon="isPlaying ? 'mdi:pause' : 'mdi:play'" />
        </div>
      </div>
    </template>
  </Sender>
</template>

<style scoped lang="scss">
.icon-item {
  display: flex;
  cursor: pointer;
  align-items: center;
  gap: 4px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  padding: 10px;
  font-size: 14px;
  &:hover{
    background-color: rgba(0, 0, 0, 0.04);
  }
}
.prev-next-btn {
  position: absolute;
  top: calc(50% + 3px);
  z-index: 10;
  cursor: pointer;
  transform: translateY(-50%);
  @apply h-22px w-22px flex-center border-1px border-[rgba(0,0,0,0.08)] rounded-8px border-solid bg-#fff font-size-10px c-[rgba(0,0,0,.4)] hover:bg-#f3f4f6
}

.play-button {
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  padding: 8px;
  font-size: 14px;
  transition: all 0.2s;
  width: 32px;
  height: 32px;
  color: #626aef;
}

.play-button.is-playing {
  background-color: #626aef;
  color: white;
}

.countdown-display {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: #f0f0f0;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  min-width: 32px;
  transition: all 0.2s;
}

.countdown-display.warning {
  background-color: #ff4d4f;
  color: white;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
