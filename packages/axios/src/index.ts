import axios, { AxiosError } from 'axios'
import type { AxiosResponse, CreateAxiosDefaults, InternalAxiosRequestConfig } from 'axios'
import axiosRetry from 'axios-retry'
import { nanoid } from '@sa/utils'
import { createAxiosConfig, createDefaultOptions, createRetryOptions } from './options'
import { BACKEND_ERROR_CODE, REQUEST_ID_KEY } from './constant'
import type {
  CustomAxiosRequestConfig,
  FlatRequestInstance,
  MappedType,
  RequestInstance,
  RequestOption,
  ResponseType,
} from './type'
/**
 * 创建通用的axios请求实例
 * @template ResponseData 响应数据类型，默认为any
 * @param axiosConfig axios默认配置
 * @param options 请求选项配置
 * @returns 返回包含实例和取消方法的对象
 */
function createCommonRequest<ResponseData = any>(
  axiosConfig?: CreateAxiosDefaults,
  options?: Partial<RequestOption<ResponseData>>,
) {
  // 初始化请求选项
  const opts = createDefaultOptions<ResponseData>(options)

  // 创建axios配置和实例
  const axiosConf = createAxiosConfig(axiosConfig)
  const instance = axios.create(axiosConf)

  // 存储AbortController的Map，用于取消请求
  const abortControllerMap = new Map<string, AbortController>()

  // 配置axios重试逻辑
  const retryOptions = createRetryOptions(axiosConf)
  axiosRetry(instance as any, retryOptions)

  // 请求拦截器
  instance.interceptors.request.use((conf) => {
    const config: InternalAxiosRequestConfig = { ...conf }

    // 设置请求ID
    const requestId = nanoid()
    config.headers.set(REQUEST_ID_KEY, requestId)

    // 配置AbortController
    if (!config.signal) {
      const abortController = new AbortController()
      config.signal = abortController.signal
      abortControllerMap.set(requestId, abortController)
    }

    // 通过hook处理配置
    const handledConfig = opts.onRequest?.(config) || config

    return handledConfig
  })
  // 响应拦截器
  instance.interceptors.response.use(
    async (response) => {
      const responseType: ResponseType = (response.config?.responseType as ResponseType) || 'json'

      // 转换响应数据为标准格式
      if (responseType === 'json' && response.data) {
        // 处理异常错误
        const code = response.data.Success === false
          ? (
              response.data.ErrorCode === 0 ? BACKEND_ERROR_CODE : response.data.ErrorCode
            )
          : response.data.ErrorCode

        response.data = {
          code,
          msg: response.data.Msg,
          data: response.data.Data,
        }
      }

      // 非json响应或后端认为成功的响应直接返回
      if (responseType !== 'json' || opts.isBackendSuccess(response)) {
        return Promise.resolve(response)
      }

      // 处理后端失败逻辑
      const fail = await opts.onBackendFail(response, instance)
      if (fail) {
        return fail
      }
      // 构造后端错误对象
      const backendError = new AxiosError<ResponseData>(
        'the backend request error',
        BACKEND_ERROR_CODE,
        response.config,
        response.request,
        response,
      )

      // 触发错误处理
      await opts.onError(backendError)

      return Promise.reject(backendError)
    },
    async (error: AxiosError<ResponseData>) => {
      // 处理请求错误
      await opts.onError(error)

      return Promise.reject(error)
    },
  )
  /**
   * 取消指定请求
   * @param requestId 请求ID
   */
  function cancelRequest(requestId: string) {
    const abortController = abortControllerMap.get(requestId)
    if (abortController) {
      abortController.abort()
      abortControllerMap.delete(requestId)
    }
  }
  /**
   * 取消所有pending中的请求
   */
  function cancelAllRequest() {
    abortControllerMap.forEach((abortController) => {
      abortController.abort()
    })
    abortControllerMap.clear()
  }

  return {
    instance,
    opts,
    cancelRequest,
    cancelAllRequest,
  }
}

/**
 * 创建请求实例
 * @template ResponseData 响应数据类型，默认为any
 * @template State 请求实例的附加状态类型，默认为Record<string, unknown>
 * @param axiosConfig axios默认配置
 * @param options 请求选项配置
 * @returns 返回配置好的请求实例
 */
export function createRequest<ResponseData = any, State = Record<string, unknown>>(
  axiosConfig?: CreateAxiosDefaults,
  options?: Partial<RequestOption<ResponseData>>,
) {
  // 从通用请求创建器中获取实例和配置
  const { instance, opts, cancelRequest, cancelAllRequest } = createCommonRequest<ResponseData>(axiosConfig, options)
  /**
   * 请求实例核心方法
   * @template T 响应数据类型
   * @template R 响应类型，默认为'json'
   * @param config 请求配置
   * @returns 返回处理后的响应数据
   */
  const request: RequestInstance<State> = async function request<T = any, R extends ResponseType = 'json'>(
    config: CustomAxiosRequestConfig,
  ) {
    const response: AxiosResponse<ResponseData> = await instance(config)

    const responseType = response.config?.responseType || 'json'

    // 处理json响应
    if (responseType === 'json') {
      return opts.transformBackendResponse(response)
    }

    // 非json响应直接返回数据
    return response.data as MappedType<R, T>
  } as RequestInstance<State>

  // 挂载取消请求方法
  request.cancelRequest = cancelRequest
  request.cancelAllRequest = cancelAllRequest

  // 初始化请求实例状态
  request.state = {} as State

  return request
}

/**
 * 创建扁平化请求实例
 * @template ResponseData 响应数据类型，默认为any
 * @template State 请求实例的附加状态类型，默认为Record<string, unknown>
 * @param axiosConfig axios默认配置
 * @param options 请求选项配置
 * @returns 返回扁平化请求实例，响应格式为{data, error, response}
 */
export function createFlatRequest<ResponseData = any, State = Record<string, unknown>>(
  axiosConfig?: CreateAxiosDefaults,
  options?: Partial<RequestOption<ResponseData>>,
) {
  // 从通用请求创建器中获取实例和配置
  const { instance, opts, cancelRequest, cancelAllRequest } = createCommonRequest<ResponseData>(axiosConfig, options)

  /**
   * 扁平化请求核心方法
   * @template T 响应数据类型
   * @template R 响应类型，默认为'json'
   * @param config 请求配置
   * @returns 返回扁平化响应对象{data, error, response}
   */
  const flatRequest: FlatRequestInstance<State, ResponseData> = async function flatRequest<
    T = any,
    R extends ResponseType = 'json',
  >(config: CustomAxiosRequestConfig) {
    try {
      const response: AxiosResponse<ResponseData> = await instance(config)

      const responseType = response.config?.responseType || 'json'

      // 处理json响应
      if (responseType === 'json') {
        const data = opts.transformBackendResponse(response)

        return { data, error: null, response }
      }

      // 处理非json响应
      return { data: response.data as MappedType<R, T>, error: null }
    }
    catch (error) {
      // 捕获错误并返回错误信息
      return { data: null, error, response: (error as AxiosError<ResponseData>).response }
    }
  } as FlatRequestInstance<State, ResponseData>

  // 挂载取消请求方法
  flatRequest.cancelRequest = cancelRequest
  flatRequest.cancelAllRequest = cancelAllRequest
  // 初始化请求实例状态
  flatRequest.state = {} as State

  return flatRequest
}
/**
 * 导出常量
 * - BACKEND_ERROR_CODE: 后端错误码标识
 * - REQUEST_ID_KEY: 请求ID标识头
 */
export { BACKEND_ERROR_CODE, REQUEST_ID_KEY }
/**
 * 导出类型定义
 * - 从'./type'导出所有类型
 * - 导出Axios的CreateAxiosDefaults和AxiosError类型
 */
export type * from './type'
export type { CreateAxiosDefaults, AxiosError }
