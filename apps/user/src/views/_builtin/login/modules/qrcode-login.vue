<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouterPush } from '@sa/hooks'
import { useAuthStore } from '@/store/modules/auth'
// import { fetchQrcodeLogin, fetchQrcodeLoginStatus } from '@/service/api/auth'

defineOptions({
  name: 'QrcodeLogin',
})

const authStore = useAuthStore()
const { toggleLoginModule, redirectFromLogin } = useRouterPush()

// 二维码状态
type QrcodeStatus = 'loading' | 'waiting' | 'scanned' | 'confirmed' | 'expired' | 'error'

// 响应式数据
const qrcodeStatus = ref<QrcodeStatus>('loading')
const qrcodeUrl = ref('')
const qrcodeKey = ref('')
const errorMessage = ref('')
const pollingTimer = ref<NodeJS.Timeout | null>(null)
const expireTimer = ref<NodeJS.Timeout | null>(null)

// 状态配置
const statusConfig = computed(() => {
  const configs = {
    loading: {
      icon: 'eos-icons:loading',
      title: '正在生成二维码...',
      description: '请稍候',
      showRefresh: false,
      iconColor: 'text-blue-500',
    },
    waiting: {
      icon: 'material-symbols:qr-code-2',
      title: '请使用手机扫码',
      description: '打开手机应用扫描二维码登录',
      showRefresh: true,
      iconColor: 'text-gray-400',
    },
    scanned: {
      icon: 'material-symbols:check-circle',
      title: '扫码成功',
      description: '请在手机端确认登录',
      showRefresh: false,
      iconColor: 'text-green-500',
    },
    confirmed: {
      icon: 'material-symbols:check-circle',
      title: '登录成功',
      description: '正在跳转...',
      showRefresh: false,
      iconColor: 'text-green-500',
    },
    expired: {
      icon: 'material-symbols:error',
      title: '二维码已过期',
      description: '请点击刷新重新获取',
      showRefresh: true,
      iconColor: 'text-orange-500',
    },
    error: {
      icon: 'material-symbols:error',
      title: '登录失败',
      description: errorMessage.value || '请重新获取二维码',
      showRefresh: true,
      iconColor: 'text-red-500',
    },
  }
  return configs[qrcodeStatus.value]
})

// 生成二维码
async function generateQrcode() {
  try {
    qrcodeStatus.value = 'loading'
    clearTimers()

    // 模拟API调用 - 在实际项目中替换为真实API
    const mockQrcodeData = {
      qrcodeKey: `qr_${Date.now()}`,
      qrcodeUrl: `https://example.com/qrcode/login?key=qr_${Date.now()}`,
      expireTime: 300, // 5分钟过期
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 在生产环境中使用真实API
    // const { data, error } = await fetchQrcodeLogin()
    // if (error) {
    //   qrcodeStatus.value = 'error'
    //   errorMessage.value = '获取二维码失败'
    //   return
    // }

    const data = mockQrcodeData

    if (data) {
      qrcodeKey.value = data.qrcodeKey
      qrcodeUrl.value = data.qrcodeUrl
      qrcodeStatus.value = 'waiting'

      // 开始轮询状态
      startPolling()

      // 设置过期定时器
      const expireTime = data.expireTime * 1000 // 转换为毫秒
      expireTimer.value = setTimeout(() => {
        if (qrcodeStatus.value === 'waiting' || qrcodeStatus.value === 'scanned') {
          qrcodeStatus.value = 'expired'
          clearTimers()
        }
      }, expireTime)
    }
  }
  catch (err) {
    qrcodeStatus.value = 'error'
    errorMessage.value = '生成二维码失败'
    console.error('Generate QR code error:', err)
  }
}

// 开始轮询状态
function startPolling() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }

  let pollCount = 0

  pollingTimer.value = setInterval(async () => {
    try {
      pollCount++

      // 模拟二维码状态变化 - 在实际项目中替换为真实API
      let mockStatus: any

      if (pollCount <= 3000) {
        // 前6秒保持等待状态
        mockStatus = { status: 'waiting' }
      }
      else if (pollCount <= 5) {
        // 接下来4秒显示已扫描状态
        mockStatus = { status: 'scanned' }
      }
      else if (pollCount === 600) {
        // 第12秒模拟登录成功
        mockStatus = {
          status: 'confirmed',
          token: `mock_token_${Date.now()}`,
          refreshToken: `mock_refresh_token_${Date.now()}`,
        }
      }
      else {
        // 其他情况保持等待状态
        mockStatus = { status: 'waiting' }
      }

      // 在生产环境中使用真实API
      // const { data, error } = await fetchQrcodeLoginStatus(qrcodeKey.value)
      // if (error) {
      //   console.error('Poll status error:', error)
      //   return
      // }

      const data = mockStatus

      if (data) {
        switch (data.status) {
          case 'scanned':
            qrcodeStatus.value = 'scanned'
            break
          case 'confirmed':
            qrcodeStatus.value = 'confirmed'
            clearTimers()
            // 登录成功，处理token
            if (data.token && data.refreshToken) {
              const loginToken = {
                token: data.token,
                refreshToken: data.refreshToken,
              }
              const success = await authStore.loginByToken(loginToken)
              if (success) {
                window.$notification?.success({
                  title: '登录成功',
                  content: `欢迎回来，${authStore.userInfo.userName}!`,
                  duration: 4500,
                })
                // 跳转到登录后页面
                await redirectFromLogin()
              }
            }
            break
          case 'expired':
            qrcodeStatus.value = 'expired'
            clearTimers()
            break
          case 'error':
            qrcodeStatus.value = 'error'
            errorMessage.value = data.message || '登录失败'
            clearTimers()
            break
        }
      }
    }
    catch (err) {
      console.error('Poll status error:', err)
    }
  }, 2000) // 每2秒轮询一次
}

// 清除定时器
function clearTimers() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
  if (expireTimer.value) {
    clearTimeout(expireTimer.value)
    expireTimer.value = null
  }
}

// 刷新二维码
function refreshQrcode() {
  generateQrcode()
}

// 组件挂载时生成二维码
onMounted(() => {
  generateQrcode()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  clearTimers()
})
</script>

<template>
  <div class="w-full">
    <!-- 二维码显示区域 -->
    <div class="mb-24px flex flex-col items-center justify-center">
      <!-- 二维码或状态图标 -->
      <div class="group relative">
        <div
          v-if="qrcodeStatus === 'waiting' && qrcodeUrl"
          class="h-200px w-200px overflow-hidden border border-gray-200 rounded-8px bg-white p-8px"
        >
          <NQrCode
            :value="qrcodeUrl"
            :size="184"
            color="#000000"
            background-color="#ffffff"
            :padding="0"
            class="h-full w-full"
          />
        </div>
        <div
          v-else
          class="h-200px w-200px flex items-center justify-center border border-gray-200 rounded-8px bg-gray-50"
        >
          <SvgIcon
            :icon="statusConfig.icon"
            class="text-64px"
            :class="statusConfig.iconColor"
          />
        </div>

        <!-- 悬浮刷新按钮 - 仅在二维码显示时出现 -->
        <div
          v-if="qrcodeStatus === 'waiting' && statusConfig.showRefresh"
          class="absolute right-8px top-8px opacity-0 transition-opacity duration-200 group-hover:opacity-100"
        >
          <NButton
            size="small"

            quaternary
            circle
            class="bg-white shadow-md hover:shadow-lg"
            data-testid="hover-refresh-button"
            @click="refreshQrcode"
          >
            <template #icon>
              <SvgIcon icon="material-symbols:refresh" class="text-16px" />
            </template>
          </NButton>
        </div>

        <!-- 加载动画 -->
        <div
          v-if="qrcodeStatus === 'loading'"
          class="absolute inset-0 flex items-center justify-center rounded-8px bg-white bg-opacity-80"
        >
          <NSpin size="large" />
        </div>
      </div>

      <!-- 状态文字 -->
      <div class="text-center">
        <div class="mb-4px text-16px font-medium" data-testid="status-title">
          {{ statusConfig.title }}
        </div>
        <div class="text-14px text-gray-500" data-testid="status-description">
          {{ statusConfig.description }}
          <!-- 过期或错误状态时显示刷新链接 -->
          <span
            v-if="(qrcodeStatus === 'expired' || qrcodeStatus === 'error') && statusConfig.showRefresh"
            class="ml-8px"
          >
            <NButton
              text
              type="primary"
              size="small"
              data-testid="text-refresh-button"
              @click="refreshQrcode"
            >
              <template #icon>
                <SvgIcon icon="material-symbols:refresh" class="text-14px" />
              </template>
              点击刷新
            </NButton>
          </span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="w-full flex justify-center">
      <!-- 返回按钮 -->
      <NButton
        text
        size="medium"
        class="text-gray-500 transition-colors duration-200 hover:text-primary"
        data-testid="back-button"
        @click="toggleLoginModule('pwd-login')"
      >
        <template #icon>
          <SvgIcon icon="mdi:arrow-left" class="text-16px" />
        </template>
        返回密码登录
      </NButton>
    </div>
  </div>
</template>

<style scoped>
/* 可以添加一些动画效果 */
.w-200px {
  width: 200px;
}

.h-200px {
  height: 200px;
}
</style>
