/**
 * 命名空间 Api
 *
 * 所有后端 API 类型
 */
declare namespace Api {
  namespace Common {
    /** 分页通用参数 */
    interface PaginatingCommonParams {
      /** 当前页码 */
      current: number
      /** 每页大小 */
      size: number
      /** 总计数 */
      total: number
    }

    /** 分页查询列表数据的通用参数 */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[]
    }

    /** 表格通用搜索参数 */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>

    /**
     * 启用状态
     *
     * - "1": 启用
     * - "2": 禁用
     */
    type EnableStatus = '1' | '2'

    /** 通用记录 */
    type CommonRecord<T = any> = {
      /** 记录 ID */
      id: number
      /** 记录创建者 */
      createBy: string
      /** 记录创建时间 */
      createTime: string
      /** 记录更新者 */
      updateBy: string
      /** 记录更新时间 */
      updateTime: string
      /** 记录状态 */
      status: EnableStatus | null
    } & T
  }

  /**
   * 命名空间 Auth
   *
   * 后端 API 模块: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string
      refreshToken?: string
    }

    interface RoleIdList {
      RoleId: string
      RoleName: string
      SchoolInfo: SchoolInfo[]
    }

    interface SchoolInfo {
      SchoolName: string
      SchoolId: string
      Province: string
      City: string
      District: string
      SubjetClassList: SubjetClassList[]
    }

    interface SubjetClassList {
      SubjectId: string
      SubjectName: string
      ClassIds: ClassId[]
    }

    interface ClassId {
      Classid: string
      ClassName: string
    }

    interface UserInfo {
      RoleIdList: RoleIdList[]
      userName: string
      roles: string[]
      buttons: string[]
      userId?: string
    }
  }

  /**
   * 命名空间 Route
   *
   * 后端 API 模块: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute

    interface MenuRoute extends ElegantConstRoute {
      id: string
    }

    interface UserRoute {
      routes: MenuRoute[]
      home: import('@elegant-router/types').LastLevelRouteKey
    }
  }
  /**
   * 命名空间 SystemManage
   *
   * 后端 API 模块: "systemManage"
   */
  namespace SystemManage {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>

    /** 角色 */
    type Role = Common.CommonRecord<{
      /** 角色名称 */
      roleName: string
      /** 角色代码 */
      roleCode: string
      /** 角色描述 */
      roleDesc: string
    }>

    /** 角色搜索参数 */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'roleName' | 'roleCode' | 'status'> & CommonSearchParams
    >

    /** 角色列表 */
    type RoleList = Common.PaginatingQueryRecord<Role>

    /** 所有角色 */
    type AllRole = Pick<Role, 'id' | 'roleName' | 'roleCode'>

    /**
     * 用户性别
     *
     * - "1": 男
     * - "2": 女
     */
    type UserGender = '1' | '2'

    /** 用户 */
    type User = Common.CommonRecord<{
      /** 用户名 */
      userName: string
      /** 用户性别 */
      userGender: UserGender | null
      /** 用户昵称 */
      nickName: string
      /** 用户电话 */
      userPhone: string
      /** 用户邮箱 */
      userEmail: string
      /** 用户角色代码集合 */
      userRoles: string[]
    }>

    /** 用户搜索参数 */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.User, 'userName' | 'userGender' | 'nickName' | 'userPhone' | 'userEmail' | 'status'> &
      CommonSearchParams
    >

    /** 用户列表 */
    type UserList = Common.PaginatingQueryRecord<User>

    /**
     * 菜单类型
     *
     * - "1": 目录
     * - "2": 菜单
     */
    type MenuType = '1' | '2'

    interface MenuButton {
      /**
       * 按钮代码
       *
       * 可以用于控制按钮权限
       */
      code: string
      /** 按钮描述 */
      desc: string
    }

    /**
     * 图标类型
     *
     * - "1": Iconify 图标
     * - "2": 本地图标
     */
    type IconType = '1' | '2'

    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
        | 'keepAlive'
        | 'constant'
        | 'order'
        | 'href'
        | 'hideInMenu'
        | 'activeMenu'
        | 'multiTab'
        | 'fixedIndexInTab'
        | 'query'
    >

    type Menu = Common.CommonRecord<{
      /** 父菜单 ID */
      parentId: number
      /** 菜单类型 */
      menuType: MenuType
      /** 菜单名称 */
      menuName: string
      /** 路由名称 */
      routeName: string
      /** 路由路径 */
      routePath: string
      /** 组件 */
      component?: string
      /** Iconify 图标名称或本地图标名称 */
      icon: string
      /** 图标类型 */
      iconType: IconType
      /** 按钮 */
      buttons?: MenuButton[] | null
      /** 子菜单 */
      children?: Menu[] | null
    }> &
    MenuPropsOfRoute

    /** 菜单列表 */
    type MenuList = Common.PaginatingQueryRecord<Menu>

    // eslint-disable-next-line ts/consistent-type-definitions
    type MenuTree = {
      id: number
      label: string
      pId: number
      children?: MenuTree[]
    }
  }

  namespace Agent {

    interface AgentTeacherHomePageListInput {
      /** 教师Id */
      teacherId?: string
      /** 学校类型 */
      schoolType?: string
      /** 学科Id */
      subjectId?: string
      /** 教材Id */
      textbookId?: string
    }

    interface AgentTeacherHomePageListOutPut {
      /** 智能体_教师首页父级类型 */
      parentTypes?: AgentTeacherHomePageParentType[]
    }

    interface AgentTeacherHomePageParentType {
      /** 类型Id */
      id?: string
      /** 类型名称 */
      title?: string
      /** 智能体_教师首页子级类型 */
      childrenTypes?: AgentTeacherHomePageChildrenType[]
    }

    interface AgentTeacherHomePageChildrenType {
      /** 类型Id */
      id?: string
      /** 类型名称 */
      title?: string
      /** 智能体信息 */
      agentInfo?: AgentTeacherHomePageAgentInfo[]
    }

    interface AgentTeacherHomePageAgentInfo {
      /** 智能体Id */
      id?: string
      /** 智能体名称 */
      agentName?: string
      /** 智能体应用编码 */
      agentBotCode?: string
      /** 智能体类型 */
      agentTypeId?: string
      /** 学校类型（0代表通用） */
      schoolType?: string
      /** 学校类型名称 */
      schoolTypeName?: string
      /** 学科Id（0代表通用） */
      subjectId?: string
      /** 学科名称 */
      subjectName?: string
      /** 教材版本Id（0代表通用） */
      textbookId?: string
      /** 教材版本名称 */
      textbookName?: string
      /** 智能体概述 */
      summarize?: string
      /** 图标Logo */
      logo?: string
      /** 创建时间 */
      createTime?: string
      /** 是否收藏 */
      isCollection?: boolean
    }

    interface AgentTeacherOralCommunicationListInput {
      /** 页码（默认1） */
      pageIndex?: number
      /** 每页数量（默认10） */
      pageSize?: number
      /** 教师Id */
      teacherId?: string
      /** 学校Id */
      schoolId?: string
      /** 智能体Id */
      agentId?: string
      /** 任务名称 */
      name?: string
      /** 班级Id */
      classId?: string
      /** 互动模式(0全部、1指令式、2对话式、3辩论式) */
      interactiveMode?: number
      /** 学科Id */
      subjectId?: string
    }

    interface AgentTeacherOralCommunicationListOutput {
      /** 智能体任务Id */
      id?: string
      /** 名称 */
      name?: string
      /** 项目背景介绍 */
      introduce?: string
      /** 互动模式(1指令式、2对话式、3辩论式) */
      interactiveMode?: number
      /** 章节Id */
      chapterId?: string
      /** 章节名称 */
      chapterName?: string
      /** 开始时间 */
      beginTime?: string
      /** 结束时间 */
      endTime?: string
      /** 创建时间 */
      createTime?: string
      /** 发布的班级 */
      className?: string
      /** Logo */
      logo?: string
    }

    interface AgentTeacherOralCommunicationListOutputPageReturn {
      /** 数据总量 */
      totalCount?: number
      /** 数据集 */
      datas?: AgentTeacherOralCommunicationListOutput[]
    }

  }
}
