import type { ColorPaletteFamily } from '../types'

export const colorPalettes: ColorPaletteFamily[] = [
  {
    name: 'Slate',
    palettes: [
      { hex: '#f8fafc', number: 50 },
      { hex: '#f1f5f9', number: 100 },
      { hex: '#e2e8f0', number: 200 },
      { hex: '#cbd5e1', number: 300 },
      { hex: '#94a3b8', number: 400 },
      { hex: '#64748b', number: 500 },
      { hex: '#475569', number: 600 },
      { hex: '#334155', number: 700 },
      { hex: '#1e293b', number: 800 },
      { hex: '#0f172a', number: 900 },
      { hex: '#020617', number: 950 },
    ],
  },
  {
    name: 'Gray',
    palettes: [
      { hex: '#f9fafb', number: 50 },
      { hex: '#f3f4f6', number: 100 },
      { hex: '#e5e7eb', number: 200 },
      { hex: '#d1d5db', number: 300 },
      { hex: '#9ca3af', number: 400 },
      { hex: '#6b7280', number: 500 },
      { hex: '#4b5563', number: 600 },
      { hex: '#374151', number: 700 },
      { hex: '#1f2937', number: 800 },
      { hex: '#111827', number: 900 },
      { hex: '#030712', number: 950 },
    ],
  },
  {
    name: 'Zinc',
    palettes: [
      { hex: '#fafafa', number: 50 },
      { hex: '#f4f4f5', number: 100 },
      { hex: '#e4e4e7', number: 200 },
      { hex: '#d4d4d8', number: 300 },
      { hex: '#a1a1aa', number: 400 },
      { hex: '#71717a', number: 500 },
      { hex: '#52525b', number: 600 },
      { hex: '#3f3f46', number: 700 },
      { hex: '#27272a', number: 800 },
      { hex: '#18181b', number: 900 },
      { hex: '#09090b', number: 950 },
    ],
  },
  {
    name: 'Neutral',
    palettes: [
      { hex: '#fafafa', number: 50 },
      { hex: '#f5f5f5', number: 100 },
      { hex: '#e5e5e5', number: 200 },
      { hex: '#d4d4d4', number: 300 },
      { hex: '#a3a3a3', number: 400 },
      { hex: '#737373', number: 500 },
      { hex: '#525252', number: 600 },
      { hex: '#404040', number: 700 },
      { hex: '#262626', number: 800 },
      { hex: '#171717', number: 900 },
      { hex: '#0a0a0a', number: 950 },
    ],
  },
  {
    name: 'Stone',
    palettes: [
      { hex: '#fafaf9', number: 50 },
      { hex: '#f5f5f4', number: 100 },
      { hex: '#e7e5e4', number: 200 },
      { hex: '#d6d3d1', number: 300 },
      { hex: '#a8a29e', number: 400 },
      { hex: '#78716c', number: 500 },
      { hex: '#57534e', number: 600 },
      { hex: '#44403c', number: 700 },
      { hex: '#292524', number: 800 },
      { hex: '#1c1917', number: 900 },
      { hex: '#0c0a09', number: 950 },
    ],
  },
  {
    name: 'Red',
    palettes: [
      { hex: '#fef2f2', number: 50 },
      { hex: '#fee2e2', number: 100 },
      { hex: '#fecaca', number: 200 },
      { hex: '#fca5a5', number: 300 },
      { hex: '#f87171', number: 400 },
      { hex: '#ef4444', number: 500 },
      { hex: '#dc2626', number: 600 },
      { hex: '#b91c1c', number: 700 },
      { hex: '#991b1b', number: 800 },
      { hex: '#7f1d1d', number: 900 },
      { hex: '#450a0a', number: 950 },
    ],
  },
  {
    name: 'Orange',
    palettes: [
      { hex: '#fff7ed', number: 50 },
      { hex: '#ffedd5', number: 100 },
      { hex: '#fed7aa', number: 200 },
      { hex: '#fdba74', number: 300 },
      { hex: '#fb923c', number: 400 },
      { hex: '#f97316', number: 500 },
      { hex: '#ea580c', number: 600 },
      { hex: '#c2410c', number: 700 },
      { hex: '#9a3412', number: 800 },
      { hex: '#7c2d12', number: 900 },
      { hex: '#431407', number: 950 },
    ],
  },
  {
    name: 'Amber',
    palettes: [
      { hex: '#fffbeb', number: 50 },
      { hex: '#fef3c7', number: 100 },
      { hex: '#fde68a', number: 200 },
      { hex: '#fcd34d', number: 300 },
      { hex: '#fbbf24', number: 400 },
      { hex: '#f59e0b', number: 500 },
      { hex: '#d97706', number: 600 },
      { hex: '#b45309', number: 700 },
      { hex: '#92400e', number: 800 },
      { hex: '#78350f', number: 900 },
      { hex: '#451a03', number: 950 },
    ],
  },
  {
    name: 'Yellow',
    palettes: [
      { hex: '#fefce8', number: 50 },
      { hex: '#fef9c3', number: 100 },
      { hex: '#fef08a', number: 200 },
      { hex: '#fde047', number: 300 },
      { hex: '#facc15', number: 400 },
      { hex: '#eab308', number: 500 },
      { hex: '#ca8a04', number: 600 },
      { hex: '#a16207', number: 700 },
      { hex: '#854d0e', number: 800 },
      { hex: '#713f12', number: 900 },
      { hex: '#422006', number: 950 },
    ],
  },
  {
    name: 'Lime',
    palettes: [
      { hex: '#f7fee7', number: 50 },
      { hex: '#ecfccb', number: 100 },
      { hex: '#d9f99d', number: 200 },
      { hex: '#bef264', number: 300 },
      { hex: '#a3e635', number: 400 },
      { hex: '#84cc16', number: 500 },
      { hex: '#65a30d', number: 600 },
      { hex: '#4d7c0f', number: 700 },
      { hex: '#3f6212', number: 800 },
      { hex: '#365314', number: 900 },
      { hex: '#1a2e05', number: 950 },
    ],
  },
  {
    name: 'Green',
    palettes: [
      { hex: '#f0fdf4', number: 50 },
      { hex: '#dcfce7', number: 100 },
      { hex: '#bbf7d0', number: 200 },
      { hex: '#86efac', number: 300 },
      { hex: '#4ade80', number: 400 },
      { hex: '#22c55e', number: 500 },
      { hex: '#16a34a', number: 600 },
      { hex: '#15803d', number: 700 },
      { hex: '#166534', number: 800 },
      { hex: '#14532d', number: 900 },
      { hex: '#052e16', number: 950 },
    ],
  },
  {
    name: 'Emerald',
    palettes: [
      { hex: '#ecfdf5', number: 50 },
      { hex: '#d1fae5', number: 100 },
      { hex: '#a7f3d0', number: 200 },
      { hex: '#6ee7b7', number: 300 },
      { hex: '#34d399', number: 400 },
      { hex: '#10b981', number: 500 },
      { hex: '#059669', number: 600 },
      { hex: '#047857', number: 700 },
      { hex: '#065f46', number: 800 },
      { hex: '#064e3b', number: 900 },
      { hex: '#022c22', number: 950 },
    ],
  },
  {
    name: 'Teal',
    palettes: [
      { hex: '#f0fdfa', number: 50 },
      { hex: '#ccfbf1', number: 100 },
      { hex: '#99f6e4', number: 200 },
      { hex: '#5eead4', number: 300 },
      { hex: '#2dd4bf', number: 400 },
      { hex: '#14b8a6', number: 500 },
      { hex: '#0d9488', number: 600 },
      { hex: '#0f766e', number: 700 },
      { hex: '#115e59', number: 800 },
      { hex: '#134e4a', number: 900 },
      { hex: '#042f2e', number: 950 },
    ],
  },
  {
    name: 'Cyan',
    palettes: [
      { hex: '#ecfeff', number: 50 },
      { hex: '#cffafe', number: 100 },
      { hex: '#a5f3fc', number: 200 },
      { hex: '#67e8f9', number: 300 },
      { hex: '#22d3ee', number: 400 },
      { hex: '#06b6d4', number: 500 },
      { hex: '#0891b2', number: 600 },
      { hex: '#0e7490', number: 700 },
      { hex: '#155e75', number: 800 },
      { hex: '#164e63', number: 900 },
      { hex: '#083344', number: 950 },
    ],
  },
  {
    name: 'Sky',
    palettes: [
      { hex: '#f0f9ff', number: 50 },
      { hex: '#e0f2fe', number: 100 },
      { hex: '#bae6fd', number: 200 },
      { hex: '#7dd3fc', number: 300 },
      { hex: '#38bdf8', number: 400 },
      { hex: '#0ea5e9', number: 500 },
      { hex: '#0284c7', number: 600 },
      { hex: '#0369a1', number: 700 },
      { hex: '#075985', number: 800 },
      { hex: '#0c4a6e', number: 900 },
      { hex: '#082f49', number: 950 },
    ],
  },
  {
    name: 'Blue',
    palettes: [
      { hex: '#eff6ff', number: 50 },
      { hex: '#dbeafe', number: 100 },
      { hex: '#bfdbfe', number: 200 },
      { hex: '#93c5fd', number: 300 },
      { hex: '#60a5fa', number: 400 },
      { hex: '#3b82f6', number: 500 },
      { hex: '#2563eb', number: 600 },
      { hex: '#1d4ed8', number: 700 },
      { hex: '#1e40af', number: 800 },
      { hex: '#1e3a8a', number: 900 },
      { hex: '#172554', number: 950 },
    ],
  },
  {
    name: 'Indigo',
    palettes: [
      { hex: '#eef2ff', number: 50 },
      { hex: '#e0e7ff', number: 100 },
      { hex: '#c7d2fe', number: 200 },
      { hex: '#a5b4fc', number: 300 },
      { hex: '#818cf8', number: 400 },
      { hex: '#6366f1', number: 500 },
      { hex: '#4f46e5', number: 600 },
      { hex: '#4338ca', number: 700 },
      { hex: '#3730a3', number: 800 },
      { hex: '#312e81', number: 900 },
      { hex: '#1e1b4b', number: 950 },
    ],
  },
  {
    name: 'Violet',
    palettes: [
      { hex: '#f5f3ff', number: 50 },
      { hex: '#ede9fe', number: 100 },
      { hex: '#ddd6fe', number: 200 },
      { hex: '#c4b5fd', number: 300 },
      { hex: '#a78bfa', number: 400 },
      { hex: '#8b5cf6', number: 500 },
      { hex: '#7c3aed', number: 600 },
      { hex: '#6d28d9', number: 700 },
      { hex: '#5b21b6', number: 800 },
      { hex: '#4c1d95', number: 900 },
      { hex: '#2e1065', number: 950 },
    ],
  },
  {
    name: 'Purple',
    palettes: [
      { hex: '#faf5ff', number: 50 },
      { hex: '#f3e8ff', number: 100 },
      { hex: '#e9d5ff', number: 200 },
      { hex: '#d8b4fe', number: 300 },
      { hex: '#c084fc', number: 400 },
      { hex: '#a855f7', number: 500 },
      { hex: '#9333ea', number: 600 },
      { hex: '#7e22ce', number: 700 },
      { hex: '#6b21a8', number: 800 },
      { hex: '#581c87', number: 900 },
      { hex: '#3b0764', number: 950 },
    ],
  },
  {
    name: 'Fuchsia',
    palettes: [
      { hex: '#fdf4ff', number: 50 },
      { hex: '#fae8ff', number: 100 },
      { hex: '#f5d0fe', number: 200 },
      { hex: '#f0abfc', number: 300 },
      { hex: '#e879f9', number: 400 },
      { hex: '#d946ef', number: 500 },
      { hex: '#c026d3', number: 600 },
      { hex: '#a21caf', number: 700 },
      { hex: '#86198f', number: 800 },
      { hex: '#701a75', number: 900 },
      { hex: '#4a044e', number: 950 },
    ],
  },
  {
    name: 'Pink',
    palettes: [
      { hex: '#fdf2f8', number: 50 },
      { hex: '#fce7f3', number: 100 },
      { hex: '#fbcfe8', number: 200 },
      { hex: '#f9a8d4', number: 300 },
      { hex: '#f472b6', number: 400 },
      { hex: '#ec4899', number: 500 },
      { hex: '#db2777', number: 600 },
      { hex: '#be185d', number: 700 },
      { hex: '#9d174d', number: 800 },
      { hex: '#831843', number: 900 },
      { hex: '#500724', number: 950 },
    ],
  },
  {
    name: 'Rose',
    palettes: [
      { hex: '#fff1f2', number: 50 },
      { hex: '#ffe4e6', number: 100 },
      { hex: '#fecdd3', number: 200 },
      { hex: '#fda4af', number: 300 },
      { hex: '#fb7185', number: 400 },
      { hex: '#f43f5e', number: 500 },
      { hex: '#e11d48', number: 600 },
      { hex: '#be123c', number: 700 },
      { hex: '#9f1239', number: 800 },
      { hex: '#881337', number: 900 },
      { hex: '#4c0519', number: 950 },
    ],
  },
]
