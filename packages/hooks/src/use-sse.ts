import { getServiceBaseURL } from '@sa/utils'
import { type FetchEventSourceInit, fetchEventSource } from '@microsoft/fetch-event-source'
import useBoolean from './use-boolean'

interface Options {
  url: (baseUrl: string) => string
  body: () => FetchEventSourceInit['body']
  initOption?: Omit<FetchEventSourceInit, 'body' | 'signal'>
}

export function useSse() {
  // 判断是否使用代理
  const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
  // 获取服务基础URL
  const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)

  const { bool, setTrue, setFalse } = useBoolean()

  const ctrl = shallowRef()

  async function start(options: Options) {
    try {
      ctrl.value = new AbortController()
      setTrue()
      await fetchEventSource(options.url(baseURL), {
        method: options?.initOption?.method ?? 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(options?.initOption?.headers ?? {}),
        },
        body: options?.body(),
        signal: ctrl.value.signal,
        openWhenHidden: options?.initOption?.openWhenHidden ?? false,
        onopen: async (response) => {
          options?.initOption?.onopen?.(response)
          if (!response.ok) {
            setFalse()
            throw new Error(`HTTP error! status: ${response.status}`)
          }
        },
        onmessage: async (response) => {
          options?.initOption?.onmessage?.(response)
        },
        onerror: (error) => {
          setFalse()
          options?.initOption?.onerror?.(error)
          throw error
        },
        onclose: () => {
          setFalse()
          options?.initOption?.onclose?.()
        },
      })
    }
    finally {
      ctrl.value = null
      setFalse()
    }
  }

  function stop() {
    if (!ctrl.value) {
      return
    }
    ctrl.value.abort()
  }

  return {
    isRunning: bool,
    start,
    stop,
  }
}
