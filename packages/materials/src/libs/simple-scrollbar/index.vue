<script setup lang="ts">
// 导入Simplebar滚动条组件及其样式
import Simplebar from 'simplebar-vue'
import 'simplebar-vue/dist/simplebar.min.css'

defineOptions({
  name: 'SimpleScrollbar',
})
</script>

<template>
  <!-- 滚动条容器 -->
  <div class="h-full flex-1-hidden">
    <!-- Simplebar滚动条组件 -->
    <Simplebar class="h-full">
      <!-- 默认插槽，用于放置需要滚动的内容 -->
      <slot />
    </Simplebar>
  </div>
</template>

<style scoped></style>
