import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { useQuestionsForm } from '@sa/hooks'
import styles from './index.module.scss'

export default defineComponent({
  name: 'MultipleChoice',
  props: {
    item: {
      type: Object as PropType<Question.ProcessedQuestionData>,
      required: true,
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
  },
  emits: ['update:item'],
  setup(props, { emit, expose }) {
    // 修改 item 属性的方法
    const updateItemProperty = (property: keyof Question.ProcessedQuestionData, value: any) => {
      const updatedItem = { ...props.item, [property]: value }
      emit('update:item', updatedItem)
    }
    const isdisabled = computed(() => props.type === 'preview')
    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    // 用户答案，从 item.userAnswer 中获取，多选题应该是数组
    const userAnswer = computed({
      get() {
        return Array.isArray(props.item.userAnswer) ? props.item.userAnswer : []
      },
      set(value: string[]) {
        updateItemProperty('userAnswer', value)
      },
    })

    const handleChange = (value: string) => {
      if (mergedDisabled.value)
        return
      const currentValues = [...userAnswer.value]
      const selected = currentValues.includes(value)
      if (selected) {
        userAnswer.value = currentValues.filter(item => item !== value)
      }
      else {
        userAnswer.value = [...currentValues, value]
      }
    }

    // 渲染函数
    const renderContent = () => (
      <ul class={styles.root}>
        {props.item.options?.map(option => (
          <li key={option.value}>
            <label class={styles.choiceItem}>
              <div
                class={[
                  styles.choiceItemQn,
                  !isdisabled && styles.cursor,
                  userAnswer.value.includes(option.value) ? styles.choiceItemQnChecked : '',
                ]}
                onClick={() => {
                  if (mergedDisabled.value) {
                    return
                  }
                  handleChange(option.value)
                }}
              >
                <span>{option.value}</span>
              </div>
              <span
                class={[styles.choiceItemLabel, 'contents']}
                v-html={option.label}
                v-katex
              />
            </label>
          </li>
        ))}
      </ul>
    )

    // 暴露修改 item 属性的方法
    expose({
      updateItemProperty,
    })

    return {
      renderContent,
    }
  },
  render() {
    return this.renderContent()
  },
})
