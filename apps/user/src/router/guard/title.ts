import { useTitle } from '@vueuse/core'
import type { Router } from 'vue-router'

/**
 * 创建页面标题守卫
 * @param router Vue Router 实例
 * @description 配置路由导航后的页面标题设置逻辑
 * 1. 在路由导航完成后获取路由元信息中的title字段
 * 2. 使用VueUse的useTitle方法动态设置页面标题
 */
export function createDocumentTitleGuard(router: Router) {
  // 路由导航后回调 - 设置页面标题
  router.afterEach((to) => {
    const { title } = to.meta // 从路由元数据获取标题
    useTitle(title) // 设置页面标题
  })
}
