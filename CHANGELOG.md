# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.5.0](https://git.eduwon.cn/zhouenbo/data-base-system/compare/v1.4.1...v1.5.0) (2025-02-25)


### Features

*  测试replace ([b4949ae](https://git.eduwon.cn/zhouenbo/data-base-system/commit/b4949aeb0e7979f9f62d2e5932efe495f3b14144))

## [1.4.1](https://git.eduwon.cn/zhouenbo/data-base-system/compare/v1.4.0...v1.4.1) (2025-02-25)

## 1.4.0 (2025-02-25)


### Features

* 2 ([8e4c544](https://git.eduwon.cn/zhouenbo/data-base-system/commit/8e4c544344d9927c4ec1cd2792754f5994de3b5f))
* 2 ([36e93da](https://git.eduwon.cn/zhouenbo/data-base-system/commit/36e93daeac4d8afc893666631b4571615a83b16d))
* 3 ([a2f2fc9](https://git.eduwon.cn/zhouenbo/data-base-system/commit/a2f2fc91a810f92b2b6ed6bc5c54bf800076de32))
* 99 ([db1b8a1](https://git.eduwon.cn/zhouenbo/data-base-system/commit/db1b8a16901a0c4bf44adc323e4bdfd14c49f385))
* 格式化调整 ([2859ff8](https://git.eduwon.cn/zhouenbo/data-base-system/commit/2859ff8b5229e0982b7b12370f0943edcadf8927))
* 删除冗余 脚本 ([5b15fbe](https://git.eduwon.cn/zhouenbo/data-base-system/commit/5b15fbeec49ae45192a2eb8fb09b5b93251db017))
* 删除微信登录组件 ([7a8f038](https://git.eduwon.cn/zhouenbo/data-base-system/commit/7a8f0389e716febcd98d0de54592b5993099745c))
* 删除i18n ([1e01ba0](https://git.eduwon.cn/zhouenbo/data-base-system/commit/1e01ba0b923d105df9ad36eb63dad26a867e8d22))
* 删除i18n ([9862eee](https://git.eduwon.cn/zhouenbo/data-base-system/commit/9862eeefb0a2e4c5c8078fc0448f95d031293ef4))
* 添加系统管理模块 ([bb6e881](https://git.eduwon.cn/zhouenbo/data-base-system/commit/bb6e881cec4fc36c3a92e48a8998abb2d28e9480))
* 添加字典管理 ([2408543](https://git.eduwon.cn/zhouenbo/data-base-system/commit/2408543929f3dd4e255a9de13ba85cbc19cda6e0))
* 添加only-allow ([8830a98](https://git.eduwon.cn/zhouenbo/data-base-system/commit/8830a987b6292d77757a996078096061710146e7))
* remove冗余文件 ([9f77a6d](https://git.eduwon.cn/zhouenbo/data-base-system/commit/9f77a6df8296401097b33af9846d33db9ef12e46))
