import type { BubbleProps } from 'vue-element-plus-x/types/components/Bubble/types'
import type { ThinkingStatus } from 'vue-element-plus-x/types/components/Thinking/types'
/** 聊天相关类型定义 */
declare namespace Chat {
  /** 消息角色类型 */
  type MessageRole = 'ai' | 'user' | 'system'

  /** 消息位置类型 */
  type MessagePlacement = 'start' | 'end'

  /** 消息内容类型 */
  type MessageContentType = 'text' | 'audio' | 'image' | 'file'

  /** 音频消息数据 */
  interface AudioMessageData {
    // /** 音频文件Blob对象 */
    // audioBlob: Blob
    /** 音频URL（用于播放） */
    audioUrl: string
    /** 音频时长（秒） */
    duration: number
    // /** 音频文件大小（字节） */
    // audioSize: number
    /** 格式化的文件大小 */
    audioSizeFormatted?: string
    /** 音频MIME类型 */
    mimeType: string
    /** 录音数据（包含波形等信息） */
    recordingData?: {
      duration: number
      mimeType: string
      sampleRate: number
      channels: number
      waveformData?: number[]
    }
  }

  /** 图片消息数据 */
  interface ImageMessageData {
    /** 图片文件对象 */
    imageFile?: File
    /** 图片URL */
    imageUrl: string
    /** 缩略图URL */
    thumbUrl?: string
    /** 图片宽度 */
    width?: number
    /** 图片高度 */
    height?: number
    /** 图片文件大小（字节） */
    imageSize?: number
    /** 格式化的文件大小 */
    imageSizeFormatted?: string
    /** 图片MIME类型 */
    mimeType?: string
    /** 图片文件名 */
    fileName?: string
    /** 是否支持预览 */
    previewable?: boolean
  }

  /** 文件消息数据 */
  interface FileMessageData {
    /** 文件对象 */
    file: File
    /** 文件URL */
    fileUrl?: string
    /** 文件名 */
    fileName: string
    /** 文件大小（字节） */
    fileSize: number
    /** 格式化的文件大小 */
    fileSizeFormatted: string
    /** 文件MIME类型 */
    mimeType: string
    /** 文件类型（用于显示图标） */
    fileType: 'word' | 'excel' | 'ppt' | 'pdf' | 'txt' | 'mark' | 'image' | 'audio' | 'video' | 'three' | 'code' | 'database' | 'link' | 'zip' | 'file' | 'unknown'
    /** 上传状态 */
    uploadStatus?: 'uploading' | 'done' | 'error'
    /** 上传进度 */
    uploadPercent?: number
  }

  /** 消息项类型 */
  type MessageItem = BubbleProps & {
    /** 消息唯一标识 */
    key: string
    /** 消息角色 */
    role: MessageRole
    /** 头像URL */
    avatar: string
    /** 发送者姓名 */
    name: string
    /** 思考状态 */
    thinkingStatus?: ThinkingStatus
    /** 思考链是否折叠 */
    thinlCollapse?: boolean
    /** 推理内容 */
    reasoning_content?: string
    /** 消息显示位置：start=左侧，end=右侧 */
    placement?: MessagePlacement
    /** 消息内容类型 */
    contentType?: MessageContentType
    /** 音频消息数据（当contentType为'audio'时使用） */
    audioData?: AudioMessageData
    /** 图片消息数据（当contentType为'image'时使用） */
    imageData?: ImageMessageData
    /** 文件消息数据（当contentType为'file'时使用） */
    fileData?: FileMessageData
    /** 图片列表（支持多图片消息） */
    images?: ImageMessageData[]
    /** 文件列表（支持多文件消息） */
    files?: FileMessageData[]

  }
}
export = Chat
export as namespace Chat
