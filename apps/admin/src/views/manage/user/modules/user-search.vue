<script setup lang="ts">
import { computed } from 'vue'
import { useFormRules, useNaiveForm } from '@sa/hooks'
import { translateOptions } from '@sa/utils'
import { enableStatusOptions, userGenderOptions } from '@sa/constants/business'

defineOptions({
  name: 'UserSearch',
})

const emit = defineEmits<Emits>()

interface Emits {
  (e: 'reset'): void
  (e: 'search'): void
}

const { formRef, validate, restoreValidation } = useNaiveForm()

const model = defineModel<Api.SystemManage.UserSearchParams>('model', { required: true })

type RuleKey = Extract<keyof Api.SystemManage.UserSearchParams, 'userEmail' | 'userPhone'>

const rules = computed<Record<RuleKey, App.Global.FormRule>>(() => {
  const { patternRules } = useFormRules() // inside computed to make locale reactive

  return {
    userEmail: patternRules.email,
    userPhone: patternRules.phone,
  }
})

async function reset() {
  await restoreValidation()
  emit('reset')
}

async function search() {
  await validate()
  emit('search')
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="搜索" name="user-search">
        <NForm
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          :label-width="80"
        >
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="用户名" path="userName" class="pr-24px">
              <NInput v-model:value="model.userName" placeholder="请输入用户名" />
            </NFormItemGi>
            <NFormItemGi
              span="24 s:12 m:6"
              label="性别"
              path="userGender"
              class="pr-24px"
            >
              <NSelect
                v-model:value="model.userGender"
                placeholder="请选择性别"
                :options="translateOptions(userGenderOptions)"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="昵称" path="nickName" class="pr-24px">
              <NInput v-model:value="model.nickName" placeholder="请输入昵称" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="手机号" path="userPhone" class="pr-24px">
              <NInput v-model:value="model.userPhone" placeholder="请输入手机号" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="邮箱" path="userEmail" class="pr-24px">
              <NInput v-model:value="model.userEmail" placeholder="请输入邮箱" />
            </NFormItemGi>
            <NFormItemGi
              span="24 s:12 m:6"
              label="用户状态"
              path="userStatus"
              class="pr-24px"
            >
              <NSelect
                v-model:value="model.status"
                placeholder="请选择用户状态"
                :options="translateOptions(enableStatusOptions)"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 m:12" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  重置
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  搜索
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
