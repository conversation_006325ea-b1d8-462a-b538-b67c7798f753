/**
 * 聊天相关的工具函数
 */
import { nanoid } from '@sa/utils'

/**
 * 聊天配置常量
 */
export const CHAT_CONFIG = {
  /** 分页配置 */
  PAGINATION: {
    PAGE_SIZE: 10,
    INITIAL_PAGE: 1,
    NO_MORE_DATA_FLAG: 0,
  },
  /** SSE 配置 */
  SSE: {
    DONE_PREFIX: 'DONE',
    CONTENT_TYPE: 'application/json',
  },
  /** 消息配置 */
  MESSAGE: {
    SYSTEM_ROLE: 'system' as const,
    USER_ROLE: 'user' as const,
    AI_PLACEMENT: 'start' as const,
    START_PLACEMENT: 'start' as const,
  },
  /** 任务类型 */
  TASK_TYPE: {
    AGENT: 0,
  },
  /** 侧边栏配置 */
  DRAWER: {
    WIDTH: '400px',
    TRANSITION_DURATION: '300ms',
  },
} as const

/**
 * 创建系统消息
 * @param content 消息内容
 * @param aiName AI名称
 * @param aiAvatar AI头像
 * @returns 系统消息对象
 */
export function createSystemMessage(
  content: string,
  aiName: string,
  aiAvatar: string,
): Chat.MessageItem {
  return {
    key: nanoid(),
    role: CHAT_CONFIG.MESSAGE.SYSTEM_ROLE,
    content,
    avatar: aiAvatar,
    placement: CHAT_CONFIG.MESSAGE.START_PLACEMENT,
    name: aiName,
  }
}

/**
 * 创建AI回复消息
 * @param aiName AI名称
 * @param aiAvatar AI头像
 * @returns AI消息对象
 */
export function createAIMessage(
  aiName: string,
  aiAvatar: string,
): Chat.MessageItem {
  return {
    key: nanoid(),
    loading: true,
    role: CHAT_CONFIG.MESSAGE.SYSTEM_ROLE,
    content: '',
    avatar: aiAvatar,
    placement: CHAT_CONFIG.MESSAGE.AI_PLACEMENT,
    name: aiName,
  }
}

/**
 * 处理API响应错误
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 */
export function handleApiError(error: any, defaultMessage: string) {
  console.error(defaultMessage, error)
  window.$message?.error(defaultMessage)
}

/**
 * 安全地中止AbortController
 * @param controller AbortController实例
 */
export function safeAbort(controller: AbortController | null) {
  if (controller && !controller.signal.aborted) {
    controller.abort()
  }
}

/**
 * 检查是否有更多数据可加载
 * @param pageIndex 当前页码
 * @returns 是否有更多数据
 */
export function hasMoreData(pageIndex: number): boolean {
  return pageIndex !== CHAT_CONFIG.PAGINATION.NO_MORE_DATA_FLAG
}

/**
 * 格式化API请求参数
 * @param baseParams 基础参数
 * @param baseParams.AgentId 智能体ID
 * @param baseParams.AgentTaskId 智能体任务ID
 * @param pageIndex 页码
 * @param userInfo 用户信息
 * @param userInfo.userId 用户ID
 * @returns 格式化后的参数
 */
export function formatApiParams(
  baseParams: { AgentId: string, AgentTaskId: string },
  pageIndex: number,
  userInfo: { userId: string },
) {
  return {
    PageIndex: pageIndex,
    PageSize: CHAT_CONFIG.PAGINATION.PAGE_SIZE,
    ...baseParams,
    StudentId: userInfo.userId,
    Type: CHAT_CONFIG.TASK_TYPE.AGENT,
  }
}
