import type { Router } from 'vue-router'
import { createRouteGuard } from './route'
import { createProgressGuard } from './progress'
import { createDocumentTitleGuard } from './title'

/**
 * 创建路由守卫
 * @param router - Vue Router 实例
 * @description 初始化并注册所有路由守卫
 * 1. 创建进度条守卫 - 处理页面加载进度
 * 2. 创建路由守卫 - 处理路由权限和拦截
 * 3. 创建文档标题守卫 - 动态设置页面标题
 */
export function createRouterGuard(router: Router) {
  createProgressGuard(router) // 设置页面加载进度条
  createRouteGuard(router) // 设置路由权限控制
  createDocumentTitleGuard(router) // 设置动态页面标题
}
