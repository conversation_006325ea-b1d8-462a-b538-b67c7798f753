<script setup lang="ts" name="FileUpload">
import { computed, ref, watch } from 'vue'
import type { UploadCustomRequestOptions, UploadFileInfo, UploadInst, UploadOnRemove } from 'naive-ui'
import { NUpload, useMessage } from 'naive-ui'
import type { FormItemInjection } from 'naive-ui/es/_mixins/use-form-item'
import { HuaweiOBSUploader } from '@sa/utils'
import type { OBSConfig } from '@sa/utils'

type FileItem = string | string[]

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  max: 5,
  multiple: true,
  accept: 'image/*',
  disabled: false,
  showFileList: true,
  listType: 'image-card',
  isDirectoryDnd: false,
})

const emit = defineEmits<Emits>()

const NFormItem = inject<FormItemInjection>('n-form-item')

interface Props {
  /** v-model 绑定的文件列表 */
  modelValue?: FileItem
  /** 最大上传数量 */
  max?: number
  /** 是否支持多选 */
  multiple?: boolean
  /** 接受的文件类型 */
  accept?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示文件列表 */
  showFileList?: boolean
  /** 列表类型 */
  listType?: 'text' | 'image' | 'image-card'
  /** 是否支持拖拽文件夹 */
  isDirectoryDnd?: boolean
  /** OBS 配置 */
  obsConfig?: OBSConfig
  /** 上传前的钩子 */
  beforeUpload?: (file: File) => boolean | Promise<boolean>
}

interface Emits {
  (e: 'update:modelValue', value: FileItem[]): void

  (e: 'change', fileList: FileItem[]): void

  (e: 'upload-success', file: FileItem): void

  (e: 'upload-error', error: any, file: File): void

  (e: 'remove', file: FileItem): void

  (e: 'preview', file: FileItem): void
}

const message = useMessage()
const uploadRef = ref<UploadInst>()

// 内部文件列表
const internalFileList = ref<UploadFileInfo[]>([])

const fileList = computed(() => {
  return Array.isArray(props.modelValue)
    ? props.modelValue
    : [props.modelValue]
})

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  () => {
    // 映射文件
    internalFileList.value = fileList.value.map((url) => {
      return {
        id: url,
        url,
        name: url!.split('/').pop() || '',
        status: 'finished',
      }
    })
  },
  { immediate: true, deep: true },
)

// 检查是否达到最大上传数量
const isMaxReached = computed(() => {
  return props.modelValue.length >= props.max
})

// 上传前检查
async function handleBeforeUpload(data: {
  file: UploadFileInfo
  fileList: UploadFileInfo[]
}) {
  const { file } = data

  // 检查数量限制
  if (isMaxReached.value) {
    message.warning(`最多只能上传 ${props.max} 个文件`)
    return false
  }

  // 执行自定义的 beforeUpload 钩子
  if (props.beforeUpload && file.file) {
    try {
      return props.beforeUpload(file.file)
    }
    catch (error) {
      console.warn(error)
      message.error('文件验证失败')
      return false
    }
  }

  return true
}

// 华为云 OBS 上传函数
async function uploadToOBS(
  file: File,
  onProgress?: (progress: number) => void,
): Promise<string> {
  try {
    // 创建华为云 OBS 上传器实例
    const uploader = new HuaweiOBSUploader({})

    // 执行上传
    const result = await uploader.upload({
      file,
      onProgress,
    })

    return result.url
  }
  catch (error) {
    console.error('华为云 OBS 上传失败:', error)
    throw error
  }
}

// 自定义上传处理
async function handleCustomRequest(options: UploadCustomRequestOptions) {
  const { file, onProgress, onFinish, onError } = options
  try {
    // 执行华为云 OBS 上传，传递真实的进度回调
    const url = await uploadToOBS(file.file!, (progress: number) => {
      onProgress({ percent: progress })
    })

    onFinish()

    emit('update:modelValue', [...fileList.value, url])

    validateThisField()
  }
  catch {
    onError()
  }
}

function handleRemove(data: Parameters<UploadOnRemove>[0]) {
  const { index } = data
  if (index > -1) {
    const newList = fileList.value.filter((_, i) => i !== index)
    emit('update:modelValue', newList)
    validateThisField()
  }
  return false
}

// 在子组件中主动触发 form-item 校验
function validateThisField() {
  if (!NFormItem) {
    return
  }
  NFormItem.handleContentChange()
}

// 暴露方法给父组件
defineExpose({
  uploadRef,
})
</script>

<template>
  <div>
    <NUpload
      ref="uploadRef"
      v-model:file-list="internalFileList"
      :multiple="multiple"
      :accept="accept"
      :disabled="disabled"
      :show-file-list="showFileList"
      :list-type="listType"
      :max="max"
      :custom-request="handleCustomRequest"
      :on-before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      v-bind="$attrs"
    >
      <NUploadDragger v-if="isDirectoryDnd">
        <slot />
      </NUploadDragger>
    </NUpload>
  </div>
</template>

<style scoped></style>
