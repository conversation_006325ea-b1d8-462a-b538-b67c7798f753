import type { CreateAxiosDefaults } from 'axios'
import type { IAxiosRetryConfig } from 'axios-retry'
import { stringify } from 'qs'
import { isHttpSuccess } from './shared'
import type { RequestOption } from './type'

/**
 * 创建默认请求选项配置
 * @template ResponseData 响应数据类型，默认为any
 * @param options 可选的请求选项配置，会覆盖默认配置
 * @returns 返回完整的请求选项配置对象
 */
export function createDefaultOptions<ResponseData = any>(options?: Partial<RequestOption<ResponseData>>) {
  // 默认请求选项配置
  const opts: RequestOption<ResponseData> = {
    // 请求拦截器钩子，默认不做处理
    onRequest: async config => config,
    // 判断请求是否成功的钩子，默认返回true
    isBackendSuccess: _response => true,
    // 后端请求失败处理钩子，默认空函数
    onBackendFail: async () => {},
    // 响应数据转换钩子，默认返回response.data
    transformBackendResponse: async response => response.data,
    // 错误处理钩子，默认空函数
    onError: async () => {},
  }
  // 合并用户自定义配置
  Object.assign(opts, options)

  return opts
}

/**
 * 创建请求重试配置
 * @param config 可选的axios默认配置，会覆盖默认重试配置
 * @returns 返回完整的请求重试配置对象
 */
export function createRetryOptions(config?: Partial<CreateAxiosDefaults>) {
  // 默认重试配置：不进行重试
  const retryConfig: IAxiosRetryConfig = {
    retries: 0, // 重试次数设置为0
  }
  // 合并用户自定义配置
  Object.assign(retryConfig, config)

  return retryConfig
}

/**
 * 创建axios默认配置
 * @param config 可选的axios配置，会覆盖默认配置
 * @returns 返回完整的axios配置对象
 */
export function createAxiosConfig(config?: Partial<CreateAxiosDefaults>) {
  // 默认超时时间10秒
  const TEN_SECONDS = 10 * 1000

  // 默认axios配置
  const axiosConfig: CreateAxiosDefaults = {
    // 请求超时时间
    timeout: TEN_SECONDS,
    // 默认JSON内容类型
    headers: {
      'Content-Type': 'application/json',
    },
    // 自定义HTTP状态码验证
    validateStatus: isHttpSuccess,
    // 参数序列化使用qs库
    paramsSerializer: (params) => {
      return stringify(params)
    },
  }
  // 合并用户自定义配置
  Object.assign(axiosConfig, config)

  return axiosConfig
}
