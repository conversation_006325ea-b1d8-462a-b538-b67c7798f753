<script setup lang="ts">
import { computed, nextTick, ref } from 'vue'
import { useFileDialog } from '@vueuse/core'
import type { CSSProperties } from 'vue'
import { HuaweiOBSUploader } from '@sa/utils'

interface UploadResult {
  /** 文件ID */
  fileId: string
  /** 文件名称 */
  fileName: string
  /** 文件大小 */
  fileSize: number
  /** 文件类型 */
  fileType: string
  /** 文件URL */
  fileUrl: string
  /** 缩略图URL（可选） */
  thumbUrl?: string
  /** 上传时间 */
  uploadTime: string
}
defineOptions({
  name: 'FilesSelect',
})

// 修复vue-element-plus-x类型定义中的拼写错误
export interface FixedFilesCardProps {
  uid?: string | number
  name?: string
  fileSize?: number
  fileType?: 'word' | 'excel' | 'ppt' | 'pdf' | 'txt' | 'mark' | 'image' | 'audio' | 'video' | 'three' | 'code' | 'database' | 'link' | 'zip' | 'file' | 'unknown'
  description?: string
  url?: string
  thumbUrl?: string
  imgFile?: File | Blob
  iconSize?: string
  iconColor?: string
  showDelIcon?: boolean
  maxWidth?: string
  noStyle?: boolean
  style?: CSSProperties
  hoverStyle?: CSSProperties // 修复拼写错误
  imgVariant?: 'rectangle' | 'square'
  imgPreview?: boolean
  imgPreviewMask?: boolean
  status?: 'uploading' | 'done' | 'error'
  percent?: number // 修复拼写错误
  errorTip?: string
}

export type FileItem = FixedFilesCardProps & {
  file: File
}

// 文件列表
const filesList = ref<FileItem[]>([])

const popoverRef = ref()
const { reset, open, onChange } = useFileDialog({
  // 允许所有图片文件，文档文件，音视频文件
  accept: 'image/*,video/*,audio/*,application/*',
  directory: false, // 是否允许选择文件夹
  multiple: true, // 是否允许多选
})

/**
 * 添加文件到列表
 * @param files 要添加的文件数组
 */
function addFiles(files: FileItem[]) {
  filesList.value = [...filesList.value, ...files]
}

/**
 * 删除指定索引的文件
 * @param index 要删除的文件索引
 */
function removeFile(index: number) {
  if (index >= 0 && index < filesList.value.length) {
    // 释放URL对象内存
    const file = filesList.value[index]
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
    filesList.value.splice(index, 1)
  }
}

/**
 * 根据uid删除文件
 * @param uid 文件的唯一标识
 */
function removeFileByUid(uid: string) {
  const index = filesList.value.findIndex(file => file.uid === uid)
  if (index !== -1) {
    removeFile(index)
  }
}

/**
 * 清空所有文件
 */
function clearFiles() {
  // 释放所有URL对象内存
  filesList.value.forEach((file) => {
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url)
    }
  })
  filesList.value = []
}

/**
 * 获取文件总数
 */
function getFilesCount() {
  return filesList.value.length
}

/**
 * 检查是否有文件
 */
function hasFiles() {
  return filesList.value.length > 0
}

/**
 * 更新文件状态
 * @param uid 文件的唯一标识
 * @param status 新的状态
 * @param percent 上传进度（可选）
 * @param errorTip 错误提示（可选）
 */
function updateFileStatus(uid: string, status: 'uploading' | 'done' | 'error', percent?: number, errorTip?: string) {
  const file = filesList.value.find(f => f.uid === uid)
  if (file) {
    file.status = status
    if (percent !== undefined) {
      file.percent = percent
    }
    if (errorTip !== undefined) {
      file.errorTip = errorTip
    }
  }
}

/**
 * 更新文件上传结果
 * @param uid 文件的唯一标识
 * @param uploadResult 上传结果
 */
function updateFileUploadResult(uid: string, uploadResult: UploadResult) {
  const file = filesList.value.find(f => f.uid === uid)
  if (file) {
    file.status = 'done'
    file.percent = 100
    // 可以根据需要更新其他字段
    file.url = uploadResult.fileUrl
    if (uploadResult.thumbUrl) {
      file.thumbUrl = uploadResult.thumbUrl
    }
  }
}

function handleUploadFiles() {
  open()
  // 关闭 popover
  popoverRef.value?.setShow(false)
}

/**
 * 上传单个文件到华为云 OBS
 * @param file 文件对象
 * @param uid 文件唯一标识
 */
async function uploadSingleFile(file: File, uid: string) {
  try {
    // 更新文件状态为上传中
    updateFileStatus(uid, 'uploading', 0)

    // 创建华为云 OBS 上传器实例
    const uploader = new HuaweiOBSUploader({})

    // 上传文件并监听进度
    const obsResult = await uploader.upload({
      file,
      onProgress: (percent: number) => {
        updateFileStatus(uid, 'uploading', percent)
      },
      acl: 'public-read', // 设置为公开读取
      metadata: {
        'uploaded-by': 'chat-file-upload',
        'upload-time': new Date().toISOString(),
      },
    })

    // 转换为组件期望的格式
    const result: UploadResult = {
      fileId: obsResult.key,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      fileUrl: obsResult.url,
      uploadTime: obsResult.uploadTime.toISOString(),
    }

    // 上传成功，更新文件状态和结果
    updateFileUploadResult(uid, result)

    return result
  }
  catch (error) {
    // 上传失败，更新文件状态为错误
    const errorMessage = error instanceof Error ? error.message : '上传失败'
    updateFileStatus(uid, 'error', 0, errorMessage)
    throw error
  }
}

onChange((files) => {
  if (!files)
    return
  const arr = [] as FileItem[]
  for (let i = 0; i < files!.length; i++) {
    const file = files![i]
    const uid = crypto.randomUUID()
    arr.push({
      uid, // 不写 uid，文件列表展示不出来，elx 1.2.0 bug 待修复
      name: file.name,
      fileSize: file.size,
      file,
      maxWidth: '200px',
      showDelIcon: true, // 显示删除图标
      imgPreview: true, // 显示图片预览
      imgVariant: 'square', // 图片预览的形状
      url: URL.createObjectURL(file), // 图片预览地址
      status: 'uploading', // 初始状态为上传中
      percent: 0, // 初始进度为0
    })
  }

  // 添加文件到本地列表
  addFiles(arr)

  // 重置文件选择器
  nextTick(() => reset())

  // 上传所有文件
  arr.forEach((item) => {
    uploadSingleFile(item.file, item.uid as string).catch((error) => {
      console.error('文件上传失败:', error)
    })
  })
})

// 暴露给父组件的方法和数据
defineExpose({
  // 文件列表数据
  filesList: computed(() => filesList.value),

  // 文件管理方法
  addFiles,
  removeFile,
  removeFileByUid,
  clearFiles,
  getFilesCount,
  hasFiles,

  // 文件状态管理方法
  updateFileStatus,
  updateFileUploadResult,

  // 文件上传方法
  uploadSingleFile,

  // 文件选择方法
  handleUploadFiles,
})
</script>

<template>
  <div class="files-select">
    <NPopover ref="popoverRef" trigger="click">
      <template #trigger>
        <div class="flex cursor-pointer items-center gap-4px border-1px border-[rgba(0,0,0,0.08)] rounded-10px border-solid p-10px font-size-14px hover:bg-[rgba(0,0,0,.04)]">
          <SvgIcon icon="mdi:paperclip" />
        </div>
      </template>
      <div class="popover-content-box">
        <div
          class="popover-content-item flex cursor-pointer items-center gap-4px rounded-10px p-10px font-size-14px hover:bg-[rgba(0,0,0,.04)]"
          @click="handleUploadFiles"
        >
          <SvgIcon icon="mdi:upload" />
          <div class="font-size-14px">
            上传文件或图片
          </div>
        </div>
      </div>
    </NPopover>
  </div>
</template>
