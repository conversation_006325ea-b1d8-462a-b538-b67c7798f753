<script setup lang="ts">
import type { PaginationInfo } from '@sa/hooks'

interface Props {
  pagination: PaginationInfo
  loading: boolean
  loadingMore: boolean
  emptyDescription?: string
  /** 加载更多阈值，距离底部多少像素时触发加载更多 */
  loadMoreThreshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  loadMoreThreshold: 50,
})
const emit = defineEmits(['loadMore'])

function onReachBottom(e: Event) {
  const el = e.target as HTMLElement
  if (el.scrollHeight - el.scrollTop <= el.clientHeight + props.loadMoreThreshold) {
    emit('loadMore')
  }
}
</script>

<template>
  <div
    v-if="!loading && !loadingMore && pagination.total === 0"
    class="h-full flex-center"
  >
    <NEmpty :description="emptyDescription || '暂无数据'" />
  </div>
  <NScrollbar v-else class="h-full" @scroll="onReachBottom">
    <slot />
    <div v-if="loadingMore" class="w-full flex-center items-center py-32px">
      <NSpin size="small" class="mr-12px" />
      <span>加载中...</span>
    </div>
    <div v-if="!pagination.hasMore" class="w-full flex-center items-center py-32px text-[#999]">
      没有更多数据了 ~
    </div>
  </NScrollbar>
</template>

<style scoped></style>
