import { fabric } from 'fabric'
import initFabricEraserPlugin from './lib/eraser_brush.mixin.js'
import {
  ACTION_BRUSH,
  ACTION_CIRCLE,
  ACTION_ERASER,
  ACTION_LINE,
  ACTION_MOVE,
  ACTION_RECT,
  ACTION_TEXT,
  FILL_COLOR,
  FONT_SIZE,
  STROKE_COLOR,
  STROKE_SIZE,
} from './util/const'
import type { DrawingBoardOptions } from './types'

import { initCircle, initLine, initMove, initRect, initText } from './core/handler'

initFabricEraserPlugin(fabric)

export default class FabricDrawingBoard {
  _fabric: typeof fabric
  _fcvs: fabric.Canvas | null
  _curAction: string | null
  _beginMouseDoing: boolean
  _isEditHistory: boolean
  _historyIdx: number
  _historyArr: string[]
  _tmpText: fabric.Textbox | null
  _tmpDrawingObj: fabric.Object | null
  _mouseFrom: { x: number, y: number }
  _mouseTo: { x: number, y: number }
  _canvasId: string
  _el?: HTMLCanvasElement | null
  _brushColor: string
  _strokeColor: string
  _fillColor: string
  _bgColor: string
  _textEditBgColor: string
  _brushSize: number
  _strokeSize: number
  _eraserSize: number
  _fontSize: number
  _drawingTimer?: NodeJS.Timeout | null

  constructor(options: DrawingBoardOptions) {
    this._fabric = fabric
    this._fcvs = null
    this._curAction = null
    this._beginMouseDoing = false
    this._isEditHistory = false
    this._historyIdx = 0
    this._historyArr = []
    this._tmpText = null
    this._tmpDrawingObj = null
    this._mouseFrom = { x: 0, y: 0 }
    this._mouseTo = { x: 0, y: 0 }
    this._canvasId = ''
    this._el = null
    this._brushColor = ''
    this._strokeColor = ''
    this._fillColor = ''
    this._bgColor = ''
    this._textEditBgColor = ''
    this._brushSize = 0
    this._strokeSize = 0
    this._eraserSize = 0
    this._fontSize = 0

    this._init(options)
    this._bindEvent()
  }

  /**
   * 初始化绘图板的方法，用于设置初始状态和配置。
   * @param options
   */
  protected _init(options: DrawingBoardOptions) {
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this

    fdb._fcvs = null // fabric canvas 对象
    fdb._curAction = null // 当前操作类型
    fdb._beginMouseDoing = false // 当前是否开启了鼠标操作

    fdb._isEditHistory = false // 当前是否正在改变画布历史
    fdb._historyIdx = 0 // 当前画布历史节点索引
    fdb._historyArr = [] // 画布历史集合

    fdb._tmpText = null // 临时绘制的文字
    fdb._tmpDrawingObj = null // 临时绘制的图形

    fdb._mouseFrom = { x: 0, y: 0 } // 鼠标操作开始点
    fdb._mouseTo = { x: 0, y: 0 } // 鼠标操作结束点

    fdb._canvasId = options.canvasId || '' // canvasId
    fdb._el = options.el

    fdb._brushColor = options.brushColor || STROKE_COLOR // 画笔颜色
    fdb._strokeColor = options.strokeColor || STROKE_COLOR // 线条颜色
    fdb._fillColor = options.fillColor || FILL_COLOR // 填充色
    fdb._bgColor = options.bgColor || FILL_COLOR // 背景色
    fdb._textEditBgColor = options.textEditBgColor || '#fff' // 文字编辑时背景色

    fdb._brushSize = options.brushSize || STROKE_SIZE // 画笔大小
    fdb._strokeSize = options.strokeSize || STROKE_SIZE // 线条大小
    fdb._eraserSize = options.eraserSize || STROKE_SIZE // 橡皮大小
    fdb._fontSize = options.fontSize || FONT_SIZE // 文字大小

    if (!options.canvasId && !options.el) {
      throw new Error('you need to pass in a canvasId to constructed function')
    }
    const canvasEl = (fdb._el || document.getElementById(fdb._canvasId)) as HTMLCanvasElement | null
    if (!canvasEl) {
      throw new Error('you need to pass in a valid canvasId to constructed function')
    }
    else {
      // 动态设置canvas画布的宽高
      canvasEl.width = (canvasEl.parentNode! as HTMLElement).offsetWidth
      canvasEl.height = (canvasEl.parentNode! as HTMLElement).offsetHeight
    }
    // 初始化 fabric canvas
    fdb._fcvs = new fdb._fabric.Canvas(canvasEl)
    // 设置画布背景色不受缩放与平移的影响
    ;(fdb._fcvs as any).set('backgroundVpt', false)
    // 禁止用户进行组选择
    fdb._fcvs.selection = false
    // 设置鼠标悬浮在绘图上的样式
    fdb._fcvs.hoverCursor = 'default'
    // 设置画布背景色
    fdb.setBgColor(this._bgColor)

    // 记录画布原始状态
    fdb._historyArr.push(JSON.stringify(fdb._fcvs))
  }

  private mouseDown() {
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this
    if (!fdb._fcvs) {
      return
    }
    fdb._fcvs.on('mouse:down', (options: { e: MouseEvent }) => {
      if (fdb._curAction !== ACTION_TEXT && fdb._tmpText) {
        this.resetTmpText()
      }
      const mouse_action_arr = [ACTION_LINE, ACTION_RECT, ACTION_CIRCLE, ACTION_TEXT, ACTION_MOVE]
      if (mouse_action_arr.includes(fdb._curAction!)) {
        fdb._mouseFrom.x = options.e.clientX - fdb._fcvs!._offset.left
        fdb._mouseFrom.y = options.e.clientY - fdb._fcvs!._offset.top

        if (fdb._curAction === ACTION_TEXT) {
          initText(fdb)
        }
        else {
          fdb._beginMouseDoing = true
        }
      }
    })
  }

  private mouseMove() {
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this
    if (!fdb._fcvs) {
      return
    }
    fdb._fcvs.on('mouse:move', (options: { e: MouseEvent }) => {
      if (fdb._beginMouseDoing) {
        fdb._mouseTo.x = options.e.clientX - fdb._fcvs!._offset.left
        fdb._mouseTo.y = options.e.clientY - fdb._fcvs!._offset.top
        switch (fdb._curAction) {
          case ACTION_LINE:
            initLine(fdb)
            break
          case ACTION_RECT:
            initRect(fdb)
            break
          case ACTION_CIRCLE:
            initCircle(fdb)
            break
          case ACTION_MOVE:
            initMove(fdb)
            break
        }
      }
    })
  }

  private mouseUp() {
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this
    if (!fdb._fcvs) {
      return
    }
    fdb._fcvs.on('mouse:up', () => {
      const handler_action_arr = [ACTION_LINE, ACTION_RECT, ACTION_CIRCLE, ACTION_MOVE]
      if (handler_action_arr.includes(fdb._curAction!)) {
        fdb._tmpDrawingObj = null
        fdb._beginMouseDoing = false
        fdb._mouseFrom = { x: 0, y: 0 }
        fdb._mouseTo = { x: 0, y: 0 }
        if (fdb._curAction === ACTION_MOVE) {
          fdb._fcvs!.setViewportTransform(fdb._fcvs!.viewportTransform)
        }
      }
    })
  }

  private afterRender() {
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this
    if (!fdb._fcvs) {
      return
    }
    fdb._fcvs.on('after:render', () => {
      if (!fdb._isEditHistory) {
        if (fdb._drawingTimer) {
          clearTimeout(fdb._drawingTimer)
          fdb._drawingTimer = null
        }
        fdb._drawingTimer = setTimeout(() => {
          if (fdb._historyIdx === 0) {
            fdb._historyArr = [fdb._historyArr[0]!]
          }
          fdb._historyArr.push(JSON.stringify(fdb._fcvs))
          fdb._historyIdx++
        }, fdb._curAction !== ACTION_TEXT ? 100 : 2000)
      }
      else {
        fdb._isEditHistory = false
      }
    })
  }

  /**
   * 绑定事件处理程序，用于监听用户交互事件。
   */
  protected _bindEvent() {
    this.mouseDown()
    this.mouseMove()
    this.mouseUp()
    this.afterRender()
  }

  getFabric() {
    return this._fabric
  }

  getFabricCanvas() {
    return this._fcvs
  }

  /**
   * 设置画布的背景颜色。
   * @param color - 要设置的背景颜色，使用颜色字符串表示。
   */
  setBgColor(color: string) {
    this._bgColor = color
    if (this._fcvs && this._fcvs.setBackgroundColor) {
      this._fcvs.setBackgroundColor(this._bgColor, this._fcvs.renderAll.bind(this._fcvs))
    }
  }

  /**
   * 设置画笔的大小。
   * @param size
   */
  setBrushSize(size: number | string) {
    this._brushSize = typeof size === 'string' ? Number.parseInt(size, 10) : size
  }

  private resetCanvas() {
    this._fcvs!.isDrawingMode = false
    const drawObjects = this._fcvs!.getObjects()
    if (drawObjects.length > 0) {
      drawObjects.forEach((item) => {
        item.set('selectable', false)
      })
    }
  }

  /**
   * 使用画笔进行绘制操作。
   */
  drawBrush() {
    this._curAction = ACTION_BRUSH
    this.resetCanvas()

    this._fcvs!.freeDrawingBrush = new this._fabric.PencilBrush(this._fcvs!)
    this._fcvs!.isDrawingMode = true
    this._fcvs!.freeDrawingBrush.color = this._brushColor
    this._fcvs!.freeDrawingBrush.width = this._brushSize
  }

  /**
   * 设置描边的大小。
   * @param size - 要设置的描边大小，可以是数字或字符串。
   */
  setStrokeSize(size: number | string) {
    this._strokeSize = typeof size === 'string' ? Number.parseInt(size, 10) : size
  }

  /**
   * 绘制一条直线。
   */
  drawLine() {
    this._curAction = ACTION_LINE
    this.resetCanvas()
  }

  /**
   * 绘制一个矩形。
   */
  drawRect() {
    this._curAction = ACTION_RECT
    this.resetCanvas()
  }

  /**
   * 绘制一个圆形。
   */
  drawCircle() {
    this._curAction = ACTION_CIRCLE
    this.resetCanvas()
  }

  /**
   * 设置文本的字体大小。
   * @param size - 要设置的字体大小，使用数字表示。
   */
  setFontSize(size: number | string) {
    this._fontSize = typeof size === 'string' ? Number.parseInt(size, 10) : size
  }

  /**
   * 绘制文本。
   */
  drawText() {
    this._curAction = ACTION_TEXT
    this.resetCanvas()
  }

  /**
   * 设置橡皮擦的大小。
   * @param size - 要设置的橡皮擦大小，使用数字表示。
   */
  setEraserSize(size: number | string) {
    this._eraserSize = typeof size === 'string' ? Number.parseInt(size, 10) : size
  }

  /**
   * 使用橡皮擦进行擦除操作。
   */
  useEraser() {
    this._curAction = ACTION_ERASER
    this.resetCanvas()
    if (!this._fcvs) {
      return
    }
    this._fcvs.freeDrawingBrush = new this._fabric.EraserBrush(this._fcvs)
    this._fcvs.freeDrawingBrush.width = this._eraserSize
    this._fcvs.isDrawingMode = true
  }

  /**
   * 使用移动工具移动画布上的对象。
   */
  useMove() {
    if (this._curAction === ACTION_MOVE)
      return
    this._curAction = ACTION_MOVE
    this.resetCanvas()
  }

  private zoomCanvas(flag: number) {
    let zoom = this._fcvs!.getZoom()
    if (flag > 0) {
      zoom *= 1.1
    }
    else {
      zoom *= 0.9
    }

    zoom = zoom > 20 ? 20 : zoom
    zoom = zoom < 0.01 ? 0.01 : zoom

    this._fcvs!.setZoom(zoom)
  }

  /**
   * 缩小画布视图。
   */
  canvasZoomDown() {
    this.zoomCanvas(-1)
  }

  /**
   * 放大画布视图。
   */
  canvasZoomUp() {
    this.zoomCanvas(1)
  }

  private changeCanvasHistory(flag: number) {
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this
    if (!fdb._fcvs) {
      return
    }
    fdb._isEditHistory = true
    const historyIdx = fdb._historyIdx + flag

    if (historyIdx < 0)
      return
    if (historyIdx >= fdb._historyArr.length)
      return

    if (fdb._historyArr[historyIdx]) {
      fdb._fcvs.loadFromJSON(fdb._historyArr[historyIdx], () => {
        if (fdb._fcvs!.getObjects().length > 0) {
          fdb._fcvs!.getObjects().forEach((item) => {
            item.set('selectable', false)
          })
        }
        fdb._historyIdx = historyIdx
      })
    }
  }

  /**
   * 撤销上一步操作。
   */
  canvasUndo() {
    this.changeCanvasHistory(-1)
  }

  /**
   * 重做上一步撤销的操作。
   */
  canvasRedo() {
    this.changeCanvasHistory(1)
  }

  /**
   * 清空画布内容。
   */
  canvasClear() {
    if (!this._fcvs) {
      return
    }
    const children = this._fcvs.getObjects()
    if (children.length > 0) {
      this._fcvs.remove(...children)
    }
  }

  private resetTmpText() {
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this
    if (!fdb._tmpText) {
      return
    }
    // 将当前文本对象退出编辑模式
    fdb._tmpText.exitEditing()
    fdb._tmpText.set('backgroundColor', 'rgba(0,0,0,0)')
    // 如果文字对象中内容为空，则清除当前文字
    if (fdb._tmpText.text === '') {
      fdb._fcvs!.remove(fdb._tmpText)
    }
    fdb._fcvs!.renderAll()
    fdb._tmpText = null
  }

  /**
   * 导出画布内容为数据 URL，并通过回调函数返回。
   * @param callback - 回调函数，接收导出的数据 URL 作为参数。
   */
  canvasExport(callback: (dataURL: any) => void) {
    if (this._tmpText) {
      this.resetTmpText()
    }
    // eslint-disable-next-line ts/no-this-alias
    const fdb = this
    if (!fdb._fcvs) {
      return
    }
    fdb._fcvs.clone((cvs: fabric.Canvas) => {
      let top = 0
      let left = 0
      let width = fdb._fcvs!.width
      let height = fdb._fcvs!.height

      const objects = cvs.getObjects()
      if (objects.length > 0) {
        let rect = objects[0]!.getBoundingRect()
        let minX = rect.left
        let minY = rect.top
        let maxX = rect.left + rect.width
        let maxY = rect.top + rect.height

        for (let i = 1; i < objects.length; i++) {
          rect = objects[i]!.getBoundingRect()
          minX = Math.min(minX, rect.left)
          minY = Math.min(minY, rect.top)
          maxX = Math.max(maxX, rect.left + rect.width)
          maxY = Math.max(maxY, rect.top + rect.height)
        }

        top = minY - 100
        left = minX - 100
        width = maxX - minX + 200
        height = maxY - minY + 200

        cvs.sendToBack(new fdb._fabric.Rect({
          left,
          top,
          width,
          height,
          stroke: 'rgba(0,0,0,0)',
          fill: fdb._bgColor,
          strokeWidth: 0,
        }))
      }

      const dataURL = cvs.toDataURL({
        format: 'png',
        multiplier: cvs.getZoom(),
        left,
        top,
        width,
        height,
      })

      if (callback) {
        callback(dataURL)
      }
    })
  }

  /**
   * 设置画笔的颜色。
   * @param color - 要设置的画笔颜色，使用颜色字符串表示。
   */
  setBrushColor(color: string) {
    this._brushColor = color
    if (this._curAction === ACTION_BRUSH) {
      this._fcvs!.freeDrawingBrush.color = this._brushColor
    }
  }

  /**
   * 设置描边的颜色。
   * @param color - 要设置的描边颜色，使用颜色字符串表示。
   */
  setStrokeColor(color: string) {
    this._strokeColor = color
  }

  /**
   * 设置填充颜色。
   * @param color - 要设置的填充颜色，使用颜色字符串表示。
   */
  setFillColor(color: string) {
    this._fillColor = color
  }
}
