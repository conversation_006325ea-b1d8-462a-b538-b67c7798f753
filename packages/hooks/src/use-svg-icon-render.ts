import { h } from 'vue'
import type { Component } from 'vue'

/**
 * SVG图标渲染钩子
 * @param SvgIcon SVG图标组件
 */
export default function useSvgIconRender(SvgIcon: Component) {
  /**
   * 图标配置接口
   */
  interface IconConfig {
    /** Iconify图标名称 */
    icon?: string
    /** 本地图标名称 */
    localIcon?: string
    /** 图标颜色 */
    color?: string
    /** 图标字体大小 */
    fontSize?: number
  }

  /**
   * 图标样式类型
   */
  type IconStyle = Partial<Pick<CSSStyleDeclaration, 'color' | 'fontSize'>>

  /**
   * 创建SVG图标VNode
   * @param config 图标配置
   * @returns 返回VNode或undefined
   */
  const SvgIconVNode = (config: IconConfig) => {
    const { color, fontSize, icon, localIcon } = config

    // 构建样式对象
    const style: IconStyle = {}
    if (color) {
      style.color = color
    }
    if (fontSize) {
      style.fontSize = `${fontSize}px`
    }

    // 必须提供icon或localIcon
    if (!icon && !localIcon) {
      return undefined
    }

    // 创建SVG图标组件
    return () => h(SvgIcon, { icon, localIcon, style })
  }

  return {
    SvgIconVNode, // 导出的SVG图标渲染函数
  }
}
