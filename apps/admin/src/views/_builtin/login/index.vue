<script setup lang="ts">
import { computed } from 'vue'
import type { Component } from 'vue'
import { getPaletteColorByNumber, mixColor } from '@sa/color'
import { useThemeStore } from '@sa/store/modules/theme'
import PwdLogin from './modules/pwd-login.vue'
import CodeLogin from './modules/code-login.vue'
import QrcodeLogin from './modules/qrcode-login.vue'
import ResetPwd from './modules/reset-pwd.vue'

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule
}

const props = defineProps<Props>()

const themeStore = useThemeStore()

interface LoginModule {
  label: string
  component: Component
}

const moduleMap: Record<UnionKey.LoginModule, LoginModule> = {
  'pwd-login': { label: '密码登录', component: PwdLogin },
  'code-login': { label: '验证码登录', component: CodeLogin },
  'qrcode-login': { label: '二维码登录', component: QrcodeLogin },
  'reset-pwd': { label: '重置密码', component: ResetPwd },
}

const activeModule = computed(() => moduleMap[props.module || 'pwd-login'])

const bgThemeColor = computed(() =>
  themeStore.darkMode ? getPaletteColorByNumber(themeStore.themeColor, 600) : themeStore.themeColor,
)

const bgColor = computed(() => {
  const COLOR_WHITE = '#ffffff'

  const ratio = themeStore.darkMode ? 0.5 : 0.2

  return mixColor(COLOR_WHITE, themeStore.themeColor, ratio)
})
const title = import.meta.env.VITE_APP_TITLE
</script>

<template>
  <div class="relative size-full min-h-100vh flex-center overflow-hidden p-4" :style="{ backgroundColor: bgColor }">
    <WaveBg :theme-color="bgThemeColor" />

    <!-- 响应式登录容器 -->
    <div class="z-[1000] max-w-80 w-full flex-center overflow-hidden transition-all duration-300 ease-in-out lg:max-w-[800px] md:max-w-[720px] sm:max-w-[600px] md:px-8 sm:px-8">
      <!-- 左侧装饰面板 - 在移动端隐藏 -->
      <div class="fadeInLeft hidden h-[400px] w-[300px] rounded-l-3xl bg-white shadow-lg transition-all duration-300 ease-in-out sm:block lg:h-[450px] lg:w-[400px] md:h-[400px] md:w-[360px]">
        <div class="flex items-center justify-between p-4 md:p-5">
          <SystemLogo class="rotateIn text-8 text-primary lg:text-[39px] md:text-10" />
          <!-- 头部区域 -->
          <header class="flex items-center justify-between max-sm:flex-col max-sm:gap-2 max-sm:p-3 max-sm:text-center">
            <h3 class="flex-1 text-[26px] text-primary font-medium max-sm:mx-0">
              {{ title }}
            </h3>
          </header>
        </div>
        <img src="@/assets/imgs/login-bg.png" alt="登录背景图">
        <span class="block p-4 text-sm text-gray-600 leading-relaxed">
          以AI+教育重塑学习体验，构建安全、高效、个性化的
          区域终身学习平台，让优质教育普惠每个成长者
        </span>
      </div>

      <!-- 右侧登录面板 -->
      <div class="fadeInRight relative z-[4] h-[400px] w-full rounded-3xl bg-white transition-all duration-300 ease-in-out lg:h-[450px] lg:w-[600px] md:h-[400px] md:w-[360px] sm:w-[300px] sm:rounded-l-none sm:rounded-r-3xl">
        <!-- 主要内容区域 -->
        <main class="px-4 pb-4 max-sm:p-3 lg:px-6 md:px-5">
          <h3 class="mb-3 p-4 text-center text-[20px] text-primary font-medium md:p-5">
            {{ activeModule.label }}
          </h3>
          <div class="overflow-y-auto">
            <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
              <component :is="activeModule.component" />
            </Transition>
          </div>
        </main>
      </div>
    </div>
    <div class="fadeInUp absolute bottom-20px z-[1000] w-full">
      <div class="copyright-container">
        更安心更安心更安心更安心更安心
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 版权信息样式 */
.copyright-container {
  @apply text-center text-white relative w-2/5 mx-auto my-8;
}

.copyright-container::before {
  @apply absolute content-[''] top-1/2 w-1/5 h-px -left-8;
  background-image: linear-gradient(to left, red, transparent);
}

.copyright-container::after {
  @apply absolute content-[''] top-1/2 w-1/5 h-px -right-8;
  background-image: linear-gradient(to right, red, transparent);
}

/* 动画类 */
.fadeInLeft {
  animation: fadeInLeft 1s ease-out both;
}

.fadeInRight {
  animation: fadeInRight 1s ease-out both;
}

.fadeInUp {
  animation: fadeInUp 1s ease-out both;
  animation-delay: 1s;
}

.rotateIn {
  animation: rotateIn 1s ease-out both;
  transform-origin: center;
  animation-delay: 1s;
}
</style>
