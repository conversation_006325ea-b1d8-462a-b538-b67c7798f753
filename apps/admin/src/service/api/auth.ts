import { adminApiRequest, appApiRequest, request } from '@/service/request'

/**
 * 用户登录接口
 * @param userName 用户名
 * @param password 密码
 */
export function fetchLogin(userName: string, password: string) {
  return adminApiRequest<Api.Auth.LoginToken>({
    url: 'Base_Manage/Home/SubmitLogin',
    method: 'POST',
    data: {
      userName,
      password,
      type: 1,
    },
  }).then((res) => {
    if (res.data) {
      return {
        data: {
          token: res.data as unknown as string,
          refreshToken: '',
        },
        error: null,
      }
    }
    return res
  })
}

/**
 * 获取用户信息接口
 * @returns 返回用户信息
 */
export async function fetchGetUserInfo() {
  return adminApiRequest<AuthApi.UserInfo>({
    url: 'Base_Manage/Base_User/GetUserInfoByUserName',
    method: 'POST',
  }).then((res) => {
    if (res.error) {
      return res
    }
    const data = res.data as any
    return {
      error: null,
      data: {
        userName: data.User?.UserName,
        userId: data.User?.Id,
        RoleIdList: (data.Roles ?? []).map((item: any) => ({
          RoleId: item.Id,
          RoleName: item.RoleName,
          SchoolInfo: [],
        })),
        subjectList: [],
        schoolId: data.User?.SchoolId,
      } as unknown as AuthApi.UserInfo,
    }
  })
}

/**
 * 获取教师学科信息
 */
export async function fetchSubjectListByUser(UserId: string) {
  return appApiRequest<AuthApi.Subject[]>({
    url: 'Period_Subject/Period_Subject/GetSubjectListByUser',
    method: 'POST',
    data: {
      UserId,
    },
  })
}

/**
 * 模拟后端错误接口
 * @param code 错误码
 * @param msg 错误信息
 * @returns 返回错误响应
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } })
}

/**
 * 刷新token接口
 * @param refreshToken 刷新token
 * @returns 返回新的token信息
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken,
    },
  })
}
