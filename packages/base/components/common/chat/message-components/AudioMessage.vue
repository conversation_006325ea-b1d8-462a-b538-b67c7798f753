<script setup lang="ts">
defineOptions({
  name: 'AudioMessage',
})

const props = defineProps<{
  /** 消息项 */
  item: Chat.MessageItem
}>()

// 音频播放状态管理 - 使用 string 类型的 key
const playingAudioKeys = ref<Set<string>>(new Set())
const audioElements = ref<Map<string, HTMLAudioElement>>(new Map())

// 检查音频是否正在播放
function isPlaying(key: string): boolean {
  return playingAudioKeys.value.has(key)
}

// 获取音频 URL
function getAudioUrl(audioData: Chat.AudioMessageData): string | null {
  // 优先使用 audioUrl，这是符合类型定义的标准属性
  if (audioData.audioUrl) {
    return audioData.audioUrl
  }

  // 如果没有 audioUrl，检查是否有其他可用的音频源
  // 注意：audioBlob 在类型定义中被注释掉了，但可能在某些情况下仍然存在
  const audioBlob = (audioData as any).audioBlob
  if (audioBlob instanceof Blob) {
    return URL.createObjectURL(audioBlob)
  }

  return null
}

// 切换音频播放状态
async function toggleAudioPlay(item: Chat.MessageItem) {
  // 类型安全检查
  if (!item.audioData) {
    console.warn('音频数据不存在')
    return
  }

  const audioUrl = getAudioUrl(item.audioData)
  if (!audioUrl) {
    console.warn('无法获取音频 URL')
    window.$message?.warning('音频文件不可用')
    return
  }

  const key = item.key
  const isCurrentlyPlaying = isPlaying(key)

  // 停止其他正在播放的音频
  for (const [audioKey, audio] of audioElements.value) {
    if (audioKey !== key && !audio.paused) {
      audio.pause()
      audio.currentTime = 0
      playingAudioKeys.value.delete(audioKey)
    }
  }

  if (isCurrentlyPlaying) {
    // 停止当前音频
    const audio = audioElements.value.get(key)
    if (audio) {
      audio.pause()
      audio.currentTime = 0
      playingAudioKeys.value.delete(key)
    }
  }
  else {
    // 播放音频
    try {
      let audio = audioElements.value.get(key)
      if (!audio) {
        // 创建新的音频元素
        audio = new Audio(audioUrl)
        audioElements.value.set(key, audio)

        // 监听音频结束事件
        audio.addEventListener('ended', () => {
          playingAudioKeys.value.delete(key)
        })

        // 监听音频错误事件
        audio.addEventListener('error', (event) => {
          playingAudioKeys.value.delete(key)
          console.error('音频播放错误:', event)
          window.$message?.error('音频播放失败')
        })

        // 监听音频加载开始
        audio.addEventListener('loadstart', () => {
          // 可以在这里添加加载状态提示
        })
      }

      playingAudioKeys.value.add(key)
      await audio.play()
    }
    catch (error) {
      playingAudioKeys.value.delete(key)
      console.error('音频播放失败:', error)

      // 提供更详细的错误信息
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          window.$message?.error('音频播放被阻止，请检查浏览器设置')
        }
        else if (error.name === 'NotSupportedError') {
          window.$message?.error('不支持的音频格式')
        }
        else {
          window.$message?.error(`音频播放失败: ${error.message}`)
        }
      }
      else {
        window.$message?.error('音频播放失败')
      }
    }
  }
}

// 格式化音频时长显示
function formatDuration(duration: number): string {
  if (!duration || duration <= 0) {
    return '0'
  }

  // 如果时长小于 1 秒，显示为 1 秒
  if (duration < 1) {
    return '1'
  }

  // 四舍五入到整数
  return Math.round(duration).toString()
}

// 获取音频格式显示文本
function getAudioFormatDisplay(mimeType: string): string {
  const formatMap: Record<string, string> = {
    'audio/wav': 'WAV',
    'audio/mp3': 'MP3',
    'audio/mpeg': 'MP3',
    'audio/webm': 'WEBM',
    'audio/ogg': 'OGG',
    'audio/aac': 'AAC',
    'audio/m4a': 'M4A',
  }

  return formatMap[mimeType] || mimeType.replace('audio/', '').toUpperCase()
}

// 组件卸载时清理音频资源
onUnmounted(() => {
  for (const [, audio] of audioElements.value) {
    audio.pause()
    audio.src = ''
    // 释放 blob URL 以避免内存泄漏
    if (audio.src.startsWith('blob:')) {
      URL.revokeObjectURL(audio.src)
    }
  }
  audioElements.value.clear()
  playingAudioKeys.value.clear()
})
</script>

<template>
  <div v-if="props.item.audioData" class="audio-message">
    <div
      class="wechat-audio-player"
      :class="{
        'is-playing': isPlaying(item.key),
        'is-user': item.role === 'user',
      }"
      @click="toggleAudioPlay(item)"
    >
      <!-- 播放按钮 -->
      <div class="audio-play-btn">
        <SvgIcon
          :icon="isPlaying(item.key) ? 'mdi:pause' : 'mdi:play'"
          :size="16"
          class="play-icon"
        />
      </div>

      <!-- 音频波形动画 -->
      <div class="audio-waveform">
        <div
          v-for="i in 5"
          :key="i"
          class="wave-bar"
          :class="{ animate: isPlaying(item.key) }"
          :style="{ animationDelay: `${(i - 1) * 0.1}s` }"
        />
      </div>

      <!-- 音频时长 -->
      <div class="audio-duration">
        {{ formatDuration(props.item.audioData.duration) }}s
      </div>

      <!-- 音频格式信息（可选显示） -->
      <div
        v-if="props.item.audioData.mimeType && props.item.audioData.mimeType !== 'audio/wav'"
        class="audio-format"
        :title="`音频格式: ${props.item.audioData.mimeType}`"
      >
        {{ getAudioFormatDisplay(props.item.audioData.mimeType) }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.audio-message {
  .wechat-audio-player {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    max-width: 200px;
    position: relative;

    // 用户消息样式（使用系统主题色）
    &.is-user {
      background: rgb(var(--primary-color));
      color: #fff;

      .audio-play-btn {
        background: rgba(255, 255, 255, 0.2);
        color: #fff;
      }

      .wave-bar {
        background: rgba(255, 255, 255, 0.8);
      }

      .audio-duration {
        color: rgba(255, 255, 255, 0.9);
      }
    }

    // 播放按钮
    .audio-play-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      flex-shrink: 0;
      transition: all 0.2s ease;

      .play-icon {
        transition: transform 0.2s ease;
      }
    }

    // 音频波形动画
    .audio-waveform {
      display: flex;
      align-items: center;
      gap: 2px;
      flex: 1;
      height: 16px;

      .wave-bar {
        width: 2px;
        height: 4px;
        border-radius: 1px;
        transition: all 0.2s ease;

        &.animate {
          animation: waveAnimation 1.2s ease-in-out infinite;
        }

        &:nth-child(2) {
          animation-delay: 0.1s;
        }

        &:nth-child(3) {
          animation-delay: 0.2s;
        }
      }
    }

    // 音频时长
    .audio-duration {
      font-size: 12px;
      font-weight: 500;
      flex-shrink: 0;
      min-width: 20px;
      text-align: right;
    }

    // 音频格式标签
    .audio-format {
      font-size: 10px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.5);
      background-color: rgba(0, 0, 0, 0.05);
      padding: 1px 4px;
      border-radius: 4px;
      margin-left: 4px;
      flex-shrink: 0;

      // 用户消息中的格式标签样式
      .is-user & {
        color: rgba(255, 255, 255, 0.8);
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    // 播放状态
    &.is-playing {
      .audio-play-btn {
        transform: scale(1.1);

        .play-icon {
          transform: scale(0.9);
        }
      }
    }

    // 悬停效果
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// 波形动画关键帧
@keyframes waveAnimation {
  0%, 100% {
    height: 4px;
    opacity: 0.6;
  }
  50% {
    height: 12px;
    opacity: 1;
  }
}
</style>
