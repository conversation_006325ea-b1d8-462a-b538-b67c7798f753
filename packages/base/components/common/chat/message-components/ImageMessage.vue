<script setup lang="ts">
import { NImage, NImageGroup } from 'naive-ui'

defineOptions({
  name: 'ImageMessage',
})

defineProps<{
  /** 消息项 */
  item: Chat.MessageItem
}>()

// 获取图片网格样式类
function getImageGridClass(count: number): string {
  if (count === 1)
    return 'grid-cols-1'
  if (count === 2)
    return 'grid-cols-2'
  if (count <= 4)
    return 'grid-cols-2'
  if (count <= 6)
    return 'grid-cols-3'
  return 'grid-cols-3'
}
</script>

<template>
  <div v-if="item.contentType === 'image'" class="image-message">
    <!-- 单张图片 -->
    <div v-if="item.imageData" class="single-image">
      <NImage
        :src="item.imageData.imageUrl"
        :preview-src="item.imageData.imageUrl"
        :width="Math.min(item.imageData.width || 200, 300)"
        :height="Math.min(item.imageData.height || 150, 200)"
        object-fit="cover"
        class="message-image"
        preview
      />
    </div>
    <!-- 多张图片 -->
    <div v-if="item.images && item.images.length > 0" class="multiple-images">
      <NImageGroup>
        <div class="images-grid" :class="getImageGridClass(item.images.length)">
          <NImage
            v-for="(image, index) in item.images"
            :key="index"
            :src="image.imageUrl"
            :preview-src="image.imageUrl"
            width="120"
            height="120"
            object-fit="cover"
            class="grid-image"
            preview
          />
        </div>
      </NImageGroup>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 图片消息样式
.image-message {
  .single-image {
    .message-image {
      border-radius: 8px;
      cursor: pointer;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.02);
      }
    }
  }

  .multiple-images {
    .images-grid {
      display: grid;
      gap: 4px;
      max-width: 300px;

      &.grid-cols-1 {
        grid-template-columns: 1fr;
      }

      &.grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
      }

      &.grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      .grid-image {
        border-radius: 6px;
        cursor: pointer;
        transition: transform 0.2s;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}
</style>
