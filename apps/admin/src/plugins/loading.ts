// @unocss-include
import { getRgb } from '@sa/color'
import { localStg, toggleHtmlClass } from '@sa/utils'
import { DARK_CLASS } from '@sa/constants/app'
import systemLogo from '@/assets/svg-icon/logo.svg?raw'

/**
 * 设置应用加载动画
 * @description 初始化应用加载时的动画效果
 * 1. 获取主题颜色和暗黑模式设置
 * 2. 根据主题色生成CSS变量
 * 3. 设置暗黑模式类名
 * 4. 构建加载动画DOM结构
 * 5. 将加载动画插入到应用容器中
 */
export function setupLoading() {
  // 从本地存储获取主题颜色，默认使用#646cff
  const themeColor = localStg.get('themeColor') || '#646cff'
  // 从本地存储获取暗黑模式设置，默认关闭
  const darkMode = localStg.get('darkMode') || false
  // 解析主题色RGB值
  const { r, g, b } = getRgb(themeColor)

  // 生成CSS变量字符串
  const primaryColor = `--primary-color: ${r} ${g} ${b}`

  // 如果启用暗黑模式，添加暗黑类名
  if (darkMode) {
    toggleHtmlClass(DARK_CLASS).add()
  }

  // 定义加载动画点的位置和动画延迟
  const loadingClasses = [
    'left-0 top-0',
    'left-0 bottom-0 animate-delay-500',
    'right-0 top-0 animate-delay-1000',
    'right-0 bottom-0 animate-delay-1500',
  ]

  // 给logo SVG添加样式类
  const logoWithClass = systemLogo.replace('<svg', `<svg class="size-128px text-primary"`)

  // 生成四个动画点元素
  const dot = loadingClasses
    .map((item) => {
      return `<div class="absolute w-16px h-16px bg-primary rounded-8px animate-pulse ${item}"></div>`
    })
    .join('\n')

  // 构建完整的加载动画HTML
  const loading = `
<div class="fixed-center flex-col bg-layout" style="${primaryColor}">
  ${logoWithClass}
  <div class="w-56px h-56px my-36px">
    <div class="relative h-full animate-spin">
      ${dot}
    </div>
  </div>
  <h2 class="text-28px font-500 text-primary">${import.meta.env.VITE_APP_TITLE}</h2>
</div>`

  // 获取应用容器元素
  const app = document.getElementById('app')

  // 如果容器存在，插入加载动画
  if (app) {
    app.innerHTML = loading
  }
}
