<script setup lang="tsx">
import { computed, ref, watch } from 'vue'
import type { SelectOption } from 'naive-ui'
import { useFormRules, useNaiveForm } from '@sa/hooks'
import { getLocalIcons } from '@sa/utils'
import SvgIcon from '@sa/components/custom/svg-icon.vue'
import { enableStatusOptions, menuIconTypeOptions, menuTypeOptions } from '@sa/constants/business'
import {
  getLayoutAndPage,
  getPathParamFromRoutePath,
  getRoutePathByRouteName,
  getRoutePathWithParam,
  transformLayoutAndPageToComponent,
} from './shared'
import { fetchGetAllRoles } from '@/service/api'

defineOptions({
  name: 'MenuOperateModal',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

export type OperateType = NaiveUI.TableOperateType | 'addChild'

interface Props {
  /** the type of operation */
  operateType: OperateType
  /** the edit menu data or the parent menu data when adding a child menu */
  rowData?: Api.SystemManage.Menu | null
  /** all pages */
  allPages: string[]
}

interface Emits {
  (e: 'submitted'): void
}

const visible = defineModel<boolean>('visible', {
  default: false,
})

const { formRef, validate, restoreValidation } = useNaiveForm()
const { defaultRequiredRule } = useFormRules()

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: '新增菜单',
    addChild: '新增子菜单',
    edit: '编辑菜单',
  }
  return titles[props.operateType]
})

type Model = Pick<
  Api.SystemManage.Menu,
  | 'menuType'
  | 'menuName'
  | 'routeName'
  | 'routePath'
  | 'component'
  | 'order'
  | 'icon'
  | 'iconType'
  | 'status'
  | 'parentId'
  | 'keepAlive'
  | 'constant'
  | 'href'
  | 'hideInMenu'
  | 'activeMenu'
  | 'multiTab'
  | 'fixedIndexInTab'
> & {
  query: NonNullable<Api.SystemManage.Menu['query']>
  buttons: NonNullable<Api.SystemManage.Menu['buttons']>
  layout: string
  page: string
  pathParam: string
}

const model = ref(createDefaultModel())

function createDefaultModel(): Model {
  return {
    menuType: '1',
    menuName: '',
    routeName: '',
    routePath: '',
    pathParam: '',
    component: '',
    layout: '',
    page: '',
    icon: '',
    iconType: '1',
    parentId: 0,
    status: '1',
    keepAlive: false,
    constant: false,
    order: 0,
    href: null,
    hideInMenu: false,
    activeMenu: null,
    multiTab: false,
    fixedIndexInTab: null,
    query: [],
    buttons: [],
  }
}

type RuleKey = Extract<keyof Model, 'menuName' | 'status' | 'routeName' | 'routePath'>

const rules: Record<RuleKey, App.Global.FormRule> = {
  menuName: defaultRequiredRule,
  status: defaultRequiredRule,
  routeName: defaultRequiredRule,
  routePath: defaultRequiredRule,
}

const disabledMenuType = computed(() => props.operateType === 'edit')

const localIcons = getLocalIcons()
const localIconOptions = localIcons.map<SelectOption>(item => ({
  label: () => (
    <div class="flex-y-center gap-16px">
      <SvgIcon localIcon={item} class="text-icon" />
      <span>{item}</span>
    </div>
  ),
  value: item,
}))

const showLayout = computed(() => model.value.parentId === 0)

const showPage = computed(() => model.value.menuType === '2')

const pageOptions = computed(() => {
  const allPages = [...props.allPages]

  if (model.value.routeName && !allPages.includes(model.value.routeName)) {
    allPages.unshift(model.value.routeName)
  }

  const opts: CommonType.Option[] = allPages.map(page => ({
    label: page,
    value: page,
  }))

  return opts
})

const layoutOptions: CommonType.Option[] = [
  {
    label: 'base',
    value: 'base',
  },
  {
    label: 'blank',
    value: 'blank',
  },
]

/** the enabled role options */
const roleOptions = ref<CommonType.Option<string>[]>([])

async function getRoleOptions() {
  const { error, data } = await fetchGetAllRoles()

  if (!error) {
    const options = data.map(item => ({
      label: item.roleName,
      value: item.roleCode,
    }))

    roleOptions.value = [...options]
  }
}

function handleInitModel() {
  model.value = createDefaultModel()

  if (!props.rowData)
    return

  if (props.operateType === 'addChild') {
    const { id } = props.rowData

    Object.assign(model.value, { parentId: id })
  }

  if (props.operateType === 'edit') {
    const { component, ...rest } = props.rowData

    const { layout, page } = getLayoutAndPage(component)
    const { path, param } = getPathParamFromRoutePath(rest.routePath)

    Object.assign(model.value, rest, { layout, page, routePath: path, pathParam: param })
  }

  if (!model.value.query) {
    model.value.query = []
  }
  if (!model.value.buttons) {
    model.value.buttons = []
  }
}

function closeDrawer() {
  visible.value = false
}

function handleUpdateRoutePathByRouteName() {
  if (model.value.routeName) {
    model.value.routePath = getRoutePathByRouteName(model.value.routeName)
  }
  else {
    model.value.routePath = ''
  }
}

function handleCreateButton() {
  const buttonItem: Api.SystemManage.MenuButton = {
    code: '',
    desc: '',
  }

  return buttonItem
}

function getSubmitParams() {
  const { layout, page, pathParam, ...params } = model.value

  const component = transformLayoutAndPageToComponent(layout, page)
  const routePath = getRoutePathWithParam(model.value.routePath, pathParam)

  params.component = component
  params.routePath = routePath

  return params
}

async function handleSubmit() {
  await validate()

  const params = getSubmitParams()

  console.log('params: ', params)

  // request
  window.$message?.success('更新成功')
  closeDrawer()
  emit('submitted')
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel()
    restoreValidation()
    getRoleOptions()
  }
})

watch(
  () => model.value.routeName,
  () => {
    handleUpdateRoutePathByRouteName()
  },
)
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-800px">
    <NScrollbar class="h-480px pr-20px">
      <NForm
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="left"
        :label-width="100"
      >
        <NGrid responsive="screen" item-responsive>
          <NFormItemGi span="24 m:12" label="菜单类型" path="menuType">
            <NRadioGroup v-model:value="model.menuType" :disabled="disabledMenuType">
              <NRadio v-for="item in menuTypeOptions" :key="item.value" :value="item.value" :label="item.label" />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="菜单名称" path="menuName">
            <NInput v-model:value="model.menuName" placeholder="请输入菜单名称" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="路由名称" path="routeName">
            <NInput v-model:value="model.routeName" placeholder="请输入路由名称" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="路由路径" path="routePath">
            <NInput v-model:value="model.routePath" disabled placeholder="请输入路由路径" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="路径参数" path="pathParam">
            <NInput v-model:value="model.pathParam" placeholder="请输入路径参数" />
          </NFormItemGi>
          <NFormItemGi v-if="showLayout" span="24 m:12" label="布局" path="layout">
            <NSelect
              v-model:value="model.layout"
              :options="layoutOptions"
              placeholder="请选择布局组件"
            />
          </NFormItemGi>
          <NFormItemGi v-if="showPage" span="24 m:12" label="页面组件" path="page">
            <NSelect
              v-model:value="model.page"
              :options="pageOptions"
              placeholder="请选择页面组件"
            />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="排序" path="order">
            <NInputNumber v-model:value="model.order" class="w-full" placeholder="请输入排序" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="图标类型" path="iconType">
            <NRadioGroup v-model:value="model.iconType">
              <NRadio
                v-for="item in menuIconTypeOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="图标" path="icon">
            <template v-if="model.iconType === '1'">
              <NInput v-model:value="model.icon" placeholder="请输入图标" class="flex-1">
                <template #suffix>
                  <SvgIcon v-if="model.icon" :icon="model.icon" class="text-icon" />
                </template>
              </NInput>
            </template>
            <template v-if="model.iconType === '2'">
              <NSelect
                v-model:value="model.icon"
                placeholder="请选择本地图标"
                :options="localIconOptions"
              />
            </template>
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="请选择菜单状态" path="status">
            <NRadioGroup v-model:value="model.status">
              <NRadio
                v-for="item in enableStatusOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="缓存路由" path="keepAlive">
            <NRadioGroup v-model:value="model.keepAlive">
              <NRadio :value="true" label="是" />
              <NRadio :value="false" label="否" />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="常量路由" path="constant">
            <NRadioGroup v-model:value="model.constant">
              <NRadio :value="true" label="是" />
              <NRadio :value="false" label="否" />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="外链" path="href">
            <NInput v-model:value="model.href" placeholder="请输入外链" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="隐藏菜单" path="hideInMenu">
            <NRadioGroup v-model:value="model.hideInMenu">
              <NRadio :value="true" label="是" />
              <NRadio :value="false" label="否" />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi
            v-if="model.hideInMenu"
            span="24 m:12"
            label="高亮的菜单"
            path="activeMenu"
          >
            <NSelect
              v-model:value="model.activeMenu"
              :options="pageOptions"
              clearable
              placeholder="请选择高亮的菜单的路由名称"
            />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="支持多页签" path="multiTab">
            <NRadioGroup v-model:value="model.multiTab">
              <NRadio :value="true" label="是" />
              <NRadio :value="false" label="否" />
            </NRadioGroup>
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="固定在页签中的序号" path="fixedIndexInTab">
            <NInputNumber
              v-model:value="model.fixedIndexInTab"
              class="w-full"
              clearable
              placeholder="请输入固定在页签中的序号"
            />
          </NFormItemGi>
          <NFormItemGi span="24" label="路由参数">
            <NDynamicInput
              v-model:value="model.query"
              preset="pair"
              key-placeholder="请输入路由参数Key"
              value-placeholder="请输入路由参数Value"
            >
              <template #action="{ index, create, remove }">
                <NSpace class="ml-12px">
                  <NButton size="medium" @click="() => create(index)">
                    <icon-ic:round-plus class="text-icon" />
                  </NButton>
                  <NButton size="medium" @click="() => remove(index)">
                    <icon-ic-round-remove class="text-icon" />
                  </NButton>
                </NSpace>
              </template>
            </NDynamicInput>
          </NFormItemGi>
          <NFormItemGi span="24" label="按钮">
            <NDynamicInput v-model:value="model.buttons" :on-create="handleCreateButton">
              <template #default="{ value }">
                <div class="ml-8px flex-y-center flex-1 gap-12px">
                  <NInput
                    v-model:value="value.code"
                    placeholder="请输入按钮编码"
                    class="flex-1"
                  />
                  <NInput
                    v-model:value="value.desc"
                    placeholder="请输入按钮描述"
                    class="flex-1"
                  />
                </div>
              </template>
              <template #action="{ index, create, remove }">
                <NSpace class="ml-12px">
                  <NButton size="medium" @click="() => create(index)">
                    <icon-ic:round-plus class="text-icon" />
                  </NButton>
                  <NButton size="medium" @click="() => remove(index)">
                    <icon-ic-round-remove class="text-icon" />
                  </NButton>
                </NSpace>
              </template>
            </NDynamicInput>
          </NFormItemGi>
        </NGrid>
      </NForm>
    </NScrollbar>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="closeDrawer">
          取消
        </NButton>
        <NButton type="primary" @click="handleSubmit">
          确认
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
