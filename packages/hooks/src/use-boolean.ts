import { ref } from 'vue'

/**
 * 布尔值状态钩子
 * @param initValue 初始值，默认为false
 */
export default function useBoolean(initValue = false) {
  const bool = ref(initValue)

  // 设置布尔值
  function setBool(value: boolean) {
    bool.value = value
  }
  // 设为true
  function setTrue() {
    setBool(true)
  }
  // 设为false
  function setFalse() {
    setBool(false)
  }
  // 切换布尔值
  function toggle() {
    setBool(!bool.value)
  }

  return {
    bool,
    setBool,
    setTrue,
    setFalse,
    toggle,
  }
}
