<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useBoolean, useFormRules, useNaiveForm } from '@sa/hooks'
import { enableStatusOptions } from '@sa/constants/business'
import MenuAuthModal from './menu-auth-modal.vue'
import ButtonAuthModal from './button-auth-modal.vue'

defineOptions({
  name: 'RoleOperateDrawer',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType
  /** the edit row data */
  rowData?: Api.SystemManage.Role | null
}

interface Emits {
  (e: 'submitted'): void
}

const visible = defineModel<boolean>('visible', {
  default: false,
})

const { formRef, validate, restoreValidation } = useNaiveForm()
const { defaultRequiredRule } = useFormRules()
const { bool: menuAuthVisible, setTrue: openMenuAuthModal } = useBoolean()
const { bool: buttonAuthVisible, setTrue: openButtonAuthModal } = useBoolean()

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增角色',
    edit: '编辑角色',
  }
  return titles[props.operateType]
})

type Model = Pick<Api.SystemManage.Role, 'roleName' | 'roleCode' | 'roleDesc' | 'status'>

const model = ref(createDefaultModel())

function createDefaultModel(): Model {
  return {
    roleName: '',
    roleCode: '',
    roleDesc: '',
    status: null,
  }
}

type RuleKey = Exclude<keyof Model, 'roleDesc'>

const rules: Record<RuleKey, App.Global.FormRule> = {
  roleName: defaultRequiredRule,
  roleCode: defaultRequiredRule,
  status: defaultRequiredRule,
}

const roleId = computed(() => props.rowData?.id || -1)

const isEdit = computed(() => props.operateType === 'edit')

function handleInitModel() {
  model.value = createDefaultModel()

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData)
  }
}

function closeDrawer() {
  visible.value = false
}

async function handleSubmit() {
  await validate()
  // request
  window.$message?.success('更新成功')
  closeDrawer()
  emit('submitted')
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel()
    restoreValidation()
  }
})
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="角色名称" path="roleName">
          <NInput v-model:value="model.roleName" placeholder="请输入角色名称" />
        </NFormItem>
        <NFormItem label="角色编码" path="roleCode">
          <NInput v-model:value="model.roleCode" placeholder="请输入角色编码" />
        </NFormItem>
        <NFormItem label="角色状态" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio v-for="item in enableStatusOptions" :key="item.value" :value="item.value" :label="item.label" />
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="角色描述" path="roleDesc">
          <NInput v-model:value="model.roleDesc" placeholder="请输入角色描述" />
        </NFormItem>
      </NForm>
      <NSpace v-if="isEdit">
        <NButton @click="openMenuAuthModal">
          菜单权限
        </NButton>
        <MenuAuthModal v-model:visible="menuAuthVisible" :role-id="roleId" />
        <NButton @click="openButtonAuthModal">
          按钮权限
        </NButton>
        <ButtonAuthModal v-model:visible="buttonAuthVisible" :role-id="roleId" />
      </NSpace>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">
            取消
          </NButton>
          <NButton type="primary" @click="handleSubmit">
            确认
          </NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
