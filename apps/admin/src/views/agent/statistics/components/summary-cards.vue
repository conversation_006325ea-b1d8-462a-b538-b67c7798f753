<script setup lang="ts">
defineOptions({
  name: 'SummaryCards',
})

const props = defineProps<Props>()

interface SummaryData {
  submissionCount: number
  averageScore: string
}

interface Props {
  /** 综合分析数据 */
  summaryData: SummaryData
}

// 卡片配置
const cardConfigs: Array<{
  key: keyof SummaryData
  title: string
  icon: string
  iconBgClass: string
  iconClass: string
  suffix: string
}> = [
  {
    key: 'submissionCount',
    title: '提交人数',
    icon: 'ph:check-circle',
    iconBgClass: 'bg-green-100 dark:bg-green-900',
    iconClass: 'text-green-600 dark:text-green-300',
    suffix: '人',
  },
  {
    key: 'averageScore',
    title: '平均得分',
    icon: 'ph:star-fill',
    iconBgClass: 'bg-yellow-100 dark:bg-yellow-900',
    iconClass: 'text-yellow-600 dark:text-yellow-400',
    suffix: '分',
  },
]
</script>

<template>
  <div>
    <!-- 响应式网格布局 -->
    <NGrid responsive="screen" :x-gap="12" :y-gap="12" item-responsive>
      <!-- 动态渲染统计卡片 -->
      <NGridItem
        v-for="(config, index) in cardConfigs"
        :key="config.key"
        span="24 s:12 m:6 l:6"
        :style="{ animationDelay: `${index * 100}ms` }"
      >
        <div class="stat-card s:p-16px h-full border border-gray-200 rounded-8px bg-white p-12px transition-all duration-300 dark:border-gray-700 hover:border-blue-300 dark:bg-gray-800 hover:shadow-lg dark:hover:border-blue-600">
          <div class="flex items-center justify-between gap-8px">
            <!-- 数据内容 -->
            <div class="min-w-0 flex-1">
              <div class="mb-4px text-16px text-gray-600 font-medium dark:text-gray-400">
                {{ config.title }}
              </div>
              <div class="flex items-baseline gap-2px">
                <div
                  v-if="typeof props.summaryData[config.key] !== 'number'"
                  class="s:text-24px l:text-32px text-20px text-gray-900 font-bold dark:text-gray-100"
                >
                  {{ props.summaryData[config.key] }}
                </div>
                <CountTo
                  v-else
                  :end-value="props.summaryData[config.key] as number"
                  :duration="1200"
                  class="s:text-24px l:text-32px text-20px text-gray-900 font-bold dark:text-gray-100"
                  :suffix="config.suffix"
                />
              </div>
            </div>

            <!-- 图标 -->
            <div
              class="s:h-48px s:w-48px l:h-56px l:w-56px s:rounded-12px h-40px w-40px flex flex-shrink-0 items-center justify-center rounded-8px"
              :class="config.iconBgClass"
            >
              <SvgIcon
                :icon="config.icon"
                class="s:text-20px l:text-24px text-18px"
                :class="config.iconClass"
              />
            </div>
          </div>
        </div>
      </NGridItem>
    </NGrid>
  </div>
</template>

<style scoped>
/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 统计卡片样式 */
.stat-card {
  animation: fadeInUp 0.6s ease-out both;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card:hover {
  transform: translateY(-4px);
}

/* 数字动画增强 */
:deep(.count-to) {
  font-variant-numeric: tabular-nums;
}

/* 图标容器动画 */
.stat-card:hover .flex-shrink-0 {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}
</style>
