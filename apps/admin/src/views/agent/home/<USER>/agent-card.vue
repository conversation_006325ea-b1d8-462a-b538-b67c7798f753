<script setup lang="ts">
import { computed } from 'vue'

defineOptions({
  name: 'AgentCard',
})

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
})

const emit = defineEmits<Emits>()

interface Agent extends AgentApi.AgentTeacherHomePageAgentInfo {}

interface Props {
  /** Agent数据 */
  agent: Agent
  /** 卡片尺寸 */
  size?: 'small' | 'medium' | 'large'
}

interface Emits {
  /** 点击卡片 */
  (e: 'click', agent: Agent): void
  /** 切换收藏状态 */
  (e: 'toggleStar', agent: Agent): void
  /** 立即使用 */
  (e: 'useNow', agent: Agent): void
}

// 星标按钮样式
const starButtonClass = computed(() => {
  const colorClass = props.agent.IsCollection
    ? 'text-yellow-500'
    : 'text-gray-400 hover:text-yellow-500'
  return `${colorClass}`
})

// 处理收藏切换
function handleToggleStar(event: Event) {
  event.stopPropagation()
  emit('toggleStar', props.agent)
}

// 处理立即使用
function handleUseNow(event: Event) {
  event.stopPropagation()
  emit('useNow', props.agent)
}

// 处理卡片点击
function handleCardClick() {
  emit('click', props.agent)
}
</script>

<template>
  <NCard
    hoverable
    class="h-180px cursor-pointer transition-all duration-300 hover:translate-y-[-2px] hover:shadow-lg"
    @click="handleCardClick"
  >
    <!-- 收藏按钮 -->
    <div class="absolute right-12px top-12px z-10" @click="handleToggleStar">
      <SvgIcon :class="starButtonClass" :icon="agent.IsCollection ? 'ph:star-fill' : 'ph:star'" />
    </div>

    <!-- 头像和标题区域 -->
    <div class="s:gap-8px mb-12px flex items-start gap-12px">
      <!-- 头像 -->
      <div class="flex-shrink-0">
        <NImage
          :src="agent.Logo"
          class="s:h-40px s:w-40px h-48px w-48px rounded-8px object-cover"
        />
      </div>

      <!-- 标题和描述 -->
      <div class="min-w-0 flex-1 break-all">
        <h3 class="s:mb-6px s:text-14px mb-8px text-16px text-gray-900 font-semibold dark:text-gray-100">
          {{ agent.AgentName }}
        </h3>
        <p class="s:text-12px s:leading-16px line-clamp-3 text-14px text-gray-600 leading-20px dark:text-gray-300">
          {{ agent.Summarize }}
        </p>
      </div>
    </div>

    <!-- 底部信息和操作区域 -->
    <div class="s:bottom-12px s:left-12px s:right-12px absolute bottom-16px left-16px right-16px">
      <!-- 统计信息和操作按钮区域 -->
      <div class="flex items-center justify-between">
        <!-- 左侧统计信息 -->
        <div class="flex items-center gap-12px">
          <!-- 点赞数 -->
          <div v-if="false" class="flex items-center gap-4px text-gray-500 dark:text-gray-400">
            <SvgIcon icon="ph:star" class="s:text-12px text-14px" />
            <span class="s:text-12px text-14px" />
          </div>
          <!-- 查看数 -->
          <div v-if="false" class="flex items-center gap-4px text-gray-500 dark:text-gray-400">
            <SvgIcon icon="ph:user" class="s:text-12px text-14px" />
            <span class="s:text-12px text-14px" />
          </div>
        </div>

        <!-- 右侧立即使用按钮 -->
        <NButton
          type="primary"
          size="tiny"
          text
          class="s:text-12px s:px-8px px-12px"
          @click="handleUseNow"
        >
          立即使用
        </NButton>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
/* 文本截断样式 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
