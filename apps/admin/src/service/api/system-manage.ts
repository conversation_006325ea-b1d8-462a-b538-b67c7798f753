import { request } from '@/service/request'

/**
 * 获取角色列表
 * @param params 可选查询参数
 * @returns 返回角色列表数据
 */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/systemManage/getRoleList',
    method: 'get',
    params,
  })
}

/**
 * 获取所有可用角色
 * @description 仅返回已启用的角色列表
 * @returns 返回所有可用角色数组
 */
export function fetchGetAllRoles() {
  return request<Api.SystemManage.AllRole[]>({
    url: '/systemManage/getAllRoles',
    method: 'get',
  })
}

/**
 * 获取用户列表
 * @param params 可选查询参数
 * @returns 返回用户列表数据
 */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: '/systemManage/getUserList',
    method: 'get',
    params,
  })
}

/**
 * 获取菜单列表
 * @returns 返回菜单列表数据
 */
export function fetchGetMenuList() {
  return request<Api.SystemManage.MenuList>({
    url: '/systemManage/getMenuList/v2',
    method: 'get',
  })
}

/**
 * 获取所有页面路由
 * @returns 返回所有页面路由名称数组
 */
export function fetchGetAllPages() {
  return request<string[]>({
    url: '/systemManage/getAllPages',
    method: 'get',
  })
}

/**
 * 获取菜单树形结构
 * @returns 返回菜单树形结构数据
 */
export function fetchGetMenuTree() {
  return request<Api.SystemManage.MenuTree[]>({
    url: '/systemManage/getMenuTree',
    method: 'get',
  })
}
