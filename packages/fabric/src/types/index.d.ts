import FabricDrawingBoard from '../index'

export interface DrawingBoardOptions {
  canvasId?: string
  el?: HTMLCanvasElement | null
  brushColor?: string
  strokeColor?: string
  fillColor?: string
  bgColor?: string
  textEditBgColor?: string
  brushSize?: number
  strokeSize?: number
  eraserSize?: number
  fontSize?: number
}

export interface MousePosition {
  x: number
  y: number
}

// 扩展fabric库的类型定义
declare module 'fabric' {
  namespace fabric {
    interface Canvas {
      _offset: { left: number, top: number }
      viewportTransform: number[]
    }

    class EraserBrush extends PencilBrush {
      constructor(canvas: fabric.Canvas)
    }
  }
}

export { FabricDrawingBoard as default }
