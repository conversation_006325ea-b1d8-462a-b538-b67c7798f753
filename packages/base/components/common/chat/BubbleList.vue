<script setup lang="ts">
import { BubbleList, Thinking, XMarkdown } from 'vue-element-plus-x'
import type { BubbleListInstance } from 'vue-element-plus-x/types/components/BubbleList/types'
import type { ThinkingStatus } from 'vue-element-plus-x/types/components/Thinking/types'
import type {
  CodeBlockHeaderExpose,
} from 'vue-element-plus-x/types/components/XMarkdownCore/components/CodeBlock/shiki-header'
import AudioMessage from './message-components/AudioMessage.vue'
import ImageMessage from './message-components/ImageMessage.vue'
import FileMessage from './message-components/FileMessage.vue'
import VoicePlayButton from './VoicePlayButton.vue'
import AnimationCard from './AnimationCard.vue'

defineOptions({
  name: 'ChatBubbleList',
})
const props = withDefaults(defineProps<Props>(), {
  messages: () => [],
  showAudio: false,
})

const emit = defineEmits<Emits>()

const selfCodeXSlot: Code<PERSON>lockHeaderExpose = {
  // 自定义渲染代码块的右侧控制按钮
  codeHeaderControl: () => h('div', ''),
}

interface Props {
  /** 消息列表 */
  messages?: Chat.MessageItem[]
  /** 是否加载中 */
  isLoading?: boolean
  /** 是否显示语音播放按钮 */
  showAudio?: boolean
}

interface Emits {
  loadMore: []
}

// 常量定义
const SCROLL_THRESHOLD = 200 // 距离顶部多少像素时触发加载
const DEBOUNCE_DELAY = 300 // 防抖延迟时间

// 响应式状态
const bubbleListRef = ref<BubbleListInstance | null>(null)
const isLoadingMore = ref(false)
const lastScrollTop = ref(0)

// 自定义代码渲染器
const selfCodeXRender = {
  // 渲染自定义iframe标识符 AnimationCard 是自己封装的Vue组件
  AnimationCard: (props: { raw: any }) => h(AnimationCard, { code: props.raw.content }),
}

// 动态组件映射
const messageComponents: Record<Chat.MessageContentType, any> = {
  text: null,
  audio: AudioMessage,
  image: ImageMessage,
  file: FileMessage,
}

// 计算属性
const hasMessages = computed(() => props.messages.length > 0)

// 获取消息组件
function getMessageComponent(contentType?: Chat.MessageContentType) {
  if (!contentType || contentType === 'text') {
    return null // 文本消息使用默认渲染
  }
  return messageComponents[contentType] || null
}

// 判断是否为AI或系统角色
function isAiOrSystemRole(role: Chat.MessageRole): boolean {
  return ['ai', 'system'].includes(role)
}

// 判断是否为用户角色
function isUserRole(role: Chat.MessageRole): boolean {
  return role === 'user'
}

// 判断是否为文本内容类型
function isTextContent(contentType?: Chat.MessageContentType): boolean {
  return !contentType || contentType === 'text'
}

// 处理思考链变化
function handleThinkingChange(_value: { value: boolean, status: ThinkingStatus }) {
  // 处理思考链状态变化
  // 可以在这里添加具体的业务逻辑
}

// 滚动控制方法
function scrollToBottom(behavior?: ScrollBehavior) {
  nextTick(() => {
    bubbleListRef.value?.scrollToBottom?.(behavior)
  })
}

function scrollToBubble(index: number, behavior?: ScrollBehavior) {
  nextTick(() => {
    bubbleListRef.value?.scrollToBubble?.(index, behavior)
  })
}

// 防抖的加载更多函数
const debouncedLoadMore = useDebounceFn(() => {
  if (!isLoadingMore.value && hasMessages.value) {
    isLoadingMore.value = true
    emit('loadMore')
  }
}, DEBOUNCE_DELAY)

// 完成加载更多后调用，重置加载状态
function finishLoadMore() {
  isLoadingMore.value = false
}

// 滚动事件处理
function handleScroll(event: Event) {
  const target = event.target as HTMLElement
  if (!target) {
    return
  }

  const { scrollTop } = target

  // 判断滚动方向：向上滚动
  if (scrollTop < lastScrollTop.value) {
    // 当滚动到接近顶部时，使用防抖触发加载更多
    if (scrollTop <= SCROLL_THRESHOLD) {
      debouncedLoadMore()
    }
  }

  // 更新上次滚动位置
  lastScrollTop.value = scrollTop
}

// 暴露方法给父组件
defineExpose({
  bubbleListRef,
  scrollToBottom,
  scrollToBubble,
  finishLoadMore,
})
</script>

<template>
  <BubbleList
    ref="bubbleListRef"
    :list="messages"
    @scroll="handleScroll"
  >
    <template #avatar="{ item }">
      <div
        :style="{
          width: item.avatarSize || '40px',
          height: item.avatarSize || '40px',
          borderRadius: '50%',
          overflow: 'hidden',
          flexShrink: 0,
          border: '1px solid #e0e0e0',
        }"
      >
        <img
          :src="item.avatar"
          :style="{
            width: '100%',
            height: '100%',
            objectFit: item.avatarFit || 'cover',
          }"
        >
      </div>
    </template>

    <template #header="{ item }">
      <!-- 姓名 -->
      <p class="mb-4px text-14px">
        {{ item.name }}
      </p>
      <Thinking
        v-if="item.reasoning_content"
        v-model="item.thinlCollapse"
        :content="item.reasoning_content"
        :status="item.thinkingStatus"
        class="mb-8px"
        @change="handleThinkingChange"
      />
    </template>

    <template #content="{ item }">
      <!-- 文本消息 -->
      <template v-if="isTextContent(item.contentType)">
        <!-- AI消息走 markdown -->
        <XMarkdown
          v-if="item.content && isAiOrSystemRole(item.role)"
          :markdown="item.content"
          :code-x-render="selfCodeXRender"
          class="leading-6"
          :code-x-slot="selfCodeXSlot"
          :themes="{ light: 'github-light', dark: 'github-dark' }"
          default-theme-mode="light"
        />
        <!-- 用户消息纯文本 -->
        <div v-if="item.content && isUserRole(item.role)" class="py-8px">
          {{ item.content }}
        </div>
      </template>

      <!-- 动态加载其他类型消息组件 -->
      <template v-if="!isTextContent(item.contentType) && isUserRole(item.role)">
        <component
          :is="getMessageComponent(item.contentType)"
          v-if="getMessageComponent(item.contentType)"
          :item="item"
        />
      </template>
    </template>

    <template #footer="{ item }">
      <!-- 语音播放按钮 当前是口语交际并且打印完成后才出现 -->
      <VoicePlayButton
        v-if="isAiOrSystemRole(item.role) && !isLoading && showAudio"
        :item="item"
      />
    </template>
  </BubbleList>
</template>
