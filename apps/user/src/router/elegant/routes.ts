import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|qrcode-login|reset-pwd)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'task',
    path: '/task',
    component: 'layout.base',
    meta: {
      title: '任务中心',
      icon: 'carbon:task'
    },
    children: [
      {
        name: 'task_chat',
        path: '/task/chat',
        component: 'view.task_chat',
        meta: {
          title: '聊天',
          hideInMenu: true,
          order: 2
        }
      },
      {
        name: 'task_home',
        path: '/task/home',
        component: 'view.task_home',
        meta: {
          title: '任务中心',
          order: 1,
          hideInMenu: true
        }
      }
    ]
  }
];
