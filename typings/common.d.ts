/** 公共类型命名空间 */
declare namespace CommonType {
  /** 策略模式 */
  interface StrategicPattern {
    /** 条件 */
    condition: boolean
    /** 如果条件为真，则调用 action 函数 */
    callback: () => void
  }

  /**
   * 选项类型
   *
   * @property value: 选项值
   * @property label: 选项标签
   */
  // eslint-disable-next-line ts/consistent-type-definitions
  type Option<K = string> = { value: K, label: string }

  type YesOrNo = 'Y' | 'N'

  /** 为所有属性添加 null */
  type RecordNullable<T> = {
    [K in keyof T]?: T[K] | null;
  }
}
