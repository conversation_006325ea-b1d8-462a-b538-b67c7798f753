{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@sa/tsconfig/app.json", "compilerOptions": {"baseUrl": ".", "paths": {"@sa/components/*": ["./base/components/*"], "@sa/store": ["./base/store"], "@sa/store/*": ["./base/store/*"], "@sa/enum": ["./base/enum"], "@sa/enum/*": ["./base/enum/*"], "@sa/constants": ["./base/constants/"], "@sa/constants/*": ["./base/constants/*"], "@sa/styles": ["./base/styles"], "@sa/styles/*": ["./base/styles/*"], "@sa/service": ["./base/service"], "@sa/service/*": ["./base/service/*"], "@sa/theme": ["./base/theme"], "@sa/theme/*": ["./base/theme/*"], "@sa/directives": ["./directives"], "@sa/directives/*": ["./directives/*"]}}, "include": ["./**/*", "../typings"], "exclude": ["node_modules"]}