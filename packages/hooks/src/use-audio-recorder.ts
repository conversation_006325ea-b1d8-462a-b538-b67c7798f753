import { ref } from 'vue'

/**
 * 录音数据接口
 */
export interface AudioRecordingData {
  isRecording: boolean
  recognizedText: string
  finalText: string
  audioBlob?: Blob
  /** 录音时长（秒） */
  duration?: number
  mimeType?: string
}

/**
 * 录音配置选项
 */
export interface AudioRecorderOptions {
  /** 音频采样率 */
  sampleRate?: number
  /** 是否启用回声消除 */
  echoCancellation?: boolean
  /** 是否启用噪声抑制 */
  noiseSuppression?: boolean
  /** 数据收集间隔（毫秒） */
  dataInterval?: number
  /** 首选的音频格式 */
  preferredMimeType?: string
  /** 最大录音时长（秒），默认60秒 */
  maxDuration?: number
  /** 显示倒计时的阈值（秒），默认10秒 */
  countdownThreshold?: number
  /** 录音达到最大时长时的回调函数 */
  onMaxDurationReached?: () => void
}

/**
 * 音频录制 Hook
 * 提供完整的音频录制功能，包括开始录音、停止录音、获取音频数据等
 */
export default function useAudioRecorder(options: AudioRecorderOptions = {}) {
  const {
    sampleRate = 44100,
    echoCancellation = true,
    noiseSuppression = true,
    dataInterval = 100,
    preferredMimeType = 'audio/webm;codecs=opus',
    maxDuration = 60,
    countdownThreshold = 10,
    onMaxDurationReached,
  } = options

  // 响应式状态
  const isRecording = ref(false)
  const audioBlob = ref<Blob | null>(null)
  const recordingData = ref<AudioRecordingData | null>(null)
  const error = ref<string | null>(null)
  const recordingTime = ref(0) // 当前录音时长（秒）
  const remainingTime = ref(0) // 剩余录音时长（秒）
  const showCountdown = ref(false) // 是否显示倒计时

  // 录音相关变量
  let mediaRecorder: MediaRecorder | null = null
  let audioChunks: Blob[] = []
  let mediaStream: MediaStream | null = null
  let startTime: number = 0
  let recordingTimer: NodeJS.Timeout | null = null
  let maxDurationTimer: NodeJS.Timeout | null = null

  /**
   * 启动录音计时器
   */
  function startRecordingTimer(): void {
    recordingTimer = setInterval(() => {
      if (!isRecording.value) {
        clearRecordingTimer()
        return
      }

      recordingTime.value += 1
      remainingTime.value = Math.max(0, maxDuration - recordingTime.value)

      // 当剩余时间小于等于阈值时显示倒计时
      if (remainingTime.value <= countdownThreshold && remainingTime.value > 0) {
        showCountdown.value = true
      }
    }, 1000)
  }

  /**
   * 清理录音计时器
   */
  function clearRecordingTimer(): void {
    if (recordingTimer) {
      clearInterval(recordingTimer)
      recordingTimer = null
    }
  }

  /**
   * 清理最大时长计时器
   */
  function clearMaxDurationTimer(): void {
    if (maxDurationTimer) {
      clearTimeout(maxDurationTimer)
      maxDurationTimer = null
    }
  }

  /**
   * 格式化文件大小
   */
  function formatFileSize(bytes: number): string {
    if (bytes === 0)
      return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
  }

  /**
   * 获取支持的音频格式
   */
  function getSupportedMimeType(): string {
    const types = [
      preferredMimeType,
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg',
    ]

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type
      }
    }

    return 'audio/webm' // 默认格式
  }

  /**
   * 开始录音
   */
  async function startRecording(): Promise<void> {
    try {
      // 重置状态
      error.value = null
      audioBlob.value = null
      recordingData.value = null
      audioChunks = []

      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('您的浏览器不支持录音功能')
      }

      // 请求麦克风权限
      mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation,
          noiseSuppression,
          sampleRate,
        },
      })

      // 获取支持的音频格式
      const mimeType = getSupportedMimeType()

      // 创建 MediaRecorder 实例
      mediaRecorder = new MediaRecorder(mediaStream, { mimeType })

      // 监听数据可用事件
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data)
        }
      }

      // 监听录音停止事件
      mediaRecorder.onstop = () => {
        handleRecordingComplete()
      }

      // 监听错误事件
      mediaRecorder.onerror = (event) => {
        console.error('录音错误:', event)
        error.value = '录音过程中发生错误'
        stopRecording()
      }

      // 开始录音
      startTime = Date.now()
      mediaRecorder.start(dataInterval)
      isRecording.value = true

      // 重置计时器状态
      recordingTime.value = 0
      remainingTime.value = maxDuration
      showCountdown.value = false

      // 启动录音计时器
      startRecordingTimer()

      // 设置最大录音时长限制
      maxDurationTimer = setTimeout(() => {
        if (isRecording.value) {
          console.log('录音达到最大时长，自动停止')
          // 调用回调函数通知外部组件
          if (onMaxDurationReached) {
            onMaxDurationReached()
          }
          stopRecording()
        }
      }, maxDuration * 1000)

      // 更新录音数据
      recordingData.value = {
        isRecording: true,
        recognizedText: '',
        finalText: '',
        mimeType,
      }

      console.log('录音已开始，格式:', mimeType)
    }
    catch (err) {
      const errorMessage = err instanceof Error ? err.message : '录音启动失败'
      error.value = errorMessage
      console.error('开始录音失败:', err)

      // 清理资源
      cleanup()
      throw new Error(errorMessage)
    }
  }

  /**
   * 停止录音
   */
  async function stopRecording(): Promise<void> {
    try {
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop()
      }

      // 停止媒体流
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop())
        mediaStream = null
      }

      // 清理计时器
      clearRecordingTimer()
      clearMaxDurationTimer()

      // 重置计时器状态
      showCountdown.value = false
    }
    catch (err) {
      console.error('停止录音失败:', err)
      error.value = '停止录音失败'
    }
    finally {
      isRecording.value = false
    }
  }

  /**
   * 处理录音完成
   */
  function handleRecordingComplete(): void {
    const durationMs = startTime ? Date.now() - startTime : 0
    const duration = Math.round(durationMs / 1000) // 转换为秒并四舍五入

    if (audioChunks.length > 0) {
      // 创建音频 Blob
      const mimeType = mediaRecorder?.mimeType || 'audio/webm'
      const blob = new Blob(audioChunks, { type: mimeType })
      audioBlob.value = blob

      // 更新录音数据
      recordingData.value = {
        isRecording: false,
        recognizedText: '',
        finalText: '',
        audioBlob: blob,
        duration,
        mimeType,
      }

      console.log('录音完成:', {
        audioSize: formatFileSize(blob.size),
        mimeType,
        duration: `${duration}秒`,
      })
    }
    else {
      error.value = '录音数据为空'
    }

    // 清理
    cleanup()
  }

  /**
   * 清理资源
   */
  function cleanup(): void {
    audioChunks = []
    mediaRecorder = null
    if (mediaStream) {
      mediaStream.getTracks().forEach(track => track.stop())
      mediaStream = null
    }
    // 清理计时器
    clearRecordingTimer()
    clearMaxDurationTimer()
    // 重置计时器状态
    recordingTime.value = 0
    remainingTime.value = 0
    showCountdown.value = false
  }

  /**
   * 获取当前录制的音频数据
   */
  function getCurrentAudioData() {
    if (!audioBlob.value) {
      return null
    }

    return {
      audioBlob: audioBlob.value,
      audioSize: audioBlob.value.size,
      audioSizeFormatted: formatFileSize(audioBlob.value.size),
      recordingData: recordingData.value,
      isRecording: isRecording.value,
    }
  }

  /**
   * 下载当前录制的音频
   */
  function downloadCurrentAudio(filename?: string): void {
    const audioData = getCurrentAudioData()

    if (!audioData) {
      console.warn('没有可下载的音频')
      return
    }

    const defaultFilename = `recording_${new Date().getTime()}.webm`
    const downloadFilename = filename || defaultFilename

    // 创建下载链接
    const url = URL.createObjectURL(audioData.audioBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = downloadFilename
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 释放 URL 对象
    setTimeout(() => URL.revokeObjectURL(url), 1000)
  }

  /**
   * 清空当前录音数据
   */
  function clearAudioData(): void {
    audioBlob.value = null
    recordingData.value = null
    error.value = null
  }

  /**
   * 播放音频
   */
  function playAudio(blob?: Blob): Promise<void> {
    return new Promise((resolve, reject) => {
      const audioToPlay = blob || audioBlob.value

      if (!audioToPlay) {
        reject(new Error('没有可播放的音频'))
        return
      }

      const audioUrl = URL.createObjectURL(audioToPlay)
      const audio = new Audio(audioUrl)

      audio.onended = () => {
        URL.revokeObjectURL(audioUrl)
        resolve()
      }

      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl)
        reject(new Error('音频播放失败'))
      }

      audio.play().catch(reject)
    })
  }

  return {
    // 状态
    isRecording,
    audioBlob,
    recordingData,
    error,
    recordingTime,
    remainingTime,
    showCountdown,

    // 方法
    startRecording,
    stopRecording,
    getCurrentAudioData,
    downloadCurrentAudio,
    clearAudioData,
    playAudio,
    formatFileSize,
  }
}
