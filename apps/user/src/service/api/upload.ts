import { request } from '../request'
/**
 *
 * 后端 API 模块: "upload"
 */
/** 上传结果 */
interface UploadResult {
  /** 文件ID */
  fileId: string
  /** 文件名称 */
  fileName: string
  /** 文件大小 */
  fileSize: number
  /** 文件类型 */
  fileType: string
  /** 文件URL */
  fileUrl: string
  /** 缩略图URL（可选） */
  thumbUrl?: string
  /** 上传时间 */
  uploadTime: string
}
/**
 * 上传文件接口
 * @param file 要上传的文件
 * @param onProgress 上传进度回调函数
 * @returns 返回上传结果
 */
export function uploadFile(file: File, onProgress?: (percent: number) => void) {
  // 创建FormData对象
  const formData = new FormData()
  formData.append('file', file)

  return request<UploadResult>({
    url: '/upload/file',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    // 上传进度回调
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total && onProgress) {
        // 计算上传进度百分比
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percent)
      }
    },
  })
}

/**
 * 批量上传文件接口
 * @param files 要上传的文件数组
 * @param onProgress 上传进度回调函数
 * @returns 返回上传结果数组
 */
export function uploadMultipleFiles(files: File[], onProgress?: (fileIndex: number, percent: number) => void) {
  // 创建上传任务数组
  const uploadTasks = files.map((file, index) => {
    return uploadFile(file, (percent) => {
      if (onProgress) {
        onProgress(index, percent)
      }
    })
  })

  // 并行执行所有上传任务
  return Promise.all(uploadTasks)
}
