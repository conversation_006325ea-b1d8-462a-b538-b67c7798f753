<script setup lang="ts" name="PreviewCode">
const modal = defineModel<{
  html: string
  show: boolean
}>('modal')

const isUrl = computed(() => {
  return ['http', 'https'].some(item => modal.value!.html.startsWith(item))
})
</script>

<template>
  <NDrawer
    v-model:show="modal!.show"
    placement="right"
    title="预览"
    width="80vw"
  >
    <template v-if="modal?.show && modal.html">
      <iframe v-if="isUrl" class="h-full w-full" :src="modal.html" />
      <iframe v-else class="h-full w-full" :srcdoc="modal.html" />
    </template>
  </NDrawer>
</template>

<style scoped></style>
