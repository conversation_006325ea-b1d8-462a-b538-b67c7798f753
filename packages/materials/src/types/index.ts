/** 头部配置 */
interface AdminLayoutHeaderConfig {
  /**
   * 是否显示头部
   * @default true
   */
  headerVisible?: boolean
  /**
   * 头部自定义类名
   * @default ''
   */
  headerClass?: string
  /**
   * 头部高度(px)
   * @default 56
   */
  headerHeight?: number
}

/** 标签页配置 */
interface AdminLayoutTabConfig {
  /**
   * 是否显示标签页
   * @default true
   */
  tabVisible?: boolean
  /**
   * 标签页自定义类名
   * @default ''
   */
  tabClass?: string
  /**
   * 标签页高度(px)
   * @default 48
   */
  tabHeight?: number
}

/** 侧边栏配置 */
interface AdminLayoutSiderConfig {
  /**
   * 是否显示侧边栏
   * @default true
   */
  siderVisible?: boolean
  /**
   * 侧边栏自定义类名
   * @default ''
   */
  siderClass?: string
  /**
   * 移动端侧边栏类名
   * @default ''
   */
  mobileSiderClass?: string
  /**
   * 侧边栏是否折叠
   * @default false
   */
  siderCollapse?: boolean
  /**
   * 侧边栏展开宽度(px)
   * @default 220
   */
  siderWidth?: number
  /**
   * 侧边栏折叠宽度(px)
   * @default 64
   */
  siderCollapsedWidth?: number
}

/** 内容区域配置 */
export interface AdminLayoutContentConfig {
  /**
   * 内容区域类名
   * @default ''
   */
  contentClass?: string
  /**
   * 是否全屏显示内容
   * 为true时会隐藏其他元素
   */
  fullContent?: boolean
}

/** 底部配置 */
export interface AdminLayoutFooterConfig {
  /**
   * 是否显示底部
   * @default true
   */
  footerVisible?: boolean
  /**
   * 底部是否固定
   * @default true
   */
  fixedFooter?: boolean
  /**
   * 底部类名
   * @default ''
   */
  footerClass?: string
  /**
   * 底部高度(px)
   * @default 48
   */
  footerHeight?: number
  /**
   * 底部是否在右侧
   * 垂直布局时底部显示在右侧
   */
  rightFooter?: boolean
}

/**
 * 布局模式
 * - horizontal: 水平布局
 * - vertical: 垂直布局
 */
export type LayoutMode = 'horizontal' | 'vertical'

/**
 * 内容溢出时的滚动模式
 * - wrapper: 布局容器滚动
 * - content: 内容区域滚动
 * @default 'wrapper'
 */
export type LayoutScrollMode = 'wrapper' | 'content'

/** 管理后台布局属性 */
export interface AdminLayoutProps
  extends AdminLayoutHeaderConfig,
  AdminLayoutTabConfig,
  AdminLayoutSiderConfig,
  AdminLayoutContentConfig,
  AdminLayoutFooterConfig {
  /**
   * 布局模式
   * @see LayoutMode
   */
  mode?: LayoutMode
  /** 是否为移动端布局 */
  isMobile?: boolean
  /**
   * 滚动模式
   * @see LayoutScrollMode
   */
  scrollMode?: LayoutScrollMode
  /**
   * 滚动元素ID
   * 用于获取对应的DOM元素并控制滚动
   * @default '__ADMIN_LAYOUT_SCROLL_EL_ID__'
   */
  scrollElId?: string
  /** 滚动元素类名 */
  scrollElClass?: string
  /** 滚动容器类名 */
  scrollWrapperClass?: string
  /**
   * 布局通用类名
   * 可用于配置过渡动画
   * @default 'transition-all-300'
   */
  commonClass?: string
  /**
   * 是否固定头部和标签页
   * @default true
   */
  fixedTop?: boolean
  /**
   * 布局最大z-index值
   * 头部、标签页、侧边栏和底部的z-index不会超过此值
   */
  maxZIndex?: number
}

/** 将驼峰命名转换为短横线命名 */
type Kebab<S extends string> = S extends Uncapitalize<S> ? S : `-${Uncapitalize<S>}`

type KebabCase<S extends string> = S extends `${infer Start}${infer End}`
  ? `${Uncapitalize<Start>}${KebabCase<Kebab<End>>}`
  : S

/** CSS变量前缀 */
type Prefix = '--soy-'

/** 布局CSS变量属性 */
export type LayoutCssVarsProps = Pick<
  AdminLayoutProps,
  'headerHeight' | 'tabHeight' | 'siderWidth' | 'siderCollapsedWidth' | 'footerHeight'
> & {
  headerZIndex?: number
  tabZIndex?: number
  siderZIndex?: number
  mobileSiderZIndex?: number
  footerZIndex?: number
}

/** 布局CSS变量类型 */
export type LayoutCssVars = {

  [K in keyof LayoutCssVarsProps as `${Prefix}${KebabCase<K>}`]: string | number
}

/**
 * 标签页模式
 * - button: 按钮样式
 * - chrome: 浏览器标签样式
 * @default chrome
 */
export type PageTabMode = 'button' | 'chrome'

/** 页面标签页属性 */
export interface PageTabProps {
  /** 是否为暗黑模式 */
  darkMode?: boolean
  /**
   * 标签页模式
   * @see PageTabMode
   */
  mode?: PageTabMode
  /**
   * 通用类名
   * 可用于配置过渡动画
   * @default 'transition-all-300'
   */
  commonClass?: string
  /** 按钮样式标签页类名 */
  buttonClass?: string
  /** 浏览器样式标签页类名 */
  chromeClass?: string
  /** 标签页是否激活 */
  active?: boolean
  /** 激活标签页颜色 */
  activeColor?: string
  /**
   * 标签页是否可关闭
   * 为true时显示关闭图标
   */
  closable?: boolean
}

/** 页面标签页CSS变量属性 */
export interface PageTabCssVarsProps {
  primaryColor: string
  primaryColor1: string
  primaryColor2: string
  primaryColorOpacity1: string
  primaryColorOpacity2: string
  primaryColorOpacity3: string
}

/** 页面标签页CSS变量类型 */
export type PageTabCssVars = {

  [K in keyof PageTabCssVarsProps as `${Prefix}${KebabCase<K>}`]: string | number
}
