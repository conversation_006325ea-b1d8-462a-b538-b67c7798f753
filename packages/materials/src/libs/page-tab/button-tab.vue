<script setup lang="ts">
import type { PageTabProps } from '../../types'
import style from './index.module.css'

defineOptions({
  name: 'ButtonTab',
})
// 定义组件props
defineProps<PageTabProps>()

defineSlots<Slots>()

type SlotFn = (props?: Record<string, unknown>) => any

interface Slots {
  /**
   * 默认插槽 - 标签页中间内容
   */
  default?: SlotFn
  /**
   * 前缀插槽 - 标签页左侧内容
   */
  prefix?: SlotFn
  /**
   * 后缀插槽 - 标签页右侧内容
   */
  suffix?: SlotFn
}
</script>

<template>
  <!-- 按钮式标签页容器 -->
  <div
    class=":soy: relative inline-flex cursor-pointer items-center justify-center gap-12px whitespace-nowrap border-(1px solid) rounded-4px px-12px py-4px"
    :class="[
      style['button-tab'], // 基础样式
      { [style['button-tab_dark']]: darkMode }, // 暗黑模式样式
      { [style['button-tab_active']]: active }, // 激活状态样式
      { [style['button-tab_active_dark']]: active && darkMode }, // 暗黑模式下的激活状态
    ]"
  >
    <!-- 左侧内容插槽 -->
    <slot name="prefix" />
    <!-- 默认内容插槽 -->
    <slot />
    <!-- 右侧内容插槽 -->
    <slot name="suffix" />
  </div>
</template>

<style scoped></style>
