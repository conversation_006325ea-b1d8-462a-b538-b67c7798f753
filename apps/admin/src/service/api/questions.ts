import { appApiRequest, request } from '@/service/request'

/**
 * 获取智能体模型信息
 * @returns 返回智能体模型信息
 */
export function getAgentModelInfo() {
  return request<QuestionsApi.AgentModelInfoResponse[]>({
    url: 'AgentCommon/AgentCommon/GetAgentModelInfo',
    method: 'POST',
  })
}
/**
 * 获取难度接口
 * @param data 请求参数
 *   @property {number} grade - 年级
 *   @property {string} year - 学年
 * @returns 返回难度信息
 */
export function getDifficulty(data: { grade: number, year: string }) {
  return appApiRequest<QuestionsApi.GetDifficultyResponse[]>({
    url: 'Question/Question/GetDifficulty',
    method: 'GET',
    params: data,
  })
}

/**
 * 获取学习水平接口
 * @param data 请求参数
 *   @property {number} grade - 年级
 *   @property {string} year - 学年
 * @returns 返回学习等级信息
 */
export function getLearningLevel(data: { grade: number, year: string }) {
  return appApiRequest<QuestionsApi.GetLearningLevelResponse[]>({
    url: 'Question/Question/GetLearninglevel',
    method: 'GET',
    params: data,
  })
}

/**
 * 获取题目类型接口
 * @returns 返回题目类型信息
 */
export function getQuestionTypes() {
  return request<QuestionsApi.GetQuestionTypesResponse[]>({
    url: '/AgentIntelligentQuestion/AgentIntelligentQuestion/GetQuestionTypes',
    method: 'GET',
  })
}

/**
 * 获取章节列表接口
 * @returns 返回章节列表信息
 */
export function getChapterList(data: QuestionsApi.GetChapterListResponseRequest) {
  return appApiRequest<QuestionsApi.GetChapterListResponse[]>({
    url: '/Chapter/Chapter/GetChapterListByCondition',
    method: 'GET',
    params: data,
  })
}

/**
 * 题目重新生成接口
 * @param data 请求参数
 *   @property {string} questionId - 题目ID
 * @returns 返回题目信息
 */
export function featchRegenerateQuestion(data: QuestionsApi.RegenerateQuestionInputRequest) {
  return appApiRequest<QuestionsApi.GeneratedQuestion>({
    url: '/AgentIntelligentQuestion/AgentIntelligentQuestion/RegenerateQuestion',
    method: 'POST',
    data,
  })
}
/**
 * 保存单题到题库接口
 * @param data 请求参数
 *   @property {string} questionId - 题目ID
 * @returns 返回保存结果
 */
export function saveSingleQuestionToBank(data: QuestionsApi.SaveBatchQuestionsToBankInputRequest) {
  return appApiRequest<QuestionsApi.SaveBatchQuestionsToBankResponse>({
    url: '/AgentIntelligentQuestion/AgentIntelligentQuestion/SaveSingleQuestionToBank',
    method: 'POST',
    data,
  })
}
/**
 * 保存多题到题库接口
 * @param data 请求参数
 *   @property {string[]} questionIds - 题目ID数组
 * @returns 返回保存结果
 */
export function saveMultipleQuestionsToBank(data: QuestionsApi.SaveBatchQuestionsToBankInputRequest[]) {
  return appApiRequest<QuestionsApi.SaveBatchQuestionsToBankResponse>({
    url: '/AgentIntelligentQuestion/AgentIntelligentQuestion/SaveBatchQuestionsToBank',
    method: 'POST',
    data,
  })
}
