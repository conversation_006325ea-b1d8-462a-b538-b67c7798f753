<script setup lang="tsx">
import { N<PERSON><PERSON>on, NPopconfirm, NTag } from 'naive-ui'
import { useTable, useTableOperate } from '@sa/hooks'
import { enableStatusRecord, userGenderRecord } from '@sa/constants/business'
import UserOperateDrawer from './modules/user-operate-drawer.vue'
import UserSearch from './modules/user-search.vue'
import { useAppStore } from '@/store/modules/app'
import { fetchGetUserList } from '@/service/api'

const appStore = useAppStore()

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable({
  apiFn: fetchGetUserList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    status: null,
    userName: null,
    userGender: null,
    nickName: null,
    userPhone: null,
    userEmail: null,
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
    },
    {
      key: 'index',
      title: '序号',
      align: 'center',
      width: 64,
    },
    {
      key: 'userName',
      title: '用户名',
      align: 'center',
      minWidth: 100,
    },
    {
      key: 'userGender',
      title: '性别',
      align: 'center',
      width: 100,
      render: (row) => {
        if (row.userGender === null) {
          return null
        }

        const tagMap: Record<Api.SystemManage.UserGender, NaiveUI.ThemeColor> = {
          1: 'primary',
          2: 'error',
        }

        const label = userGenderRecord[row.userGender]

        return <NTag type={tagMap[row.userGender]}>{label}</NTag>
      },
    },
    {
      key: 'nickName',
      title: '昵称',
      align: 'center',
      minWidth: 100,
    },
    {
      key: 'userPhone',
      title: '手机号',
      align: 'center',
      width: 120,
    },
    {
      key: 'userEmail',
      title: '邮箱',
      align: 'center',
      minWidth: 200,
    },
    {
      key: 'status',
      title: '用户状态',
      align: 'center',
      width: 100,
      render: (row) => {
        if (row.status === null) {
          return null
        }

        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          2: 'warning',
        }

        const label = enableStatusRecord[row.status]

        return <NTag type={tagMap[row.status]}>{label}</NTag>
      },
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确认删除吗？',
              trigger: () => (
                <NButton type="error" ghost size="small">
                  删除
                </NButton>
              ),
            }}
          </NPopconfirm>
        </div>
      ),
    },
  ],
})

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted,
  // closeDrawer
} = useTableOperate(data, getData)

async function handleBatchDelete() {
  // request
  console.log(checkedRowKeys.value)

  onBatchDeleted()
}

function handleDelete(id: number) {
  // request
  console.log(id)

  onDeleted()
}

function edit(id: number) {
  handleEdit(id)
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="用户列表" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <UserOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
