import { ref } from 'vue'
import type { Ref } from 'vue'
import { createFlatRequest } from '@sa/axios'
import type {
  AxiosError,
  CreateAxiosDefaults,
  CustomAxiosRequestConfig,
  MappedType,
  RequestOption,
  ResponseType,
} from '@sa/axios'
import useLoading from './use-loading'

/**
 * 请求成功响应数据结构
 * @template T 数据类型
 */
export interface HookRequestInstanceResponseSuccessData<T = any> {
  data: Ref<T> // 响应数据
  error: Ref<null> // 错误对象(成功时为null)
}

/**
 * 请求失败响应数据结构
 * @template ResponseData 响应数据类型
 */
export interface HookRequestInstanceResponseFailData<ResponseData = any> {
  data: Ref<null> // 数据(失败时为null)
  error: Ref<AxiosError<ResponseData>> // 错误对象
}

/**
 * 请求响应数据联合类型
 * @template T 数据类型
 * @template ResponseData 响应数据类型
 */
export type HookRequestInstanceResponseData<T = any, ResponseData = any> = {
  loading: Ref<boolean> // 加载状态
} & (HookRequestInstanceResponseSuccessData<T> | HookRequestInstanceResponseFailData<ResponseData>)

/**
 * 钩子请求实例接口
 * @template ResponseData 响应数据类型
 */
export interface HookRequestInstance<ResponseData = any> {
  <T = any, R extends ResponseType = 'json'>(
    config: CustomAxiosRequestConfig
  ): HookRequestInstanceResponseData<MappedType<R, T>, ResponseData>
  cancelRequest: (requestId: string) => void // 取消单个请求
  cancelAllRequest: () => void // 取消所有请求
}

/**
 * 创建钩子请求实例
 * @template ResponseData 响应数据类型
 * @param axiosConfig axios配置
 * @param options 请求选项
 * @returns 返回钩子请求实例
 */
export default function createHookRequest<ResponseData = any>(
  axiosConfig?: CreateAxiosDefaults,
  options?: Partial<RequestOption<ResponseData>>,
) {
  // 创建扁平化请求实例
  const request = createFlatRequest<ResponseData>(axiosConfig, options)

  const hookRequest: HookRequestInstance<ResponseData> = function hookRequest<T = any, R extends ResponseType = 'json'>(
    config: CustomAxiosRequestConfig,
  ) {
    // 使用loading状态
    const { loading, startLoading, endLoading } = useLoading()

    // 响应数据ref
    const data = ref<MappedType<R, T> | null>(null) as Ref<MappedType<R, T>>
    // 错误ref
    const error = ref<AxiosError<ResponseData> | null>(null) as Ref<AxiosError<ResponseData> | null>

    startLoading()

    // 发起请求
    request(config).then((res) => {
      if (res.data) {
        data.value = res.data
      }
      else {
        error.value = res.error
      }

      endLoading()
    })

    return {
      loading,
      data,
      error,
    }
  } as HookRequestInstance<ResponseData>

  // 挂载取消请求方法
  hookRequest.cancelRequest = request.cancelRequest
  hookRequest.cancelAllRequest = request.cancelAllRequest

  return hookRequest
}
