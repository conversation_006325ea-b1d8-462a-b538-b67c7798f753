import type { AxiosHeaderValue, AxiosResponse, InternalAxiosRequestConfig } from 'axios'

/**
 * 获取请求的Content-Type头信息
 * @param config axios请求配置对象
 * @returns 返回Content-Type值，默认为'application/json'
 */
export function getContentType(config: InternalAxiosRequestConfig) {
  // 从请求头中获取Content-Type，若不存在则使用默认值
  const contentType: AxiosHeaderValue = config.headers?.['Content-Type'] || 'application/json'

  return contentType
}
/**
 * 检查HTTP状态码是否表示成功
 * @param status HTTP状态码
 * @returns 如果是成功状态码(200-299或304)返回true，否则返回false
 */
export function isHttpSuccess(status: number) {
  // 检查状态码是否在200-299范围内
  const isSuccessCode = status >= 200 && status < 300
  // 304(Not Modified)也视为成功状态
  return isSuccessCode || status === 304
}

/**
 * 检查响应是否为JSON格式
 * @param response axios响应对象
 * @returns 如果响应类型是'json'或未定义(默认json)则返回true，否则返回false
 */
export function isResponseJson(response: AxiosResponse) {
  // 从响应配置中获取responseType
  const { responseType } = response.config

  // 判断是否为json类型或未定义(未定义时默认为json)
  return responseType === 'json' || responseType === undefined
}
