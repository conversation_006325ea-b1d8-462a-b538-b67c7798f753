import type {
  LocationQueryRaw,
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteLocationRaw,
  Router,
} from 'vue-router'
import type { RouteKey, RoutePath } from '@elegant-router/types'
import { localStg } from '@sa/utils'
import { useAuthStore } from '@/store/modules/auth'
import { useRouteStore } from '@/store/modules/route'
import { getRouteName } from '@/router/elegant/transform'

/**
 * create route guard
 *
 * @param router router instance
 */
/**
 * 创建路由守卫
 * @param router Vue Router 实例
 * @description 配置路由导航前的权限验证逻辑
 * 1. 初始化路由并处理可能的重定向
 * 2. 检查用户登录状态和权限
 * 3. 根据验证结果决定是否允许访问或重定向
 */
export function createRouteGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    // 初始化路由并获取可能的跳转位置
    const location = await initRoute(to)

    // 如果需要跳转则执行跳转
    if (location) {
      next(location)
      return
    }

    // 获取权限存储实例
    const authStore = useAuthStore()

    // 定义关键路由名称
    const rootRoute: RouteKey = 'root'
    const loginRoute: RouteKey = 'login'
    const noAuthorizationRoute: RouteKey = '403'

    // 检查登录状态
    const isLogin = Boolean(localStg.get('token'))
    // 检查当前路由是否需要登录
    const needLogin = !to.meta.constant
    // 获取路由要求的角色
    const routeRoles = to.meta.roles || []

    // 检查用户是否具有访问权限
    const hasRole = authStore.userInfo.roles.some(role => routeRoles.includes(role))
    const hasAuth = authStore.isStaticSuper || !routeRoles.length || hasRole

    // 已登录时访问登录页则跳转到首页
    if (to.name === loginRoute && isLogin) {
      next({ name: rootRoute })
      return
    }

    // 不需要登录的路由直接放行
    if (!needLogin) {
      handleRouteSwitch(to, from, next)
      return
    }

    // 需要登录但未登录则跳转到登录页
    if (!isLogin) {
      next({ name: loginRoute, query: { redirect: to.fullPath } })
      return
    }

    // 已登录但无权限则跳转到403页
    if (!hasAuth) {
      next({ name: noAuthorizationRoute })
      return
    }

    // 正常情况下的路由切换
    handleRouteSwitch(to, from, next)
  })
}

/**
 * initialize route
 *
 * @param to to route
 */
/**
 * 初始化路由
 * @param to 目标路由对象
 * @returns 返回需要重定向的路由位置或null
 * @description 处理路由初始化逻辑，包括：
 * 1. 初始化常量路由
 * 2. 处理未登录状态下的路由访问
 * 3. 初始化认证路由
 * 4. 处理404和403情况
 */
async function initRoute(to: RouteLocationNormalized): Promise<RouteLocationRaw | null> {
  const routeStore = useRouteStore()

  // 定义404路由名称并检查当前是否是404路由
  const notFoundRoute: RouteKey = 'not-found'
  const isNotFoundRoute = to.name === notFoundRoute

  // 如果常量路由未初始化，则初始化常量路由
  if (!routeStore.isInitConstantRoute) {
    await routeStore.initConstantRoute()

    // 由于常量路由未初始化导致被404路由捕获，初始化后重定向回原路由
    const path = to.fullPath
    const location: RouteLocationRaw = {
      path,
      replace: true, // 使用替换而非push方式
      query: to.query,
      hash: to.hash,
    }

    return location
  }

  // 检查用户登录状态
  const isLogin = Boolean(localStg.get('token'))

  // 未登录情况处理
  if (!isLogin) {
    // 允许访问常量路由(非404路由)
    if (to.meta.constant && !isNotFoundRoute) {
      routeStore.onRouteSwitchWhenNotLoggedIn()
      return null
    }

    // 未登录且非常量路由，跳转到登录页
    const loginRoute: RouteKey = 'login'
    const query = getRouteQueryOfLoginRoute(to, routeStore.routeHome)

    const location: RouteLocationRaw = {
      name: loginRoute,
      query, // 携带重定向参数
    }

    return location
  }

  // 已登录但认证路由未初始化
  if (!routeStore.isInitAuthRoute) {
    // 初始化认证路由
    await routeStore.initAuthRoute()

    // 处理因认证路由未初始化导致的404情况
    if (isNotFoundRoute) {
      const rootRoute: RouteKey = 'root'
      // 如果是从根路由重定向过来的，则跳转到首页，否则保持原路径
      const path = to.redirectedFrom?.name === rootRoute ? '/' : to.fullPath

      const location: RouteLocationRaw = {
        path,
        replace: true,
        query: to.query,
        hash: to.hash,
      }

      return location
    }
  }

  // 已登录状态下的路由切换处理
  routeStore.onRouteSwitchWhenLoggedIn()

  // 非404路由直接放行
  if (!isNotFoundRoute) {
    return null
  }

  // 检查404路由是否实际存在(可能是权限不足)
  const exist = await routeStore.getIsAuthRouteExist(to.path as RoutePath)
  const noPermissionRoute: RouteKey = '403'

  // 如果路由存在但无权限，跳转到403页
  if (exist) {
    const location: RouteLocationRaw = {
      name: noPermissionRoute,
    }

    return location
  }

  // 其他情况(真正的404)返回null
  return null
}

/**
 * 处理路由切换逻辑
 * @param to 目标路由对象
 * @param from 来源路由对象
 * @param next 路由跳转函数
 * @description 处理特殊路由切换情况：
 * 1. 对于带有href属性的路由，在新窗口打开链接
 * 2. 其他情况正常跳转
 */
function handleRouteSwitch(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
  // 检查路由是否包含href元数据
  if (to.meta.href) {
    // 在新窗口打开链接
    window.open(to.meta.href, '_blank')

    // 保持当前路由不变
    next({
      path: from.fullPath,
      replace: true, // 使用替换而非push方式
      query: from.query,
      hash: to.hash,
    })

    return
  }

  // 普通路由正常跳转
  next()
}

/**
 * 获取登录页的路由查询参数
 * @param to 目标路由对象
 * @param routeHome 首页路由名称
 * @returns 返回登录页的查询参数对象
 * @description 处理登录页的重定向逻辑：
 * 1. 解析当前路由路径和查询参数
 * 2. 判断是否是跳转到首页
 * 3. 生成合适的重定向参数
 */
function getRouteQueryOfLoginRoute(to: RouteLocationNormalized, routeHome: RouteKey) {
  // 定义登录路由名称
  const loginRoute: RouteKey = 'login'
  // 获取完整路径作为重定向目标
  const redirect = to.fullPath
  // 分割路径和查询参数
  const [redirectPath, redirectQuery] = redirect.split('?')
  // 获取路由名称
  const redirectName = getRouteName(redirectPath as RoutePath)

  // 判断是否是跳转到首页
  const isRedirectHome = routeHome === redirectName

  // 生成查询参数：非登录页且非首页跳转时才添加redirect参数
  const query: LocationQueryRaw = to.name !== loginRoute && !isRedirectHome ? { redirect } : {}

  // 如果是跳转到首页且有查询参数，则特殊处理
  if (isRedirectHome && redirectQuery) {
    query.redirect = `/?${redirectQuery}`
  }

  return query
}
