<script setup lang="ts">
import { useVoicePlayerProvider } from '@sa/hooks'
import { nanoid } from '@sa/utils'
import ChatBubbleList from './BubbleList.vue'
import ChatSender from './ChatSender.vue'

interface Props {
  /** 是否显示操作列表 */
  showActionList?: boolean
  /** 输入框占位符 */
  placeholder?: string
  /** 是否显示前缀功能 */
  showPrefix?: boolean
  /** 是否加载中 */
  isLoading?: boolean
  /** 是否显示发送输入框 */
  showSender?: boolean
  /** 是否显示语音播放按钮 */
  showAudio?: boolean
  /** 聊天用户信息 */
  chatUserInfo?: {
    userName: string
    userAvatar: string
  }
}

interface Emits {
  startSse: [data: {
    inputContent: string
    uploadedFiles: any[]
    audioData?: any
  }]
  cancelSse: []
  loadMore: []
}

defineOptions({
  name: 'ChatContainer',
})

const props = withDefaults(defineProps<Props>(), {
  showActionList: false,
  showPrefix: true,
  isLoading: false,
  placeholder: '请输入消息或点击录音...',
  showSender: true,
  chatUserInfo: () => ({
    userName: '',
    userAvatar: '',
  }),
})

const emit = defineEmits<Emits>()

const initialMessages = defineModel('initialMessages', {
  type: Array as PropType<Chat.MessageItem[]>,
  default: () => [],
})

// 组件引用
const bubbleListRef = ref<InstanceType<typeof ChatBubbleList>>()
const senderRef = ref<InstanceType<typeof ChatSender>>()

// 设置语音播放上下文
useVoicePlayerProvider({
  callbacks: {
    onPlayError: (error) => {
      window.$message?.error(error.message || '语音播放失败')
    },
  },
})

// 处理SSE开始事件
function handleStartSse(data: {
  inputContent: string
  uploadedFiles: any[]
  audioData?: {
    audioUrl: string
    duration: number
    audioSizeFormatted: string
    mimeType: string
    recordingData?: any
  }
}) {
  if (!props.chatUserInfo.userName) {
    return
  }

  const messageKey = nanoid()
  const { userAvatar = '', userName = '' } = props.chatUserInfo

  // 创建基础消息对象
  const createBaseMessage = (contentType: Chat.MessageContentType) => ({
    key: messageKey,
    role: 'user' as const,
    contentType,
    avatar: userAvatar,
    placement: 'end' as const,
    name: userName,
  })

  // 添加文本消息
  if (data.inputContent.trim()) {
    addMessage({
      ...createBaseMessage('text'),
      content: data.inputContent,
    })
  }

  // 添加音频消息
  if (data.audioData) {
    addMessage({
      ...createBaseMessage('audio'),
      audioData: {
        audioUrl: data.audioData.audioUrl,
        duration: data.audioData.duration || 0,
        audioSizeFormatted: data.audioData.audioSizeFormatted || '0 KB',
        mimeType: data.audioData.mimeType || 'audio/webm',
        recordingData: data.audioData.recordingData,
      },
    })
  }

  // 处理上传的文件
  if (data.uploadedFiles?.length) {
    const imageFiles = data.uploadedFiles
      .filter((item: any) => item.file && item.status === 'done' && item.file.type.startsWith('image/'))
      .map((item: any) => {
        const file = item.file as File
        const imageUrl = URL.createObjectURL(file)
        return {
          imageFile: file,
          imageUrl,
          thumbUrl: imageUrl,
          width: item.width,
          height: item.height,
          imageSize: file.size,
          imageSizeFormatted: formatFileSize(file.size),
          mimeType: file.type,
          fileName: file.name,
          previewable: true,
        }
      })

    // 添加图片消息
    if (imageFiles.length > 0) {
      addMessage({
        ...createBaseMessage('image'),
        key: messageKey + 2,
        images: imageFiles,
      })
    }
  }

  // 发射事件给父组件
  emit('startSse', data)
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0)
    return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

// 添加消息并滚动到底部
function addMessage(message: Chat.MessageItem) {
  initialMessages.value?.push(message)
  nextTick(scrollToBottom)
}

// 滚动控制方法
function scrollToBottom(behavior?: ScrollBehavior) {
  bubbleListRef.value?.scrollToBottom(behavior)
}

// 加载更多完成
function finishLoadMore() {
  bubbleListRef.value?.finishLoadMore()
}

// 滚动到指定索引的气泡
function scrollToBubble(index: number, behavior?: ScrollBehavior) {
  bubbleListRef.value?.scrollToBubble(index, behavior)
}

// 暴露方法给父组件
defineExpose({
  bubbleListRef,
  senderRef,
  scrollToBottom,
  scrollToBubble,
  finishLoadMore,
})
</script>

<template>
  <div class="relative h-full w-full flex flex-col items-center">
    <div class="h-full w-full flex flex-col justify-between">
      <ChatBubbleList
        ref="bubbleListRef"
        :is-loading="isLoading"
        :show-audio="showAudio"
        :messages="initialMessages"
        class="chat-bubble-list h-full"
        @load-more="emit('loadMore')"
      />

      <ChatSender
        v-if="showSender"
        ref="senderRef"
        :show-action-list="showActionList"
        :placeholder="placeholder"
        :show-prefix="showPrefix"
        :is-loading="isLoading"
        class="mb-22px w-full"
        @start-sse="handleStartSse"
        @cancel-sse="emit('cancelSse')"
      />
    </div>
  </div>
</template>

<style scoped>
:deep(.el-bubble-list) {
  max-height: 100% !important;
}
:deep(.el-bubble-content-wrapper){
  margin-bottom: 12px;
}
</style>
