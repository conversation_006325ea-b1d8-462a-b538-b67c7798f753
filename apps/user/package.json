{"name": "@apps/user", "type": "module", "version": "1.0.0", "scripts": {"dev": "vite --mode dev", "test": "vite --mode test", "dev:prod": "vite --mode prod", "build": "vite build --mode prod", "build:test": "vite build --mode test", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint:fix": "eslint . --fix --quiet"}, "dependencies": {"@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@yw/fabric": "workspace:*"}, "turbo": {"pipeline": {"build": {"outputs": ["../../dist/user/**"]}}}}