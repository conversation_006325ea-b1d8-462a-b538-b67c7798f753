// 导入Vue相关API和类型
import { computed, effectScope, nextTick, onScopeDispose, ref, watch } from 'vue'
// 导入ECharts核心模块和图表类型
import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON>auge<PERSON>hart, <PERSON><PERSON>hart, <PERSON>ctorial<PERSON>ar<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON> } from 'echarts/charts'
import type {
  BarSeriesOption,
  GaugeSeriesOption,
  LineSeriesOption,
  PictorialBarSeriesOption,
  PieSeriesOption,
  RadarSeriesOption,
  ScatterSeriesOption,
} from 'echarts/charts'
// 导入ECharts组件和类型
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  TransformComponent,
} from 'echarts/components'
// 导入ECharts特性和渲染器
import type {
  DatasetComponentOption,
  GridComponentOption,
  LegendComponentOption,
  TitleComponentOption,
  ToolboxComponentOption,
  TooltipComponentOption,
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
// 导入工具函数
import { useElementSize } from '@vueuse/core'
import { useThemeStore } from '@sa/store/modules/theme'

// 定义ECharts配置项类型
export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | ScatterSeriesOption
  | PictorialBarSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | TitleComponentOption
  | LegendComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | ToolboxComponentOption
  | DatasetComponentOption
>

// 注册ECharts使用的模块
echarts.use([
  TitleComponent,
  LegendComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  PictorialBarChart,
  RadarChart,
  GaugeChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
])
// 定义图表Hook回调接口
type EchartsChartHooks = (chart: echarts.ECharts) => void | Promise<void>
interface ChartHooks {
  onRender?: EchartsChartHooks
  onUpdated?: EchartsChartHooks
  onDestroy?: EchartsChartHooks
}
/**
 * ECharts Hook
 * @param optionsFactory ECharts配置项工厂函数
 * @param hooks 图表生命周期钩子
 */
export function useEcharts<T extends ECOption>(optionsFactory: () => T, hooks: ChartHooks = {}) {
  // 创建effect作用域
  const scope = effectScope()

  // 获取主题状态
  const themeStore = useThemeStore()
  const darkMode = computed(() => themeStore.darkMode)

  // DOM引用和尺寸
  const domRef = ref<HTMLElement | null>(null)
  const initialSize = { width: 0, height: 0 }
  const { width, height } = useElementSize(domRef, initialSize)

  // 图表实例和配置
  let chart: echarts.ECharts | null = null
  const chartOptions: T = optionsFactory()

  // 解构生命周期钩子
  const {
    onRender = (instance) => {
      // 默认加载状态配置
      const textColor = darkMode.value ? 'rgb(224, 224, 224)' : 'rgb(31, 31, 31)'
      const maskColor = darkMode.value ? 'rgba(0, 0, 0, 0.4)' : 'rgba(255, 255, 255, 0.8)'

      instance.showLoading({
        color: themeStore.themeColor,
        textColor,
        fontSize: 14,
        maskColor,
      })
    },
    onUpdated = (instance) => {
      instance.hideLoading()
    },
    onDestroy,
  } = hooks

  /**
   * 判断是否可以渲染图表
   * @returns 返回布尔值，表示是否满足渲染条件：
   *          1. DOM元素已挂载
   *          2. 初始宽度大于0
   *          3. 初始高度大于0
   */
  function canRender() {
    return domRef.value && initialSize.width > 0 && initialSize.height > 0
  }

  /**
   * 检查图表是否已渲染
   * @returns 返回布尔值，表示图表是否已渲染：
   *          1. DOM元素已挂载
   *          2. 图表实例已创建
   */
  function isRendered() {
    return Boolean(domRef.value && chart)
  }

  /**
   * 更新图表配置选项
   * @param callback 配置更新回调函数，接收当前配置和配置工厂函数，返回新配置
   * @returns Promise<void>
   */
  async function updateOptions(callback: (opts: T, optsFactory: () => T) => ECOption = () => chartOptions) {
    // 检查图表是否已渲染
    if (!isRendered())
      return

    // 通过回调函数获取更新后的配置
    const updatedOpts = callback(chartOptions, optionsFactory)

    // 合并配置变更
    Object.assign(chartOptions, updatedOpts)

    // 如果图表已渲染，先清空内容
    if (isRendered()) {
      chart?.clear()
    }

    // 应用新配置（设置透明背景）
    chart?.setOption({ ...updatedOpts, backgroundColor: 'transparent' })

    // 触发更新后的钩子函数
    await onUpdated?.(chart!)
  }

  /**
   * 直接设置图表配置选项
   * @param options 新的图表配置对象
   * @description 直接应用新配置，不经过合并或回调处理
   */
  function setOptions(options: T) {
    chart?.setOption(options)
  }

  /**
   * 渲染图表
   * @description 初始化ECharts实例并应用配置
   * 1. 检查图表是否已渲染
   * 2. 根据当前主题模式设置图表主题
   * 3. 等待DOM更新后初始化图表
   * 4. 应用配置选项(设置透明背景)
   * 5. 触发渲染完成回调
   */
  async function render() {
    // 检查图表是否已渲染
    if (!isRendered()) {
      // 根据暗黑模式状态选择主题
      const chartTheme = darkMode.value ? 'dark' : 'light'

      // 等待DOM更新
      await nextTick()

      // 初始化ECharts实例
      chart = echarts.init(domRef.value, chartTheme)

      // 应用配置选项(设置透明背景)
      chart.setOption({ ...chartOptions, backgroundColor: 'transparent' })

      // 触发渲染完成回调
      await onRender?.(chart)
    }
  }

  /**
   * 调整图表尺寸
   * @description 当容器尺寸变化时，重新计算并设置图表尺寸
   */

  function resize() {
    chart?.resize()
  }

  /**
   * 销毁图表实例
   * @description 安全地销毁ECharts实例并执行清理操作：
   * 1. 检查图表实例是否存在
   * 2. 执行销毁前的回调函数
   * 3. 释放图表资源
   * 4. 重置实例引用
   */
  async function destroy() {
    // 检查图表实例是否存在
    if (!chart)
      return

    // 执行销毁前的回调函数
    await onDestroy?.(chart)
    // 释放图表资源
    chart?.dispose()
    // 重置实例引用
    chart = null
  }

  /**
   * 切换图表主题
   * @description 安全地切换图表主题模式：
   * 1. 销毁当前图表实例
   * 2. 重新渲染图表
   * 3. 触发更新后的回调
   */
  async function changeTheme() {
    // 销毁当前图表实例
    await destroy()
    // 使用新主题重新渲染图表
    await render()
    // 触发更新后的回调函数
    await onUpdated?.(chart!)
  }

  /**
   * 根据尺寸渲染图表
   * @param w 新宽度
   * @param h 新高度
   * @description 处理图表尺寸变化的完整流程：
   * 1. 更新初始尺寸记录
   * 2. 检查尺寸是否有效，无效则销毁图表
   * 3. 如果图表已渲染，调整其尺寸
   * 4. 渲染或重新渲染图表
   */
  async function renderChartBySize(w: number, h: number) {
    // 更新记录的初始尺寸
    initialSize.width = w
    initialSize.height = h

    // 检查尺寸是否异常（无效尺寸）
    if (!canRender()) {
      // 销毁现有图表实例
      await destroy()

      return
    }

    // 如果图表已存在，调整其尺寸
    if (isRendered()) {
      resize()
    }

    // 执行图表渲染（首次渲染或重新渲染）
    await render()
  }

  // 在effect作用域内设置响应式监听
  scope.run(() => {
    // 监听容器尺寸变化，自动调整图表大小
    watch([width, height], ([newWidth, newHeight]) => {
      renderChartBySize(newWidth, newHeight)
    })

    // 监听主题模式变化，自动切换图表主题
    watch(darkMode, () => {
      changeTheme()
    })
  })

  // 组件卸载时的清理逻辑
  onScopeDispose(() => {
    // 销毁图表实例
    destroy()
    // 停止effect作用域
    scope.stop()
  })

  return {
    domRef,
    updateOptions,
    setOptions,
  }
}
